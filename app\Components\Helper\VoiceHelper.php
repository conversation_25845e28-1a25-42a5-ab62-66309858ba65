<?php

namespace App\Components\Helper;

use Qcloud\Sms\TtsVoiceSender;

class VoiceHelper
{

    /**
     * 语音消息调用函数
     * @param $params
     * @param $mobile
     * @param $templateId
     * @return bool
     */
    public static function callPhone($params, $mobiles, $templateId = 0)
    {
        // 语音消息应用 SDK AppID
        $appid = config('qcloudsms.APPID');
        // 语音消息应用 App Key
        $appkey = config('qcloudsms.APPKEY');
        // 语音模板 ID，需要在语音消息控制台中申请
        if (empty($templateId)) {
            $templateId = config('qcloudsms.TEMPLATEID');  // NOTE: 这里的模板 ID`7839`只是示例，真实的模板 ID 需要在语音消息控制台中申请
        }

        foreach ($mobiles as $mobile) {
            try {
                $tvsender = new TtsVoiceSender($appid, $appkey);
                $result = $tvsender->send("86", "{$mobile}", $templateId, $params);
                $ret = json_decode($result, true);
                if ($ret["result"] == 0 && $ret["errmsg"] == "OK") {
                    \Log::info('语音info:' . $result);
                    return true;
                } else {
                    \Log::error('语音error：' . $ret["errmsg"]);
                    return false;
                }
            } catch (\Exception $e) {
                \Log::error('语音代码error：' . $e->getMessage());
                return false;
            }
        }
    }
}
