<?php

/**
 * 关联录屏和性能数据
 * @desc 关联录屏和性能数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/12
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\RecordLog;
use App\Model\StarRocks\ExceptionRecordPerf;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\PerfStarRocksService;
use App\Service\SdkStarRocksService;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RelationRecordPerfCommand extends Command
{
    /**
     * 统计时间间隔
     *
     * @var int
     */
    public const TIME = 5;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'relation:record:perf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '关联录屏和性能数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            //打印日志
            Log::info("执行关联录屏和性能数据脚本开始");
            // 获取当前时间的时间戳
            $nowTime = time();
            // 上次同步时间
            $lastTime = $nowTime - ((static::TIME + 1) * 60);
            // 日期时间
            $dateTime = date('Y-m-d', ($nowTime - (static::TIME * 60)));
            // 获取一定时间内的崩溃数据
            $list = ExceptionStreamAll::query()
                ->select(['stream_time', 'server_dev_str', 'exception_merge_id', 'extra_app_id'])
                ->where('stream_date', $dateTime)
                ->where('stream_time', '>=', $lastTime)
                ->whereIn('os_type', [1, 2, 3])
                ->where('server_dev_str', '!=', '')
                ->where('server_dev_str', '!=', '协议未被同意')
                ->where('event_name', 'exception_crash')
                ->orderBy('stream_time')
                ->getFromSR();
            // 判断是否有数据，没有数据则退出
            if (empty($list)) {
                Log::info("关联录屏和性能数据脚本没有数据，退出");
                return;
            }
            // 根据设备号，获取性能数据
            $list = $this->getPerfList($list);
            // 根据设备号，获取录屏数据
            $list = $this->getRecordList($list);
            // 处理数据
            $newData = [];
            foreach ($list as $item) {
                $newData[] = [
                    'exception_merge_id' => $item['exception_merge_id'],
                    'stream_date' => date('Y-m-d', $item['stream_time']),
                    'server_dev_str' => $item['server_dev_str'],
                    'record_id' => $item['record_id'] ?? 0,
                    'perf_id' => $item['perf_id'] ?? 0,
                ];
            }
            //字段
            $columns = [
                'exception_merge_id',
                'stream_date',
                'server_dev_str',
                'record_id',
                'perf_id',
            ];
            //写入csv
            $path = (new WriteCsvFileService("app/exception-record-perf/" . Str::random() . ".csv", $columns, $newData))->write();
            //同步到starRocks
            (new SyncFileToStarRocksService($path, $columns, ExceptionRecordPerf::TABLE_NAME))->sync();
            //打印日志
            Log::info("执行关联录屏和性能数据脚本完成");
        } catch (\Exception $e) {
            Log::error("执行关联录屏和性能数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    /**
     * 获取性能报告列表
     *
     * @param $list
     * @return array
     */
    private function getPerfList($list)
    {
        // 获取设备号列表，并去除重复数据
        $serverDevStrList = array_unique(array_column($list, 'server_dev_str'));
        // 当前时间减一天
        $yesterday = Carbon::now()->subDay()->toDateTimeString();
        // 设备号字符串
        $serverDevStrListStr = implode("','", $serverDevStrList);
        // SQL语句
        $sql = <<<SQL
select mysql_apm_report_list.id, mysql_apm_report_list.developer_app_id, mysql_apm_report_list.created_at, mysql_apm_report_list.dev_str, performance_stat_data.duration
from mysql_apm_report_list
inner join performance_stat_data on mysql_apm_report_list.id = performance_stat_data.session_id
where performance_stat_data.date >= '{$yesterday}' and mysql_apm_report_list.dev_str in ('{$serverDevStrListStr}')
order by mysql_apm_report_list.id desc
SQL;
        // 获取数据
        $perfList = (new PerfStarRocksService())->query($sql);
        // 根据设备号,进行数据分组
        $newPerfList = [];
        foreach ($perfList as $item) {
            $newPerfList[$item['dev_str']][] = $item;
        }
        // 组装数据,遍历list数组，关联性能报告
        foreach ($list as $key => $item) {
            // 判断是否找到关联的设备号
            if (isset($newPerfList[$item['server_dev_str']])) {
                // 检查报告是否符合
                foreach ($newPerfList[$item['server_dev_str']] as $val) {
                    // 创建时间比崩溃发生时间少，加上时长后和崩溃发生时间误差在1分钟之内，并且appid相同
                    if ((strtotime($val['created_at']) < $item['stream_time']) && (abs((strtotime($val['created_at']) + $val['duration']) - $item['stream_time']) <= 60) && ($val['developer_app_id'] == $item['extra_app_id'])) {
                        $list[$key]['perf_id'] = $val['id'];
                        break;
                    }
                }
            }
        }
        // 返回列表
        return $list;
    }

    /**
     * 获取录屏报告列表
     *
     * @param $list
     * @return array
     */
    private function getRecordList($list)
    {
        // 获取设备号列表，并去除重复数据
        $serverDevStrList = array_unique(array_column($list, 'server_dev_str'));
        // 当前时间减一天
        $yesterday = Carbon::now()->subDay()->toDateTimeString();
        // 设备号字符串
        $serverDevStrListStr = implode("','", $serverDevStrList);
        // SQL语句
        $sql = <<<SQL
select dev_str, any_value(android_id) as android_id
from active
where dev_str in ('{$serverDevStrListStr}')
group by dev_str
SQL;
        // 获取数据
        $devStrList = (new SdkStarRocksService())->query($sql);
        // dev_str转为android_id
        $devStrList = array_column($devStrList, 'android_id', 'dev_str');
        // 遍历 serverDevStrList 列表, 把 dev_str 转为 android_id
        foreach ($serverDevStrList as $key => $item) {
            if (isset($devStrList[$item])) {
                $serverDevStrList[$key] = $devStrList[$item];
            }
        }
        // 查询录屏数据
        $recordList = RecordLog::query()
            ->select(['device_id', 'id', 'created_at', 'updated_at', 'frequency', 'developer_app_id'])
            ->whereIn('device_id', $serverDevStrList)
            ->where('created_at', '>=', $yesterday)
            ->where('image_num', '>', 0)
            ->orderBy('id', 'desc')
            ->get();
        // 根据设备号,进行数据分组
        $newRecordList = [];
        // devStrList key 和 val 反转
        $devStrList = array_flip($devStrList);
        foreach ($recordList as $item) {
            $newRecordList[$devStrList[$item['device_id']] ?? $item['device_id']][] = $item;
        }
        // 组装数据,遍历list数组，关联性能报告
        foreach ($list as $key => $item) {
            // 判断是否找到关联的设备号
            if (isset($newRecordList[$item['server_dev_str']])) {
                // 检查报告是否符合
                foreach ($newRecordList[$item['server_dev_str']] as $val) {
                    // 创建时间比崩溃发生时间少，加上时长后和崩溃发生时间误差在1分钟之内
                    if ((strtotime($val['created_at']) < $item['stream_time']) && (abs(strtotime($val['updated_at']) - $item['stream_time']) <= 900) && ($val['developer_app_id'] == $item['extra_app_id'])) {
                        exec('php /data/www/developer/screen-record/artisan get:record:count ' . $val['id'], $output);
                        // 判断创建时间+图片数量是否和崩溃时间的差值在1分钟之内,这样才算符合
                        $duration = (int)$output[0] ?? 0;
                        if ((strtotime($val['created_at']) + ($duration / $val['frequency']) + 60) - $item['stream_time'] >= 0) {
                            $list[$key]['record_id'] = $val['id'];
                        }
                        break;
                    }
                }
            }
        }
        // 返回列表
        return $list;
    }
}
