<?php

/**
 * API控制器类
 * @desc API控制器类
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/02/25
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Http\Logic\ExceptionLogic;
use App\Service\Exception\GetMonitorData;
use App\Service\Exception\Home\CrashRankList;
use App\Service\Exception\Home\ErrorRankList;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApiController extends Controller
{
    /**
     * 首页崩溃率排行
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/07
     * @doc
     */
    public function crashRankList(Request $request): JsonResponse
    {
        $list = (new CrashRankList($request))->list();
        return $this->response(0, $list);
    }

    /**
     * 首页错误率排行
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/07
     * @doc
     */
    public function errorRankList(Request $request): JsonResponse
    {
        $list = (new ErrorRankList($request))->list();
        return $this->response(0, $list);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/18 10:22
     * memo : [異常趨勢]統計數據
     */
    public function summaryStatistic(Request $request): JsonResponse
    {
        if ($request->key != 'sP6XMSA41j7Zn38UkrT9gtfyKcqvOeYm') {
            return $this->response(1, '非法请求');
        }
        $extraAppId = $request->developer_app_id;
        $eventName = ExceptionLogic::EVENT_NAME[$request->type];
        $streamTime = [strtotime($request->start_date), strtotime($request->end_date)];
        $rawStreamDate = [$request->start_date, $request->end_date];
        $streamWhere = [];
        if ($request->os_type ?? 0) {
            $streamWhere['os_type'] = ($request->os_type ?? 0) ? (string)($request->os_type) : '';
        }
        $streamWhere['crash_type'] = $request->crash_type ?? null; //异常类型
        //SDK包名
        $useDuration = $request->use_duration ?? 0; //使用時長
        $whiteListFilter = (isset($request->is_filter) && !empty($request->is_filter)) ? intval($request->is_filter) : 0; //是否剔除白名單
        $result2 = (new ExceptionLogic())->summary(
            $extraAppId,
            $eventName,
            $streamTime,
            $streamWhere,
            $useDuration,
            $whiteListFilter,
            $rawStreamDate
        );
        return $this->response(0, $result2);
    }

    /**
     * 获取监控数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function monitor(Request $request): JsonResponse
    {
        $params = $request->all();
        if (empty($params['key']) || $params['key'] != 'sP6XMSA41j7Zn38UkrT9gtfyKcqvOeYm') {
            return $this->response(1, '非法请求');
        }

        $result = (new GetMonitorData($params))->getData();
        return $this->response(0, $result);
    }
}
