<?php

/**
 * 关键词过滤脚本
 * @desc 关键词过滤脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Jobs\ExceptionKeywordJob;
use App\Model\StarRocks\FilterKeyword;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExceptionKeywordCommand extends Command
{
    /**
     * 时间
     *
     * @var int
     */
    public const TIME = 600;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'filter:exception:keyword {startTime? : 开始时间} {endTime? : 结束时间} {developerAppId? : 效能后台ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '关键词过滤脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');

        //1、获取所有关键词
        $keywords = FilterKeyword::query()->when($this->argument('developerAppId'), function ($query) {
            return $query->where('developer_app_id', $this->argument('developerAppId'));
        })->selectRaw('keyword, developer_app_id')->getFromSR();
        //判断关键词是否为空
        if (empty($keywords)) {
            $this->error("关键词为空");
            Log::info("执行关键词过滤脚本，关键词为空");
            return;
        }
        //2、按效能后台ID分组
        $newKeywords = [];
        foreach ($keywords as $keyword) {
            $newKeywords[$keyword['developer_app_id']][] = $keyword['keyword'];
        }
        $keywords = $newKeywords;
        //3、查询关键词
        $endTime = (int)($this->argument('endTime') ?? time());
        $startTime = (int)($this->argument('startTime') ?? $endTime - (self::TIME * 1.5)); //时间戳
        //打印日志
        Log::info("执行关键词过滤脚本，开始时间：{$startTime}、结束时间：{$endTime}");
        //循环查询
        foreach ($keywords as $developerAppId => $keyword) {
            dispatch(new ExceptionKeywordJob($developerAppId, $startTime, $endTime, $keyword))
                ->onQueue('exception_keyword_queue');
        }
    }
}
