APP_NAME=异常概览
APP_KEY=base64:Awd9c/m2KjYMY9XASjw4b3/q9Xz0NyPtx6gVg9Z1Q5o=
APP_DEBUG=true
APP_ENV=develop
APP_URL=https://test-exception-manager.shiyue.com

LOG_CHANNEL=stack

DB_CONNECTION=developer
DB_HOST=*********
DB_PORT=3306
DB_DATABASE=developer
DB_USERNAME=xge
DB_PASSWORD=D3fYRPMjIrayKqv1

DB_HOST_EXCEPTION=*************
DB_PORT_EXCEPTION=3306
DB_DATABASE_EXCEPTION=exception
DB_USERNAME_EXCEPTION=xge
DB_PASSWORD_EXCEPTION=ZLhXGWgB^57kPA=M

BROADCAST_DRIVER=log

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
QUEUE_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=3

REDIS_HOST_QUEUE=127.0.0.1
REDIS_PASSWORD_QUEUE=null
REDIS_PORT_QUEUE=6379
REDIS_DB_QUEUE=3

REDIS_HOST_SESSION=127.0.0.1
REDIS_PASSWORD_SESSION=null
REDIS_PORT_SESSION=6379
REDIS_DB_SESSION=3

SESSION_CONNECTION=session
SESSION_COOKIE=my_developer
SESSION_DRIVER=redis        #使用redis作为公共session存放位置
SESSION_DOMAIN=.shiyue.com   #需要共享的域
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=false

#COS配置
COS_BUCKET_NAME_EXCEPTION=efficacy-exception-1256453865
COS_BUCKET_NAME_REGION=ap-shanghai
COS_SECRET_ID=AKIDud6IDrEK9hyxg8JD7k6B5i37uQikV0D9
COS_SECRET_KEY=g5D4e3D6i26sNbYEdlbBUWGkwrVsynKa
COS_BASE_URL=https://test-hitbug-1256453865.cos.ap-shanghai.myqcloud.com/

#clickhouse配置
UPLOAD_DATA_WAREHOUSE_URL=https://test-datawarehouse.shiyue.com/api/reporting-data
GET_DATA_WAREHOUSE_URL=https://test-datawarehouse.shiyue.com/admin/reporting-list
DATA_WAREHOUSE_TABLE_NAME=user_log_data_all

#StarRocks配置
STAR_ROCKS_HOST=
STAR_ROCKS_PORT=
STAR_ROCKS_DATABASE=
STAR_ROCKS_USERNAME=
STAR_ROCKS_PASSWORD=
