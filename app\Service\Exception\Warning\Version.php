<?php

/**
 * 预警版本服务类
 * @desc 预警版本服务类
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/09/11
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Warning;


use App\Components\ClickHouse\ClickHouse;
use App\Model\ClickHouse\UserLogDataAll;

class Version extends Base
{
    /**
     * 获取版本列表
     *
     * @return array
     */
    public function getList(): array
    {
        $list = (new ClickHouse())->getData(
            UserLogDataAll::query()
                ->select(['app_version'])
                ->where('extra_app_id', $this->params['developer_app_id'])
                ->where('event_name', 'exception_start')
                ->where('app_version', '!=', '')
                ->groupBy('app_version')
        );
        return $this->format($list);
    }

    /**
     * 格式化数据
     *
     * @param $list
     * @return array
     */
    private function format($list): array
    {
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'label' => $item['app_version'],
                'value' => $item['app_version'],
            ];
        }
        return $newList;
    }
}
