<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExceptionProcessTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('exception_process', function (Blueprint $table) {
            $table->Increments('id');
            $table->unsignedInteger('record_id')->comment('异常记录唯一id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedInteger('operator_id')->default(0)->comment('操作人id');
            $table->string('operator', 64)->nullable()->comment('操作人');
            $table->string('handler_id', 512)->default('')->comment('处理人id');
            $table->string('handler', 512)->nullable()->comment('处理人');
            $table->unsignedInteger('status')->default(1)->comment('处理状态，1:未处理，2:处理中，3:已处理');
            $table->string('comment', 512)->nullable()->comment('操作备注');
            $table->unsignedTinyInteger('is_status_change')->default(0)->comment('是否状态改变，1:状态改变、0:评论记录');
            $table->timestamps();
            $table->index('record_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `exception_process` comment '异常处理过程表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('exception_process');
    }
}
