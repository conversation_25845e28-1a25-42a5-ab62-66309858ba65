<?php
/**
 * Symbol.php
 *
 * User: Dican
 * Date: 2022/9/16
 * Email: <<EMAIL>>
 */

namespace App\Model;

use App\Components\ApiResponse\ErrorHelper;
use App\Components\Helper\CommonHelper;
use App\Http\Logic\SymbolLogic;
use App\Service\SshService;

/**
 * App\Model\Symbol
 *
 * @property int $id
 * @property int $developer_app_id APP项目id
 * @property bool $type 异常类型 1为崩溃异常
 * @property string $app_version app版本
 * @property string $file_name 文件名
 * @property string $file_md5 md5
 * @property string $file_url 文件下载地址
 * @property int $file_size 文件大小
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereAppVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereDeveloperAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereFileMd5($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereFileSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereFileUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property bool $os_type 平台类型;1为安卓,2为iOS
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereOsType($value)
 * @property string $uuid uuid
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Symbol whereUuid($value)
 */
class Symbol extends BaseModel
{
    use ModelTrait;

    protected $table = 'symbol';
    protected $primaryKey = 'id';
    protected $fillable = [
        'developer_app_id', 'type', 'app_version', 'file_name', /*'file_md5',*/
        'file_url', 'file_size', 'os_type', 'uuid', 'abi',
    ];

    public $validateRule = [
        'developer_app_id' => 'required|integer',
        'type' => 'required|integer',
        'os_type' => 'required|integer',
        'app_version' => 'required|string',
        'file_name' => 'required|string',
        //        'file_md5' => 'required|string',
        'file_url' => 'required|string',
        'file_size' => 'required|integer',
        'uuid' => 'required|string',
    ];

    public $uniqueKey = ['developer_app_id', 'type', 'os_type', 'app_version', 'uuid'];

    /**
     * 获取符号表列表
     * @param $developerAppId
     * @param $appVersion
     * @param $osType
     * @param array $uuids
     * @param false $paginate
     * @param int $type
     * @return Symbol[]|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|\Illuminate\Support\Collection
     */
    public static function symbolList($developerAppId, $appVersion, $osType, $uuids = [], $paginate = false, $type = 1)
    {
        $list = self::whereDeveloperAppId($developerAppId)->/*whereOsType($osType)->whereType($type)->*/whereIn('os_type', [$osType, 0])
            ->when(!empty($uuids), function ($query) use ($uuids) {
                $query->whereIn('uuid', $uuids);
            })
            ->where('app_version', $appVersion)->orderByDesc('updated_at');
        return $paginate ? $list->paginate() : $list->get();
    }

    /**
     * 获取符号表列表(包含uuid)
     *
     * @param $developerAppId
     * @param $appVersion
     * @param $osType
     * @param array $uuids
     * @param false $paginate
     */
    public static function symbolIndex(int $developerAppId, string $appVersion, string $uuid, string $filename, int $pageSize, $osType)
    {
        $builder = self::query()->selectRaw('*, created_at AS upload_date')->where('developer_app_id', $developerAppId)->where('uuid', '!=', '');
        if ($appVersion) {
            $builder->where('app_version', 'like', "%{$appVersion}%");
        }

        if ($uuid) {
            $builder->where('uuid', 'like', "%{$uuid}%");
        }

        if ($filename) {
            $builder->where('file_name', 'like', "%{$filename}%");
        }

        if ($osType) {
            $builder->where('os_type', $osType);
        }

        return $builder->orderByDesc('created_at')->paginate($pageSize);
    }

    /**
     * 校验uuid
     * @throws \App\Exceptions\InfoException
     */
    protected function extraHandler()
    {
        $symbolDetail = (new SymbolLogic())->pullSymbolDetail(SymbolLogic::$SYMBOL_SERVICE_TYPE['UUID'], $this->file_url, '', $this->file_name);
        \Log::info('$symbolDetail:' . CommonHelper::prettyJsonEncode($symbolDetail));
        //IOS不需要判断uuid
        if ($symbolDetail['uuid'] != $this->uuid && $this->os_type != 2) {
            ErrorHelper::callException(1013, "UUID不匹配（当前文件：{$symbolDetail["uuid"]}）");
        }

        //-----
        if ($discardVersion = 0) {
            //上传文件到打包机、执行脚本
            $sshService = new SshService(config('ssh.packer_host'), config('ssh.packer_port'), config('ssh.packer_user'), config('ssh.packer_pwd'));
            $cmd = 'cd /Users/<USER>/awesome-tools;git pull;source venv/bin/activate;
                inv crash.prase-crash.' . (self::OS_TYPE[$this->os_type] ?? 'android') . '-uuid --url=' . $this->file_url;
            \Log::info('check-uuid-cmd:' . $cmd);
            $sshService->run($cmd, $output);
            $runResult = $sshService->formatOut($output);
            //        if ($runResult['code'] != 0) ErrorHelper::callException(1012, '解析报错');
            if ($runResult['code'] != 0) {
                ErrorHelper::callException(1012, $runResult['error']);
            }

            if ($runResult['data'] != $this->uuid) {
                ErrorHelper::callException(1013, 'uuid不匹配');
            }

        }
    }
}
