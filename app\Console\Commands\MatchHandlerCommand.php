<?php

/**
 * 同步exception数据脚本
 * @desc 同步exception数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/11/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\ExceptionHandlerConfig;
use App\Model\Record;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\ExceptionHandleService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MatchHandlerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'match:handler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '匹配异常处理人数据';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            //打印日志
            Log::info("执行匹配异常处理人数据脚本开始");
            // 获取当前时间的前5分钟
            $date = Carbon::now()->subMinutes(5);
            // 获取有那些应用开启了关键词匹配
            $handlerConfigs = ExceptionHandlerConfig::query()->get();
            $developerAppIds = [];
            foreach ($handlerConfigs as $handlerConfig) {
                // 判断 handler 不为空
                if (!empty($handlerConfig->handler)) {
                    $developerAppIds[$handlerConfig->developer_app_id][] = $handlerConfig;
                }
            }
            // 判断是否有数据
            if (empty($developerAppIds)) {
                Log::info("执行匹配异常处理人数据脚本，没有数据");
                return;
            }
            // 获取异常记录
            $offset = 0;
            $limit = 10000;
            $ids = array_keys($developerAppIds);
            while (true) {
                $records = ExceptionStreamAll::query()
                    ->selectRaw("exception_block_id, extra_app_id, ANY_VALUE(event_name) as event_name, ANY_VALUE(type) as category, ANY_VALUE(subject_name) as subject_name, ANY_VALUE(explain_desc) as explain_desc, ANY_VALUE(os_type) as os_type")
                    ->where('stream_date', '>=', $date->toDateString())
                    ->where('stream_time', '>=', $date->timestamp)
                    ->whereIn('extra_app_id', $ids)
                    ->where('event_name', 'exception_error')
                    ->whereRaw("exception_block_id not in (select exception_block_id from exception_handler where handler_id != 0 and extra_app_id in (" . implode(',', $ids) . "))")
                    ->offset($offset)
                    ->limit($limit)
                    ->groupBy('extra_app_id', 'exception_block_id')
                    ->getFromSR();
                // 判断没有数据，获取时间少于当前时间减1小时
                if (empty($records) || $date->timestamp < Carbon::now()->subHours()->timestamp) {
                    break;
                }
                // 处理数据
                foreach ($records as $record) {
                    // 判断是那个应用ID
                    $configs = $developerAppIds[$record['extra_app_id']];
                    $this->handleData($record, $configs);
                }
                if (count($records) < $limit) {
                    break;
                }
                $offset += $limit;
            }
            Log::info("执行匹配异常处理人数据脚本完成");
        } catch (\Exception $e) {
            Log::error("执行匹配异常处理人数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    /**
     * 处理数据
     * @param $record
     * @param $config
     * @return void
     */
    private function handleData($record, $configs)
    {
        $handlerKeywordId = 0;
        $handlers = [];
        foreach ($configs as $config) {
            $subjectName = strtolower(rawurldecode($record['subject_name']));
            $explainDesc = strtolower(rawurldecode($record['explain_desc']));
            // 判断是否命中关键词
            if (strpos($subjectName, $config->keyword) === false && strpos($explainDesc, $config->keyword) === false) {
                continue;
            }
            foreach ($config->handler as $handler) {
                $handlers[] = [
                    'handler_id' => $handler['handler_id'],
                    'handler_name' => $handler['handler'],
                ];
            }
            $handlerKeywordId = $config->id;
        }
        if (empty($handlers)) {
            return;
        }
        // 创建异常记录
        $recordId = Record::createRecord([
            'developer_app_id' => $record['extra_app_id'],
            'event_name' => $record['event_name'],
            'category' => $record['category'],
            'name' => $record['subject_name'],
            'explain' => $record['explain_desc'],
            'os_type' => $record['os_type'],
            'exception_block_id' => $record['exception_block_id'],
            'handler_keyword_id' => $handlerKeywordId,
        ]);
        (new ExceptionHandleService([
            'developer_app_id' => $record['extra_app_id'],
            'exception_block_id' => $record['exception_block_id'],
            'record_id' => $recordId,
            'is_status_change' => 1,
            'status' => 1,
            'handlers' => $handlers,
            'comment' => '自动匹配异常处理人',
            'type' => 2,
        ]))->updateExceptionState();
    }
}
