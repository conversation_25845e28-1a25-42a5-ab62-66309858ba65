<?php
/**
 * ModelTrait.php
 *
 * User: Dican
 * Date: 2022/8/16
 * Email: <<EMAIL>>
 */

namespace App\Model;

use App\Components\ApiResponse\ErrorHelper;
use App\Components\Helper\CommonHelper;
use App\Exceptions\InfoException;
use App\Http\Logic\SymbolLogic;

/**
 * 模型层操作trait
 * 封装增删查改统一操作，减少每次写重复的代码
 * Trait ModelTrait
 * @package App\Model
 */
trait ModelTrait
{
    /**
     * 新增、编辑
     * model需定义validateRule、uniqueKey字段
     * @example
        public $validateRule = [
            'username' => 'required',
            'alias' => 'required',
            'password' => 'required',
        ];
        public $uniqueKey = ['username'];
     * @param array $params
     * @param false $validate 是否验证表单数据
     * @param false $checkExists 是否检验唯一值
     * @throws InfoException
     */
    public function store($params = [], $validate = false, $checkExists = false)
    {
        //CommonHelper::xdebug($symbolDetail, '$symbolDetail');
        empty($params) && $params = \request()->all();
        $validate && ErrorHelper::validate($params, $this->validateRule);

        $checkExists && $this->checkExists($params);

        foreach ($params as $key => $value) {
            in_array($key, $this->getFillable()) && $this->$key = $value;
        }

        //额外处理
        $this->extraHandler();


        !$this->save() && ErrorHelper::callException(1007, '保存失败');
    }

    /**
     * 检查是否已存在相同数据
     * model需定义uniqueKey字段
     * @example public $uniqueKey = ['username'];代表用户名唯一
     * @param array $params
     * @throws InfoException
     */
    private function checkExists(array $params)
    {
        //组装唯一值判断条件
        $unique = [];
        foreach ($this->uniqueKey as $value) {
            $unique[$value] = $params[$value] ?? '';
        }

        (new static())->where($unique)->exists() && ErrorHelper::callException(1012, '已存在相应数据');
    }

    /**
     * 空方法，用于保存时做一下额外处理
     * model层各自重写这个方法
     */
    protected function extraHandler()
    {

    }

    /**
     * 一对多统一批量新增
     * 需要在模型层配置validateRule、foreignKey和fillable变量
     * 直接清除旧数据后新增，比较粗暴，建议加事务
     * @param $foreign
     * @param array $params
     * @param bool $validate
     * @throws InfoException
     */
    public function batchStore($foreign, array $params = [], bool $validate = true)
    {
        if (empty($foreign) || empty($params)) {
            ErrorHelper::callException(1000);
        }

        $insertData = [];
        foreach ($params as $param) {
            $param[$this->foreignKey] = $foreign;
            $validate && ErrorHelper::validate($param, $this->validateRule);

            $one = [];
            foreach ($param as $key => $value) {
                in_array($key, $this->getFillable()) && $one[$key] = $value;
            }
            if (!empty($one)) {
                $one['created_at'] = $one['updated_at'] = date('Y-m-d H:i:s');
                $insertData[] = $one;
            }
        }
        // 清除旧数据
        $this->where($this->foreignKey, $foreign)->delete();
        !empty($insertData) && !$this->insert($insertData) && ErrorHelper::callException(1007, '保存失败');
    }
}
