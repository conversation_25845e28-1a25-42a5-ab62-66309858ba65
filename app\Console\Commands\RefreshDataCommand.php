<?php

/**
 * 刷新数据脚本
 * @desc 刷新数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Service\StarRocksService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RefreshDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '刷新数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            //当前时间减一个小时
            $time = Carbon::now()->subHour()->timestamp;
            // 日期时间
            $dateTime = date('Y-m-d', $time);
            for ($i = 0; $i < 2; $i++) {
                // sql语句
                $sql = <<<SQL
insert into exception_stat_all_v2
select extra_app_id,
       exception_block_id,
       event_name,
       min(stream_time) as min_stream_time,
       max(stream_time) as max_stream_time,
       count(distinct server_dev_str) as server_dev_str_count,
       count(1) as count
from exception_stream_all
where stream_date = '{$dateTime}'
  and event_name in ('exception_crash', 'exception_error')
  and extra_app_id not in (67)
  and stream_time >= {$time}
  and exception_block_id not in (select exception_block_id from exception_stat_all_v2 where max_stream_time >= {$time})
group by extra_app_id, exception_block_id, event_name
SQL;
                // 同步数据表
                (new StarRocksService())->execute($sql);
                //睡眠30s
                if ($i == 0) {
                    sleep(30);
                }
            }
        } catch (\Exception $e) {
            Log::error("执行刷新数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
