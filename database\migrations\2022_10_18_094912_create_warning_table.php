<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWarningTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('warning', function (Blueprint $table) {
            $table->bigIncrements('warning_id')->comment('预警id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台类型;1为安卓,2为iOS');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态');
            $table->string('name', 128)->comment('预警计划名称');
            $table->string('receiving_group', 255)->comment('接收群地址');
            $table->json('app_version')->comment('app版本');
            $table->json('notification_method')->comment('通知方式');
            $table->json('receiving_person')->comment('接收人员');
            $table->dateTime('last_warning_time')->comment('最近预警时间');
            $table->timestamps();
            $table->unique(['developer_app_id', 'name']);
            $table->index(['developer_app_id', 'os_type']);
            $table->index(['developer_app_id', 'last_warning_time', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('warning');
    }
}
