<?php

namespace App\Http\Logic;

use App\Components\Helper\CommonHelper;
use App\Model\BaseModel;
use App\Model\Symbol;
use Illuminate\Database\Eloquent\Model;

class SymbolLogic
{

    public static $SYMBOL_SERVICE_TYPE = [
        'UUID' => 6, //獲取UUID
        'ANALYSIS' => 7, //解釋符號
        'HARMONY_UUID' => 8, //獲取UUID
        'HARMONY_PARSE_STACK' => 9, //解釋符號
    ];

    /**
     * 请求接口获取符号表解析数据
     *
     * @param int $type
     * @param string $symbolUrl
     * @param string $content
     * @param string $symbolName
     * @return array
     * @throws \Throwable
     */
    public function pullSymbolDetail(int $type, string $symbolUrl, string $content = '', string $symbolName = ''): array
    {
        try {
            $currentTime = time();
            $secret = 'aWv5xmD/9lEDqXfjUyQlMITzoTMlG8LzLan5PzkzEhg=';
            $domain = env('SYMBOL_SERVICE_DOMAIN');
            $startApi = "{$domain}/analysis/start";
            $startBody = [
                'task_type' => $type, //ios or android
                'symbol_urls' => $symbolUrl,
                'symbol_name' => $symbolName,
                'task_data' => $content,
                'timestamp' => $currentTime,
            ];
            $startResult = CommonHelper::commonHttpPost($startApi, $startBody, [
                'Content-Type' => 'application/json',
                'X-Signature' => $sha1 = sha1($signature = CommonHelper::prettyJsonEncode($startBody) . ":{$secret}:{$currentTime}"),
            ]);
            $mark1 = "pullSymbolDetailStartBody: {$signature} SHA1:{$sha1} startResult:" . CommonHelper::prettyJsonEncode($startResult);
            if ($taskId = $startResult['data']['task_id'] ?? '') {
                $statusApi = "{$domain}/analysis/result/{$taskId}";
                $retry = 0;
                while (true) { //TODO:callback
                    $statusResult = CommonHelper::commonHttpGet($statusApi, []);
                    if (
                        (($statusResult['result']['code'] ?? '-1') === 0) &&
                        ($data = $statusResult['result']['data'] ?? [])
                    ) {
                        return $data;
                    }

                    // 判断是否有错误
                    if (($statusResult['result']['code'] ?? '') === -1) {
                        throw new \Exception($statusResult['result']['message'] ?? '系统错误', -1);
                    }
                    $retry = $retry + 1;
                    if ($retry >= 10) {
                        break;
                    }

                    sleep($retry * 2);
                }
            } else {
                throw new \Exception($startResult['message'] ?? '系统错误', -1);
            }
            throw new \Exception("{$mark1} / " . CommonHelper::prettyJsonEncode($statusResult ?? []), -1);
        } catch (\Throwable $e) {
            CommonHelper::xdebug($e);
            throw $e;
        }
    }

    /**
     * 保存符号表
     *
     * @param int $developerAppId
     * @param string $appVersion
     * @param string $symbolUrl
     * @param string $symbolName
     * @return Symbol|array|\Illuminate\Database\Eloquent\Builder|Model
     * @throws \Throwable
     */
    public function symbolCommonStore(int $developerAppId, string $appVersion, string $symbolUrl, string $symbolName, $osType)
    {
        $currentDate = date('Y-m-d H:i:s');
        $result = [];
        $type = self::$SYMBOL_SERVICE_TYPE['UUID'];
        if ($osType == BaseModel::HARMONY) {
            $type = self::$SYMBOL_SERVICE_TYPE['HARMONY_UUID'];
        }
        if ($symbolDetail = $this->pullSymbolDetail($type, $symbolUrl, '', $symbolName)) {
            // 上传参数中的uuid
            $uuid = request()->input('uuid');
            // 判断是对象还是数组
            $isArray = isset($symbolDetail[0]);
            // 不是数组的话转成数组
            if ($isArray) {
                // 当前时间戳
                $currentTime = time();
                // 获取文件路径
                $path = parse_url($symbolUrl, PHP_URL_PATH);
                $filePath = substr($path, strpos($path, '/files'));
                $fileHost = substr($symbolUrl, 0, strpos($symbolUrl, '/files'));
                $prefixPath = '/data/www/developer/efficacy-manager/storage/app/public';
                // 解压文件
                $this->unzipFile("{$prefixPath}{$filePath}", "{$prefixPath}/files/so/{$currentTime}");
                // 判断是否有uuid参数，如果有则判断，解析的uuid和上传的uuid是否一致
                if ($uuid && !in_array($uuid, array_column($symbolDetail, 'uuid'))) {
                    throw new \Exception('UUID不一致，请重新上传', -1);
                }
                // 处理数据
                foreach ($symbolDetail as $item) {
                    // 修改文件名称
                    rename("{$prefixPath}/files/so/{$currentTime}/{$item['abi']}/{$item['file_name']}", "{$prefixPath}/files/so/{$currentTime}/{$item['abi']}/{$item['uuid']}.so");
                    // 插入数据
                    $result = Symbol::query()->updateOrCreate([
                        'developer_app_id' => $developerAppId,
                        'app_version' => $appVersion,
                        'os_type' => $item['os_type'],
                        'uuid' => $item['uuid'],
                    ], array_merge([
                        'file_url' => "{$fileHost}/files/so/{$currentTime}/{$item['abi']}/{$item['uuid']}.so",
                        'created_at' => $currentDate,
                        'updated_at' => $currentDate,
                    ], $item));
                }
            } else {
                // 判断是否有uuid参数，如果有则判断，解析的uuid和上传的uuid是否一致
                if ($uuid && $uuid !== $symbolDetail['uuid']) {
                    throw new \Exception('UUID不一致，请重新上传', -1);
                }
                $result = Symbol::query()->updateOrCreate([
                    'developer_app_id' => $developerAppId,
                    'app_version' => $appVersion,
                    'os_type' => $symbolDetail['os_type'],
                    'uuid' => $symbolDetail['uuid'],
                ], array_merge([
                    'file_url' => $symbolUrl,
                    'created_at' => $currentDate,
                    'updated_at' => $currentDate,
                ], $symbolDetail));
            }
        }
        return $result;
    }

    /**
     * 解压文件
     *
     * @param $zipFilePath
     * @param $destinationPath
     * @return bool
     */
    private function unzipFile($zipFilePath, $destinationPath)
    {
        $zip = new \ZipArchive();
        $res = $zip->open($zipFilePath);
        if ($res === true) {
            $zip->extractTo($destinationPath);
            $zip->close();
            return true;
        }
        return false;
    }
}
