<?php
/**
 * TapdBug.php
 *
 * User: hetao
 * Date: 2023/7/20
 * Email: <<EMAIL>>
 */

namespace App\Model;

use App\Http\Logic\TapdLogic;
use Illuminate\Support\Facades\DB;

class TapdBug extends BaseModel
{
    protected $table = 'tapd_bug';
    protected $primaryKey = 'id';
    protected $fillable = [
        'developer_app_id', 'exception_block_id', 'tapd_account_id', 'tapd_bug_id',
        'type', 'detail', 'tapd_link', 'create_type', 'creator_id', 'bind_status',
        'status',
    ];

    // 获取tapd跳转链接
    public static function getTapdLink(array $params)
    {
        return TapdBug::query()->where('developer_app_id', $params['developer_app_id'])
            ->where(DB::raw('upper(exception_block_id)'), strtoupper($params['exception_block_id']))
            ->where('tapd_bug_id', $params['bug_id'])
            ->where('bind_status', '!=', 0)
            ->pluck('tapd_link');
    }

    // 解绑tapd链接
    public static function untieTapd(array $params)
    {
        TapdBug::query()
            ->where('developer_app_id', $params['developer_app_id'])
            ->when(!empty($params['exception_block_id']), function ($query) use ($params) {
                return $query->where(DB::raw('upper(exception_block_id)'), strtoupper($params['exception_block_id']));
            })
            ->where('tapd_bug_id', $params['bug_id'])
            ->where('bind_status', '!=', 0)
            ->update(['bind_status' => 0]);
    }

    // 根据exception_block_id获取最新且未解绑的缺陷记录
    public static function getTapdBugList(int $developerAppId, int $type, array $list)
    {
        // 获取当前项目绑定的item_id
        $tapdAccountDetail = TapdLogic::pullTapdAccountDetailCache($developerAppId);
        $result = TapdBug::query()
            ->select([DB::raw('upper(exception_block_id) as exception_block_id'), 'tapd_bug_id'])
            ->where('developer_app_id', $developerAppId)
            ->where('type', $type) // 1 崩溃 2错误
            ->where('bind_status', 1) // 1 已绑定 2未绑定
            ->when(!empty($tapdAccountDetail), function ($query) use ($tapdAccountDetail) {
                $query->where('tapd_account_id', $tapdAccountDetail['id']);
            })
            ->whereIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                return strtoupper($item);
            }, array_column($list, 'exception_block_id')))
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    public static function getTapdBugByExceptionBlockId(int $developerAppId, int $type, string $exceptionBlockId)
    {
        // 获取当前项目绑定的item_id
        $tapdAccountDetail = TapdLogic::pullTapdAccountDetailCache($developerAppId);
        return TapdBug::query()
            ->where('developer_app_id', $developerAppId)
            ->where('type', $type) // 1 崩溃 2错误
            ->where('bind_status', 1) // 1 已绑定 2未绑定
            ->where(DB::raw('upper(exception_block_id)'), strtoupper($exceptionBlockId))
            ->when(!empty($tapdAccountDetail), function ($query) use ($tapdAccountDetail) {
                $query->where('tapd_account_id', $tapdAccountDetail['id']);
            })
            ->first();
    }
}
