<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><PERSON>a Email: zhang<PERSON><EMAIL>
 * Date: 2022/6/1 11:28
 */

namespace App\Providers;
use Illuminate\Support\ServiceProvider;

class ClickHouseServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register the application services.
     *
     * @return SqlPrepare|void
     */
    public function register()
    {
        $this->app->singleton(
            'clickhouse',
            'App\Components\ClickHouse\ClickHouse'
        );


    }
}
