<?php

namespace App\Http\Logic;

use App\Components\Helper\CommonHelper;
use App\Service\TapdService;
use Illuminate\Support\Facades\DB;
use App\Components\ApiResponse\StatusCode;
use Illuminate\Support\Facades\Log;
use App\Model\TapdBug;

Class TapdLogic
{

    const TAP_BUG_VIEW_DOMAIN = 'https://www.tapd.cn/';
    const TAP_BUG_VIEW_URI = '/bugtrace/bugs/view';

    /**
     * 创建缺陷
     * @param array $params
     * @return array
     */
    public function createTapdBug(array $params)
    {
        // 根据tapd_account_id获取绑定配置表
        $tapdConfig =  self::pullTapdAccountDetailCache($params['developer_app_id']);
        if (empty($tapdConfig)) {
            return [false, StatusCode::C_NOT_BOUND_TAPD_PROJECT, null];
        }
        $tapdRequestParams = $this->unsetParam($params);
        $tapdRequestParams['workspace_id'] = $tapdConfig['item_id'];
        // 调用tapd服务api创建权限，获取到对应的参数
        $data = TapdService::post(TapdService::TAPD_CREATE_BUG_URL, $tapdRequestParams);
        if ($data['status'] != 1) {
            Log::error($data);
            return [false, StatusCode::C_CREATE_TAPD_BUG_FAIL, null];
        }
        // 通过模型类创建缺陷表记录
        // todo $params['create_type'] 手续需要判断是否手动创建还是绑定创建已有缺陷,到时候需要拆分成两个方法
        $tapdBug = new TapdBug();
        $tapdBug->developer_app_id = $params['developer_app_id'];
        $tapdBug->exception_block_id = $params['exception_block_id'];
        $tapdBug->tapd_account_id = $tapdConfig['id'];
        $tapdBug->tapd_bug_id = $data['data']['Bug']['id'];
        $tapdBug->type = $params['type'];
        $tapdBug->detail = json_encode($tapdRequestParams, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $tapdBug->tapd_link = $this->structTapdLink($tapdConfig['item_id'], $data['data']['Bug']['id']);
        $tapdBug->create_type = $params['create_type'] ?? 1;
        $tapdBug->creator_id = $params['reporter_id'];
        $tapdBug->bind_status = 1; // 默认为已绑定
        $tapdBug->status = $data['data']['Bug']['status'];
        $tapdBug->save();
        return [true, 0, $data['data']['Bug']['id']];
    }

    /**
     * 剔除请求tapd时不必要的参数
     * @param array $params
     * @return array
     */
    public function unsetParam(array $params)
    {
        unset($params['developer_app_id']);
        unset($params['reporter_id']);
        unset($params['exception_block_id']);
        unset($params['type']);
        return $params;
    }

    /**
     * 生成跳转到tapd的链接
     * @param int $workspaceId 项目id
     * @param int $bugId 缺陷id
     * @return string
     */
    public function structTapdLink(int $workspaceId, int $bugId)
    {
        return TapdLogic::TAP_BUG_VIEW_DOMAIN . $workspaceId . TapdLogic::TAP_BUG_VIEW_URI . '?bug_id=' . $bugId;
    }

    /**
     * 更新缺陷状态
     * @param array $data
     */
    public function updateBugStatus(array $data)
    {
        $groups = [];
        foreach ($data as $key => $value) {
            $groups[$value][] = strval($key); // 注意转换，数据库中使string，tapd返回的是int，需要做好类型转换，否则会有bug
        }
        foreach ($groups as $status => $bugIdArray) {
            TapdBug::whereIn('tapd_bug_id', $bugIdArray)->update(['status' => $status]);
        }
    }

    /**
     * @param int $itemAccountId
     * @param int $developerAppId
     * @param string $itemId
     * @param int $bindStatus
     * @return int
     * author : <EMAIL>
     * datetime: 2023/07/20 14:41
     * memo : 操作綁定（含：增/刪/改）
     */
    public function itemBindHandler(int $developerAppId, int $tapdAccountId, string $itemId, int $bindStatus)
    {
        // 从tapd中获取项目信息，返回null则表示该项目ID无效
        if ($itemId) {
            $result = TapdService::get(TapdService::TAPD_GET_WORKSPACES_INFO_URL, ['workspace_id' => $itemId]);
            if (empty($result)) {
                return ['code' => StatusCode::C_ITEM_ID_ERROR];
            }
        }

        $currentTime = date('Y-m-d H:i:s');
        $builder = DB::connection('exception')->table('tapd_account');

        // 判断当前项目id是否已经绑定过其他$developerAppId项目
//        if ($itemId) {
//            $record = (clone $builder)->where('item_id', $itemId)->where('bind_status',1)->get()->toArray();
//            if (!empty($record)) {
//                return ['code' => StatusCode::C_ITEM_ID_BOUND, 'developer_app_id' => ];
//            }
//        }

        if(!$tapdAccountId && $itemId){//新增
            //如存在已解綁的歷史記錄，則更新歷史記錄
            $record = (clone $builder)->where('developer_app_id', $developerAppId)->where('item_id', $itemId)->where('bind_status',0)->get()->toArray();
            if (!empty($record[0])) {
                $record = (array)$record[0];
            }
            if($tapdAccountId = ($record['id'] ?? 0)){
                $record = [
                    'developer_app_id' => $developerAppId,
                    'item_id' => $itemId,
                    'bind_status' => $bindStatus,
                    'updated_at' => $currentTime
                ];
                $builder->where('id', $tapdAccountId)->update($record);
                return ['tapd_account_id' => $tapdAccountId];
            }else{
                $record = [
                    'developer_app_id' => $developerAppId,
                    'item_id' => $itemId,
                    'bind_status' => $bindStatus,
                    'updated_at' => $currentTime,
                    'created_at' => $currentTime
                ];
                return ['tapd_account_id' => $builder->insertGetId($record)];
            }
        }elseif($tapdAccountId){//更新
            if($itemId) $record['item_id'] = $itemId;
            $record['bind_status'] = $bindStatus;
            $record['updated_at'] = $currentTime;
            $builder->where('id', $tapdAccountId)->update($record);
            return ['tapd_account_id' => $tapdAccountId];//TODO: update table(tapd_bug)
        }else{//查看
            return $builder->where('developer_app_id', $developerAppId)->where('bind_status',1)->first() ?? (object)[];
        }
    }

    /**
     * @param int $developerAppId
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/20 14:53
     * memo : null
     */
    public static function pullTapdAccountDetailCache(int $developerAppId): array
    {
        $builder = DB::connection('exception')->table('tapd_account');
        return (array)($builder
                ->where('developer_app_id', $developerAppId)
                ->where('bind_status', 1)
                ->get()->toArray()[0] ?? []);
    }

    /**
     * @param int $developerAppId
     * @param string $tapdBugIdList
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/20 17:34
     * memo : 獲取缺陷狀態列表
     */
    public function pullBugStatusList(int $developerAppId, string $tapdBugIdList): array
    {
        $tapdAccountDetail = self::pullTapdAccountDetailCache($developerAppId);
        if (empty($tapdAccountDetail)) { // 未绑定项目id
            $tapdBugIdList = explode(',', $tapdBugIdList);
            $data = [];
            foreach ($tapdBugIdList as $item) {
                $data[] = [
                    "Bug" => [
                        "id" => $item,
                        "status" => "item_untie_status"
                    ]
                ];
            }
        } else {
            $result = (new TapdService())->pullBugStatus($tapdAccountDetail['item_id'], $tapdBugIdList);
            $data = $result['data'] ?? [];
        }
        //format[START]
        $format = [];
        foreach ($data as $value){
            $format[$value['Bug']['id']] = $value['Bug']['status'];
        }
        self::updateBugStatus($format);

        // 从tapd获取工作流状态中英文对应关系
        if (!empty($tapdAccountDetail)) {
            $result = TapdService::get(TapdService::TAPD_GET_WORK_FLOWS_STATUS_MAP_URL,
                ['workspace_id' => $tapdAccountDetail['item_id'], 'system' => 'bug']);
        }
        // 封装信息
        $format = self::addMessage($result['data'] ?? [], $format);
        return $format;
        //format[END]
    }

    /**
     * @param int $developerAppId
     * @param string $exceptionBlockId
     * @return string
     * author : <EMAIL>
     * datetime: 2023/07/20 17:32
     * memo : 根據$exceptionBlockId獲取缺陷狀態
     */
    public function pullBugStatusByExceptionBlockId(int $developerAppId, string $exceptionBlockId): string
    {
        $builder = DB::connection('exception')->table('tapd_bug');
        $tapdBugResult = $builder
            ->select('developer_app_id', 'tapd_bug_id')
            ->where('developer_app_id', $developerAppId)
            ->where('exception_block_id', $exceptionBlockId)
            ->first();
        $tapdAccountResult = self::pullTapdAccountDetailCache($tapdBugResult['developer_app_id']);
        $response = (new TapdService())->pullBugStatus($tapdAccountResult['item_id'], $tapdBugResult['tapd_bug_id']);
        return $response[0]['Bug']['status'];
    }

    /**
     * 获取tapd项目处理人
     * @param array $params
     * @return array
     */
    public function getTapdHandlers(array $params)
    {
        $data = TapdService::get(TapdService::TAPD_GET_WORKSPACES_USERS, ['workspace_id' => $params['item_id']]);
        if (empty($data['data'])) return [];
        $users = [];
        foreach ($data['data'] as $item) {
            $user = ['user' => $item['UserWorkspace']['user'] ?? ''];
            $users[] = $user;
        }
        return $users;
    }

    /**
     * 获取tapd项目迭代列表
     * @param array $params
     * @return array
     */
    public function getTapdIteration(array $params)
    {
        $data = TapdService::get(TapdService::TAPD_GET_ITERATIONS_URL, ['workspace_id' => $params['item_id']]);
        if (empty($data['data'])) return [];
        $iterations = [];
        foreach ($data['data'] as $item) {
            $iteration = ['iteration_id' => $item['Iteration']['id'] ?? 0, 'iteration_name' => $item['Iteration']['name'] ?? ''];
            $iterations[] = $iteration;
        }
        return $iterations;
    }

    /**
     * 获取tapd项目发布计划列表
     * @param array $params
     * @return array
     */
    public function getTapdReleases(array $params)
    {
        $data = TapdService::get(TapdService::TAPD_GET_RELEASES_URL, ['workspace_id' => $params['item_id']]);
        if (empty($data['data'])) return [];
        $releases = [];
        foreach ($data['data'] as $item) {
            $release = ['release_id' => $item['Release']['id'] ?? 0, 'release_name' => $item['Release']['name'] ?? ''];
            $releases[] = $release;
        }
        return $releases;
    }

    /**
     * 获取tapd项目发现版本
     * @param array $params
     * @return array
     */
    public function getTapdVersions(array $params)
    {
        $data = TapdService::get(TapdService::TAPD_GET_VERSIONS_URL, ['workspace_id' => $params['item_id']]);
        if (empty($data['data'])) return [];
        $versions = [];
        foreach ($data['data'] as $item) {
            $version = ['version_id' => $item['Version']['id'] ?? 0, 'version_name' => $item['Version']['name']?? ''];
            $versions[] = $version;
        }
        return $versions;
    }

    /**
     * 获取tap工作流中英文名对应关系
     * @param array $statusMap
     * @param array $data
     * @return array
     */
    public function addMessage(array $statusMap, array $data)
    {
        $format = [];
        foreach ($data as $key => $value) {
            $format[$key]['status'] = $value;
            $format[$key]['label'] = $statusMap[$value] ?? '';
        }
        return $format;
    }
}
