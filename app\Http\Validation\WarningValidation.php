<?php

/**
 * 表单校验
 * @desc 表单校验
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/06/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

/**
 * @method static WarningValidation build()
 */
class WarningValidation extends BaseValidation
{
    /**
     * 效能后台ID
     *
     * @return $this
     */
    public function developerAppId(): WarningValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 预警规则ID
     *
     * @return $this
     */
    public function warningId(): WarningValidation
    {
        $this->rules['warning_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 预警报告ID
     *
     * @return $this
     */
    public function warningRecordId(): WarningValidation
    {
        $this->rules['warning_record_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * （非必选）预警报告ID
     *
     * @return $this
     */
    public function notWarningRecordId(): WarningValidation
    {
        $this->rules['warning_record_id'] = 'integer|min:1';
        return $this;
    }

    /**
     * 页码
     *
     * @return $this
     */
    public function page(): WarningValidation
    {
        $this->rules['page'] = 'int';
        return $this;
    }

    /**
     * 分页大小
     *
     * @return $this
     */
    public function perPage(): WarningValidation
    {
        $this->rules['per_page'] = 'int';
        return $this;
    }
}
