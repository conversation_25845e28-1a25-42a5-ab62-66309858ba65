<?php

namespace App\Service;

use App\Components\Scope\CrashScope;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\VersionWhiteList;

class VersionWhiteListService extends WhiteListService
{
    const ADD_VERSIONS_LIST = 0; // 隐藏操作
    const DELETE_VERSIONS_LIST = 1; // 显示操作

    /**
     * @var CrashScope
     */
    private $scope;
    private $query;

    public function __construct($params)
    {
        parent::__construct($params);
        $this->scope = new CrashScope($params);
        $this->query = (new UserLogDataAll())::query();
        isset($this->params['condition']) && $this->params['condition'] = explode(',', $this->params['condition']);
        isset($this->params['versions']) && $this->params['versions'] = explode(',', $this->params['versions']);
    }

    public function hiddenWhiteList()
    {
        $result = VersionWhiteList::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->when(isset($this->params['condition']), function ($query) {
                $query->whereIn('app_version', $this->params['condition']);
            })
            ->paginate($this->limit, ['app_version'], 'page', $this->page);
        $total = $result->total();
        $list = $result->items();
        return compact('total', 'list');
    }

    public function displayWhiteList()
    {
        // 获取隐藏列表的版本集合
        $result = VersionWhiteList::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->get()
            ->pluck('app_version');

        $builder = $this->query->tap(function ($query) {
            $this->scope->getBuilder($query);
        });

        $total = \ClickHouse::getSqlData(
            'select count(1) as count from (' .
            \ClickHouse::getSqlBindings(
                (clone $builder)
                    ->selectRaw('app_version')
                    ->when(isset($this->params['condition']), function ($query) {
                        $query->whereIn('app_version', $this->params['condition']);
                    })
                    ->whereNotIn('app_version', $result->toArray())
                    ->groupBy('app_version')
            ) . ') t limit 1'
        )[0]['count'] ?? 0;

        $list = (clone $builder)
            ->selectRaw('distinct app_version')
            ->when(isset($this->params['condition']), function ($query) {
                $query->whereIn('app_version', $this->params['condition']);
            })
            ->whereNotIn('app_version', $result->toArray())
            ->forPage($this->page, $this->limit)
            ->getFromCK();
        return compact('total', 'list');
    }

    /**
     * 操作版本白名单数据库
     */
    public function operateWhiteList()
    {
        if ($this->params['operate_type'] == self::ADD_VERSIONS_LIST) {
            foreach ($this->params['versions'] as $value) {
                $data = [
                    'developer_app_id' => $this->params['developer_app_id'],
                    'app_version' => $value
                ];
                $version = new VersionWhiteList($data);
                // 操作数据库添加版本白名单
                $version->save();
            }
        } else if ($this->params['operate_type'] == self::DELETE_VERSIONS_LIST) {
            // 操作数据库删除版本白名单
            foreach ($this->params['versions'] as $value) {
                $version = VersionWhiteList::query()
                    ->where('developer_app_id', $this->params['developer_app_id'])
                    ->where('app_version', $value);
                // 操作数据库添加版本白名单
                $version->delete();
            }
        }
    }
}


