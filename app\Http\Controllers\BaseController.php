<?php
/**
 * 基本控制器
 * BaseController.php
 *
 * User: Dican
 * Date: 2022/9/19
 * Email: <<EMAIL>>
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BaseController extends Controller
{
    public $request;

    public function __construct(Request $request)
    {
        //访问的 应用id
        $appId = $request->developer_app_id ?? 1;
        /*
        //判断是否具有该应用的推送权限
        $permission = $appId . '-app';
        $this->middleware(['role_or_permission:super-admin|' . $permission]);//DEBUG_LABEL
        */
        $this->middleware(['appIdAuthenticate:' . $appId]);
        $this->request = $request->all();
    }
}
