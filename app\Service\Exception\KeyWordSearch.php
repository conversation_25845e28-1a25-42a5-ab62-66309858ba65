<?php

/**
 * 关键词搜索
 * @desc 关键词搜索
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception;

trait KeyWordSearch
{
    /**
     * 关键词搜索
     *
     * @param $builder
     * @param string $subSql
     * @param array $params
     * @param string $opt
     * @return void
     */
    protected function keyWordSearch($builder, string $subSql, array $params, string $opt = 'in'): void
    {
        $builder->where(function ($query) use ($subSql, $params, $opt) {
            $likeService = new LikeService($subSql);
            if (isset($params['extra_app_id'])) {
                $likeService->setExtraAppId($params['extra_app_id']);
            }
            if (isset($params['event_name'])) {
                $likeService->setEventName($params['event_name']);
            }
            if (!empty($params['start_date'])) {
                $likeService->setStartDate($params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $likeService->setEndDate($params['end_date']);
            }
            $query->whereRaw("exception_block_id {$opt} ({$likeService->likeToSQL()})");
        });
    }
}
