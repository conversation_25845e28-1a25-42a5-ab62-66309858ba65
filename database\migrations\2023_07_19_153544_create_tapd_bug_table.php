<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTapdBugTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('tapd_bug', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->string('exception_block_id', 32)->default('')->comment('异常类型分组id');
            $table->unsignedBigInteger('tapd_account_id')->default(0)->comment('tap配置表id');
            $table->string('tapd_bug_id', 255)->default('')->comment('tapd创建缺陷返回id');
            $table->unsignedTinyInteger('type')->default(0)->comment('异常类型,1为崩溃异常,2为错误异常');
            $table->text('detail')->nullable()->comment('缺陷单详情');
            $table->text('tapd_link')->nullable()->comment('跳转至tapd链接');
            $table->unsignedTinyInteger('create_type')->default(0)->comment('创建类型,1为手动创建,2为关联创建');
            $table->unsignedBigInteger('creator_id')->default(0)->comment('创建人id');
            $table->unsignedTinyInteger('bind_status')->default(1)->comment('是否解绑tapd,1为已绑定,0为已解绑');
            $table->string('status', 32)->default('')->comment('处理状态');
            $table->timestamps();
            $table->index('exception_block_id');
            $table->index('tapd_bug_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `tapd_bug` comment 'tapd缺陷表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('tapd_bug');
    }
}
