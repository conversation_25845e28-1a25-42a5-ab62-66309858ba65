<?php

namespace App\Jobs;

use App\Http\Logic\HomeLogic;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class LoadHomeDataCacheJob implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * @var int
     */
    public $timeout = 1800;

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {
        try {
            (new HomeLogic())->loadHomeDataCache();
        } catch (\Throwable $e) {
            \Log::error('loadHomeDataCache:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

}
