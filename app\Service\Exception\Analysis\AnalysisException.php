<?php

/**
 * 分析异常报告
 * @desc 分析异常报告
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Model\BaseModel;
use App\Model\ClickHouse\AnalysisException as AnalysisExceptionModel;
use App\Service\Exception\BaseService;
use Illuminate\Support\Facades\DB;

class AnalysisException extends BaseService
{
    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;

    /**
     * 异常ID
     *
     * @var string
     */
    private $exceptionBlockId;

    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->extraAppId = $request->input('developer_app_id');
        $this->exceptionBlockId = strtoupper($request->input('exception_block_id'));
        $this->eventName = self::EVENT_NAME[$request->input('type')] ?? '';
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        // 获取待查询的query
        $queries = [
            'app_version' => $this->getAppVersionQuery(),
            'os_version' => $this->getOsVersionQuery(),
            'device_model' => $this->getDeviceModelQuery(),
            'use_duration' => $this->getUseDurationQuery(),
            'extra' => $this->getExtraQuery(),
            'emulator' => $this->getEmulatorQuery(),
            'images' => $this->getImageQuery(),
        ];
        // 执行查询
        $results = (new ClickHouse())->getMultiSqlData($queries);
        // 返回结果
        return array_merge([
            'app_version' => $this->handleAppVersion($results['app_version']),
            'min_app_version' => $this->handleMinAppVersion($results['app_version']),
            'os_version' => $this->handleOsVersion($results['os_version']),
            'device_model' => $this->handleDeviceModel($results['device_model']),
            'use_duration' => $this->handleUseDuration($results['use_duration']),
            'emulator' => $this->handleEmulator($results['emulator']),
            'images' => $this->handleImage($results['images']),
        ], $this->handleExtra($results['extra']));
    }

    /**
     * 获取APP版本的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getAppVersionQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw('app_version, count(*) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->where('app_version', '!=', '')
            ->groupBy('app_version')
            ->orderBy('num', 'desc');
    }

    /**
     * 处理APP版本数据
     *
     * @param $data
     * @return array
     */
    private function handleAppVersion($data)
    {
        // 把data里面所有 num 字段数据进行相加
        $num = 0;
        foreach ($data as $item) {
            $num += $item['num'];
        }
        // 计算每个app_version的占比
        foreach ($data as &$item) {
            $item['rate'] = round(bcdiv($item['num'], $num, 4) * 100, 2);
        }
        return $data;
    }

    /**
     * 处理最小APP版本数据
     *
     * @param $data
     * @return array
     */
    private function handleMinAppVersion($data)
    {
        // 根据app_version排序
        usort($data, function ($a, $b) {
            return version_compare($a['app_version'], $b['app_version']);
        });
        // 获取最小版本那个
        return $data[0]['app_version'] ?? '';
    }

    /**
     * 获取APP版本的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getOsVersionQuery()
    {
        return (new AnalysisExceptionModel())
            ->selectRaw('exception_analysis.os_version, exception_stream_all.os_type, count(*) as num')
            ->join('exception_stream_all', 'exception_stream_all.exception_merge_id', '=', 'exception_analysis.exception_merge_id')
            ->where('exception_analysis.extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_analysis.exception_block_id)'), $this->exceptionBlockId)
            ->where('exception_analysis.event_name', $this->eventName)
            ->where('exception_analysis.os_version', '!=', '')
            ->groupBy('exception_analysis.os_version', 'exception_stream_all.os_type')
            ->orderBy('num', 'desc');
    }

    /**
     * 处理系统版本数据
     *
     * @param $data
     * @return array
     */
    private function handleOsVersion($data)
    {
        // 把data里面所有 num 字段数据进行相加
        $num = 0;
        foreach ($data as $item) {
            $num += $item['num'];
        }
        // 计算每个app_version的占比
        foreach ($data as &$item) {
            $item['rate'] = round(bcdiv($item['num'], $num, 4) * 100, 2);
            // 处理 os_version的名称 通过 manufacturer 判断是否 是安卓还是苹果
            if ($item['os_type'] == BaseModel::ANDROID) {
                $item['os_version'] = "Android {$item['os_version']}";
            } elseif ($item['os_type'] == BaseModel::IOS) {
                $item['os_version'] = "IOS {$item['os_version']}";
            } elseif ($item['os_type'] == BaseModel::PC) {
                $item['os_version'] = "PC {$item['os_version']}";
            } elseif ($item['os_type'] == BaseModel::MINI) {
                $item['os_version'] = "小程序 {$item['os_version']}";
            } elseif ($item['os_type'] == BaseModel::HARMONY) {
                $item['os_version'] = "鸿蒙 {$item['os_version']}";
            }
        }
        return $data;
    }

    /**
     * 获取APP版本的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getDeviceModelQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw('manufacturer, device_model, count(*) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->where('manufacturer', '!=', '')
            ->where('device_model', '!=', '')
            ->groupBy('manufacturer', 'device_model');
    }

    /**
     * 处理系统版本数据
     *
     * @param $data
     * @return array
     */
    private function handleDeviceModel($data)
    {
        // 把data里面所有 num 字段数据进行相加
        $num = 0;
        foreach ($data as $item) {
            $num += $item['num'];
        }
        // 对品牌进行分组
        $newData = [];
        foreach ($data as $item) {
            $newData[$item['manufacturer']]['num'] = $item['num'] + ($newData[$item['manufacturer']]['num'] ?? 0);
            $newData[$item['manufacturer']]['list'][] = $item;
            $newData[$item['manufacturer']]['name'] = $item['manufacturer'];
        }
        // 计算每个app_version的占比
        foreach ($newData as &$item) {
            $item['rate'] = round(bcdiv($item['num'], $num, 4) * 100, 2);
            // 并对item['list']进行排序, 按 num 字段，从大到小排序
            usort($item['list'], function ($a, $b) {
                return $b['num'] <=> $a['num'];
            });
        }
        usort($newData, function ($a, $b) {
            return $b['num'] <=> $a['num'];
        });
        return array_values($newData);
    }

    /**
     * 获取APP版本的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getUseDurationQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw("COUNT(CASE WHEN use_duration <= 10 THEN 1 END) AS '10', COUNT(CASE WHEN use_duration > 10 AND use_duration <= 30 THEN 1 END) AS '30', COUNT(CASE WHEN use_duration > 30 AND use_duration <= 60 THEN 1 END) AS '60', COUNT(CASE WHEN use_duration > 300 THEN 1 END) AS '300'")
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName);
    }

    /**
     * 处理系统版本数据
     *
     * @param $data
     * @return array
     */
    private function handleUseDuration($data)
    {
        $data = $data[0] ?? [];
        // 组装数据
        $data = [
            [
                'name' => '10秒以下(包含)',
                'num' => $data['10'] ?? 0,
            ],
            [
                'name' => '10-30秒以下(包含30)',
                'num' => $data['30'] ?? 0,
            ],
            [
                'name' => '30-60秒以下(包含60)',
                'num' => $data['60'] ?? 0,
            ],
            [
                'name' => '300秒以上(不包含)',
                'num' => $data['300'] ?? 0,
            ],
        ];
        // 根据data 里面的 num 字段进行排序，按从大到小排
        usort($data, function ($a, $b) {
            return $b['num'] <=> $a['num'];
        });
        // 把data里面所有 num 字段数据进行相加
        $num = 0;
        foreach ($data as $item) {
            $num += $item['num'];
        }
        $newData = [];
        // 计算每个app_version的占比
        foreach ($data as $item) {
            if ($item['num'] == 0) {
                continue;
            }
            $item['rate'] = round(bcdiv($item['num'], $num, 4) * 100, 2);
            $newData[] = $item;
        }
        return $newData;
    }

    /**
     * 获取扩展的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getExtraQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw("min(role_level) as min_role_level, max(role_level) as max_role_level, min(daily_active) as min_daily_active, max(daily_active) as max_daily_active, min(power) as min_power, max(power) as max_power")
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName);
    }

    /**
     * 处理扩展数据
     *
     * @param $data
     * @return array
     */
    private function handleExtra($data)
    {
        return [
            'role_level' => [
                'min' => $data[0]['min_role_level'] ?? 0,
                'max' => $data[0]['max_role_level'] ?? 0,
            ],
            'daily_active' => [
                'min' => $data[0]['min_daily_active'] ?? 0,
                'max' => $data[0]['max_daily_active'] ?? 0,
            ],
            'power' => [
                'min' => $data[0]['min_power'] ?? 0,
                'max' => $data[0]['max_power'] ?? 0,
            ],
        ];
    }

    /**
     * 获取模拟器真机的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getEmulatorQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw('is_emulator, count(*) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->groupBy('is_emulator')
            ->orderBy('num', 'desc');
    }

    /**
     * 处理模拟器真机数据
     *
     * @param $data
     * @return array
     */
    private function handleEmulator($data)
    {
        // 把data里面所有 num 字段数据进行相加
        $num = 0;
        foreach ($data as $item) {
            $num += $item['num'];
        }
        // 计算占比
        foreach ($data as &$item) {
            $item['rate'] = round(bcdiv($item['num'], $num, 4) * 100, 2);
        }
        return $data;
    }

    /**
     * 获取图片的查询query
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getImageQuery()
    {
        return (new AnalysisExceptionModel())->selectRaw("exception_image")
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->where('exception_image', '!=', '')
            ->limit(10);
    }

    /**
     * 处理图片数据
     *
     * @param $data
     * @return array
     */
    private function handleImage($data)
    {
        return array_column($data, 'exception_image');
    }
}
