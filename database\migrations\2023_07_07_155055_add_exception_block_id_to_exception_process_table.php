<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddExceptionBlockIdToExceptionProcessTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('exception_process', function (Blueprint $table) {
            $table->string('exception_block_id', 32)->default('')->comment('异常类型分组id');
            $table->index('exception_block_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('exception_process', function (Blueprint $table) {
            $table->dropColumn('exception_block_id');
            $table->dropIndex(['exception_block_id']); // 删除索引
        });
    }
}
