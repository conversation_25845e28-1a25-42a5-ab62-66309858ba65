<?php

namespace App\Jobs;

use App\Components\ClickHouse\ClickHouse;
use App\Components\Helper\CommonHelper;
use App\Components\Helper\VoiceHelper;
use App\Components\HttpAgent;
use App\Components\OauthCenter\OauthCenterApi;
use App\Model\BaseModel;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Warning;
use App\Service\WXGroupNoticeService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Model\VersionWhiteList;
use Illuminate\Database\Eloquent\Builder;
use App\Model\Record;

class WarningReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 脚本执行的时间
     *
     * @var string
     */
    const DAILY_TIME = '10:00';

    /**
     *  间隔时间
     *
     * @var int
     */
    const INTERVAL = 7200;

    /**
     * 处理程序
     *
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            $this->warningReportMonitor();
        } catch (\Exception $e) {
            Log::error('WarningReportJob:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * 预警监控
     *
     * @return true
     */
    public function warningReportMonitor(): bool
    {
        $ableWarningList = $this->getWarningList();
        foreach ($ableWarningList as &$eachWarning) {
            $reportData = $this->getWarningReportData($eachWarning);
            $status = $this->send($eachWarning, $reportData);
            if ($status) {
                $this->updateWarning($eachWarning);
            }
        }
        return true;
    }

    /**
     * 获取大盘监控列表
     *
     * @return array
     */
    public function getWarningList(): array
    {
        $list = Warning::query()
            ->where('status', Warning::START)
            ->where('monitor_type', Warning::REPORT_MONITOR)
            ->orderByDesc('created_at')
            ->get();
        $apps = DB::connection('developer')
            ->table("apps")
            ->pluck('app_name', 'id');
        $warnings = [];
        foreach ($list as &$eachWarning) {
            //最后一次执行时间距离当前时间少于指定时间间隔，则不再执行
            if (time() - strtotime($eachWarning->last_warning_time) < self::INTERVAL) {
                continue;
            }
            // 判断预警执行周期
            if (!empty($eachWarning->schedule_time) && $eachWarning->schedule_time == 604800) {
                // 判断是否周一
                if (Carbon::now()->dayOfWeek != 1) {
                    continue;
                }
            }
            //赋值应用名称
            $eachWarning->developer_app_name = $apps[$eachWarning->developer_app_id] ?? '';
            //加入到数组中
            $warnings[] = $eachWarning;
        }
        return $warnings;
    }

    /**
     * 获取大盘报告数据
     *
     * @param Warning $eachWarning
     * @return array
     */
    public function getWarningReportData(Warning $eachWarning): array
    {
        $startTime = date('Y-m-d', strtotime("-1 day"));
        $endTime = $startTime;
        $isDay = true;
        $oldStartTime = $oldEndTime = Carbon::parse($startTime)->subDays(1)->toDateString();
        // 判断预警执行周期
        if (!empty($eachWarning['schedule_time']) && $eachWarning['schedule_time'] == 604800) {
            $isDay = false;
            $startTime = Carbon::parse($startTime)->subDays(6)->toDateString();
            $oldEndTime = Carbon::parse($oldEndTime)->subDays(6)->toDateString();
            $oldStartTime = Carbon::parse($oldEndTime)->subDays(6)->toDateString();
        }
        $date = [
            'current' => [
                $startTime,
                $endTime,
            ],
            'old' => [
                $oldStartTime,
                $oldEndTime,
            ],
        ];
        $result = $this->getWarningReportDataDetail($date, $eachWarning);
        // 计算环比
        $data = [];
        $keys = ['crashCommonNum', 'crashCommonRate', 'errorCommonNum', 'errorCommonRate', 'connectDeviceNum', 'crashDeviceRate', 'errorDeviceRate'];
        foreach ($keys as $key) {
            $data[$key] = $result['current'][$key];
            if ($result['old'][$key] > 0) {
                $data[$key . '_ratio'] = bcadd(round(bcmul(bcdiv(bcsub($data[$key], $result['old'][$key], 8), $result['old'][$key], 8), 100, 6), 2), 0, 2);
            } else {
                $data[$key . '_ratio'] = bcadd(round(bcmul($data[$key], 100, 6), 2), 0, 2);
            }
            // 判断上升还是下降
            if ($data[$key . '_ratio'] > 0) {
                $data[$key . '_ratio_text'] = "<font color='red'>↑" . abs($data[$key . '_ratio']) . "%</font>";
            } else {
                $data[$key . '_ratio_text'] = "<font color='info'>↓" . abs($data[$key . '_ratio']) . "%</font>";
            }
        }
        $data['isDay'] = $isDay;
        return $data;
    }

    /**
     * 获取数据明细
     *
     * @param array $dates
     * @param Warning $eachWarning
     * @return array
     */
    private function getWarningReportDataDetail(array $dates, Warning $eachWarning): array
    {
        //过滤白名单
        $appVersionWhite = $this->getAppVersionWhite($eachWarning->developer_app_id);
        foreach ($dates as $key => $val) {
            $builder = (new UserLogDataAll())->where('stream_date', '>=', $val[0])
                ->where('stream_date', '<=', $val[1])
                ->when($eachWarning->app_version, function ($query, $appVersion) {
                    return $query->whereIn('app_version', $appVersion);
                })
                ->when($eachWarning->os_type, function ($query, $osType) {
                    return $query->where('os_type', $osType);
                })
                ->where('extra_app_id', $eachWarning->developer_app_id)
                ->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[6]);

            $crashBlockIdWhite = $this->getExceptionBlockIdWhite($eachWarning->developer_app_id, 1);
            $errorBlockIdWhite = $this->getExceptionBlockIdWhite($eachWarning->developer_app_id, 2);
            $queries[$key . 'start'] = (clone $builder)
                ->selectRaw('count(*) AS num, count(distinct `server_dev_str`) AS dev_num')
                ->where('event_name', 'exception_start');
            $queries[$key . 'start_dev'] = (clone $builder)
                ->selectRaw('count(*) AS num, count(distinct `server_dev_str`) AS dev_num');
            $queries[$key . 'crash'] = (clone $builder)
                ->selectRaw('count(*) AS num, count(distinct `server_dev_str`) AS dev_num')
                ->where('event_name', 'exception_crash')
                ->when($appVersionWhite, function (Builder $query, $appVersionWhite) { //排除白名单版本
                    return $query->whereNotIn('app_version', $appVersionWhite);
                })
                ->when($crashBlockIdWhite, function (Builder $query, $crashBlockIdWhite) { //排除白名单异常
                    return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map('strtoupper', $crashBlockIdWhite));
                });
            $queries[$key . 'error'] = (clone $builder)
                ->selectRaw('count(*) AS num, count(distinct `server_dev_str`) AS dev_num')
                ->where('event_name', 'exception_error')
                ->when($appVersionWhite, function (Builder $query, $appVersionWhite) { //排除白名单版本
                    return $query->whereNotIn('app_version', $appVersionWhite);
                })
                ->when($errorBlockIdWhite, function (Builder $query, $errorBlockIdWhite) { //排除白名单异常
                    return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map('strtoupper', $errorBlockIdWhite));
                });
            if ($eachWarning->stream_time_filter == 1 && $eachWarning->stream_time_filter_hours != 0) {
                $queries[$key . 'crash'] = $queries[$key . 'crash']->whereRaw(sprintf('upper(exception_block_id) in (select upper(exception_block_id)_id from exception_stat_all_v2 where min_stream_time >= %d)', strtotime("-{$eachWarning->stream_time_filter_hours} hours")));
                $queries[$key . 'error'] = $queries[$key . 'error']->whereRaw(sprintf('upper(exception_block_id) in (select upper(exception_block_id) from exception_stat_all_v2 where min_stream_time >= %d)', strtotime("-{$eachWarning->stream_time_filter_hours} hours")));
            }
        }

        $results = (new ClickHouse())->getMultiSqlData($queries);

        $data = [];
        $keys = array_keys($dates);
        foreach ($keys as $key) {
            $crashList = $results[$key . 'crash'];
            $errorList = $results[$key . 'error'];
            $startList = $results[$key . 'start'];
            $startDevList = $results[$key . 'start_dev'];
            $crashCommonNum = $crashList[0]['num'] ?? 0;
            $crashDeviceNum = $crashList[0]['dev_num'] ?? 0;
            $errorCommonNum = $errorList[0]['num'] ?? 0;
            $errorDeviceNum = $errorList[0]['dev_num'] ?? 0;
            $startCommonNum = $startList[0]['num'] ?? 0;
            $startDeviceNum = $startDevList[0]['dev_num'] ?? 0;
            $data[$key] = [
                'crashCommonNum' => $crashCommonNum,
                'crashCommonRate' => $startCommonNum ? number_format($crashCommonNum / $startCommonNum * 100, 2, '.', '') : '0.00',
                'errorCommonNum' => $errorCommonNum,
                'errorCommonRate' => $startCommonNum ? number_format($errorCommonNum / $startCommonNum * 100, 2, '.', '') : '0.00',
                'connectDeviceNum' => $startList[0]['dev_num'] ?? 0,
                'crashDeviceRate' => $startDeviceNum ? number_format($crashDeviceNum / $startDeviceNum * 100, 2, '.', '') : '0.00',
                'errorDeviceRate' => $startDeviceNum ? number_format($errorDeviceNum / $startDeviceNum * 100, 2, '.', '') : '0.00',
            ];
        }
        return $data;
    }

    /**
     * 發送通知
     *
     * @param Warning $warning
     * @param array $eachWarningReport
     * @return bool
     */
    private function send(Warning $warning, array $eachWarningReport): bool
    {
        try {
            if (!$eachWarningReport) {
                return false;
            }
            //推送的信息
            //版本
            $version = $warning->getAppVersion();
            //平台
            $osType = $warning->getOsType();
            //类型
            $exceptionType = $warning->getExceptionType();
            $params = [
                $eachWarningReport['isDay'] ? config("message.warning_{$exceptionType}_report") : config("message.warning_{$exceptionType}_report_week"),
                $warning->name,
                date('Y-m-d H:i:s'),
                $warning->developer_app_name,
                $osType,
                $version,
            ];
            $advice = "";
            $no = 1;
            $types = [];
            if (in_array($exceptionType, [Warning::EXCEPTION_TYPE_CRASH, Warning::EXCEPTION_TYPE_ALL])) {
                $types[] = 'crash';
                $params[] = $eachWarningReport['connectDeviceNum'];
                // $params[] = $eachWarningReport['connectDeviceNum'] . "({$eachWarningReport['connectDeviceNum_ratio_text']})";
                $params[] = $eachWarningReport['crashCommonNum'] . "({$eachWarningReport['crashCommonNum_ratio_text']})";
                $params[] = $eachWarningReport['crashCommonRate'] . "%({$eachWarningReport['crashCommonRate_ratio_text']})";
                $params[] = $eachWarningReport['crashDeviceRate'] . "%({$eachWarningReport['crashDeviceRate_ratio_text']})";

                // $params[] = $eachWarningReport['connectDeviceNum'];
                // $params[] = $eachWarningReport['crashCommonNum'];
                // $params[] = $eachWarningReport['crashCommonRate'] . "%";
                // $params[] = $eachWarningReport['crashDeviceRate'] . "%";
                if ($eachWarningReport['crashCommonRate_ratio'] > 0) {
                    $advice .= "{$no}、崩溃率环比上升{$eachWarningReport['crashCommonRate_ratio']}%，请尽快定位崩溃原因，并采取相应措施进行修复。\n";
                } else {
                    $advice .= "{$no}、崩溃率环比下降{$eachWarningReport['crashCommonRate_ratio']}%，崩溃修复效果良好，请继续保持当前的优化工作。\n";
                }
                $no++;
            }
            if (in_array($exceptionType, [Warning::EXCEPTION_TYPE_ERROR, Warning::EXCEPTION_TYPE_ALL])) {
                $types[] = 'error';
                $params[] = $eachWarningReport['connectDeviceNum'] . "";
                // $params[] = $eachWarningReport['connectDeviceNum'] . "({$eachWarningReport['connectDeviceNum_ratio_text']})";
                $params[] = $eachWarningReport['errorCommonNum'] . "({$eachWarningReport['errorCommonNum_ratio_text']})";
                $params[] = $eachWarningReport['errorCommonRate'] . "%({$eachWarningReport['errorCommonRate_ratio_text']})";
                $params[] = $eachWarningReport['errorDeviceRate'] . "%({$eachWarningReport['errorDeviceRate_ratio_text']})";

                // $params[] = $eachWarningReport['connectDeviceNum'] . "";
                // $params[] = $eachWarningReport['errorCommonNum'] . "";
                // $params[] = $eachWarningReport['errorCommonRate'] . "%";
                // $params[] = $eachWarningReport['errorDeviceRate'] . "%";
                if ($eachWarningReport['errorCommonRate_ratio'] > 0) {
                    $advice .= "{$no}、错误率环比上升{$eachWarningReport['errorCommonRate_ratio']}%，请尽快定位错误原因，并采取相应措施进行修复。\n";
                } else {
                    $eachWarningReport['errorCommonRate_ratio'] = abs($eachWarningReport['errorCommonRate_ratio']);
                    $advice .= "{$no}、错误率环比下降{$eachWarningReport['errorCommonRate_ratio']}%，错误修复效果良好，请继续保持当前的优化工作。\n";
                }
                $no++;
            }
            // 判断联网设备是否为 0，如果为 0，则不发送通知
            if ($eachWarningReport['connectDeviceNum'] == 0) {
                return false;
            }
            // $params[] = $advice;
            $startTime = $endTime = date('Y-m-d', strtotime('-1 day'));
            // 判断是否周报
            if (!$eachWarningReport['isDay']) {
                $startTime = Carbon::parse(date('Y-m-d', strtotime('-1 day')))->subDays(6)->toDateString();
                $params[] = $startTime;
            }
            $params[] = $endTime;
            $message = sprintf(...$params);
            //判断是否有接收人，有则推送
            if (!empty($warning->receiving_person)) {
                $newMessage = str_replace("<font color='info'>", '', $message);
                $newMessage = str_replace("<font color='red'>", '', $newMessage);
                $newMessage = str_replace("</font>", '', $newMessage);
                $params = '?range=[%22' . $startTime . '+00:00:00%22,%22' . $endTime . '+23:59:59%22]&click_type=warning_push';
                $newMessage .= "<a href='{$this->getUrl($warning,$params)}'>【查看详情】</a>";
                (new OauthCenterApi)->sendWXMsg($warning->receiving_person, str_replace('**', '', $newMessage));
            }
            //判断是否有推送的机器人地址，有则推送
            if (!empty($warning->receiving_group)) {
                // 判断是否周报，如果是周报，则排行截图
                if (!$eachWarningReport['isDay']) {
                    $rankImg = $this->getRankImg($startTime, $endTime, $types);
                    foreach ($warning->receiving_group as $receive) {
                        $this->sendImg($rankImg, $receive['url']);
                    }
                }
                foreach ($warning->receiving_group as $receive) {
                    $service = new WXGroupNoticeService($receive['url'], $receive['is_mentioned']);
                    $params = '?range=[%22' . $startTime . '+00:00:00%22,%22' . $endTime . '+23:59:59%22]&click_type=warning_push';
                    $message .= "[【查看详情】]({$this->getUrl($warning,$params)})";
                    $service->wxGroupNotify($message, 'markdown');
                }
            }
            //判断是否有手机号，有则拨打电话
            if (!empty($warning->receiving_phone)) {
                VoiceHelper::callPhone([str_replace('**', '', $message)], $warning->receiving_phone, 1475525);
            }
            return true;
        } catch (\Throwable $e) {
            CommonHelper::xdebug($e);
            return false;
        }
    }

    /**
     * 更新通知時間+觸發次數
     *
     * @param Warning $warning
     * @return int
     */
    public function updateWarning(Warning $warning): int
    {
        return $warning->increment(
            'trigger_count',
            1,
            [
                'last_warning_time' => date('Y-m-d H:i:s'),
            ]
        );
    }

    /**
     * 获取推送跳转地址
     *
     * @return string
     */
    protected function getUrl(Warning $warning, $params = ''): string
    {
        // 拼接跳转的url
        $baseUrl = 'https://auth-pro-api.shiyue.com/oauth2/wechat-login?appId=1818223957164789760&gotoUri=https%3A%2F%2Fdeveloper-manager.shiyue.com%2Fauth-pro%3Fredirect_url%3D';

        if ($params) {
            $params .= '&auto_login=1';
        } else {
            $params .= '?auto_login=1';
        }

        return $baseUrl . base64_encode(config('app.front_url') . "/console/my-app/{$warning->developer_app_id}/app-monitor/exception-overview/crash-trend" . $params);
    }

    /**
     * 获取排行截图
     *
     * @return array
     */
    protected function getRankImg($startDate, $endDate, $types): array
    {
        try {
            $client = new Client([
                'timeout' => 3600,
                'connect_timeout' => 3600,
            ]);
            $response = $client->request('POST', 'https://fe-tool-api.shiyue.com/screenshot/getWebScreenShot', [
                'json' => [
                    'deviceScaleFactor' => 2,
                    'web_url' => "https://manage.shiyue.com/screenshot/performanceRank?dateRange={$startDate},{$endDate}&modules=" . implode(',', $types),
                    'width' => count($types) > 1 ? null : 520,
                ],
            ]);
            $result = $response->getBody()->getContents();
            $code = $response->getStatusCode(); // 200
            if ($code == 201 || $code == 200) {
                $data = json_decode($result, true);
                return $data;
            } else {
                Log::error("请求失败：" . $result);
            }
        } catch (\Throwable $e) {
            Log::error("请求失败：{$e->getMessage()}, {$e->getTraceAsString()}");
        }
        return [];
    }

    /**
     * 发送图片
     *
     * @param $data
     * @param $url
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function sendImg($data, $url)
    {
        HttpAgent::getInstance()->request('POST', $url, [
            'json' => [
                "msgtype" => "image",
                "image" => [
                    "base64" => $data['data']['image_data'],
                    "md5" => $data['data']['image_md5'],
                ]
            ]
        ]);
    }

    /**
     * 获取版本白名单
     *
     * @return array
     */
    protected function getAppVersionWhite($developer_app_id): array
    {
        $result = VersionWhiteList::query()
            ->where('developer_app_id', $developer_app_id)
            ->pluck('app_version')
            ->toArray();
        return $result;
    }

    /**
     * 获取白名单异常ID
     *
     * @param $type
     * @return array
     */
    protected function getExceptionBlockIdWhite($developer_app_id, $type): array
    {
        return Record::query()
            ->when($type, function (Builder $query, $type) {
                return $query->where('type', $type);
            })
            ->where('developer_app_id', $developer_app_id)
            ->where('is_add_white_list', 1)
            ->pluck('exception_block_id')
            ->toArray();
    }
}
