<?php

/**
 * Tapd服务基础类
 * @desc Tapd服务基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/04
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Tapd;

use App\Service\Exception\BaseService;
use Illuminate\Support\Facades\DB;

abstract class Base extends BaseService
{
    /**
     * TAPD 域名
     *
     * @var string
     */
    const TAP_BUG_VIEW_DOMAIN = 'https://www.tapd.cn/';

    /**
     * 获取账号详情
     *
     * @param int $developerAppId
     * @return array
     */
    public function getAccountDetail(int $developerAppId): array
    {
        $builder = DB::connection('exception')->table('tapd_account');
        return (array)($builder
            ->where('developer_app_id', $developerAppId)
            ->where('bind_status', 1)
            ->get()->toArray()[0] ?? []);
    }
}
