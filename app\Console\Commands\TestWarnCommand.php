<?php

/**
 * 测试预警脚本
 * @desc 测试预警脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/04/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Model\AppModel;
use App\Model\ExceptionHandlerWebhook;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestWarnCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:warn:command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试预警脚本';

    /**
     * 截图服务API地址
     * 
     * @var string
     */
    private const SCREENSHOT_API = 'https://fe-tool-api.shiyue.com/screenshot/getWebScreenShot';

    /**
     * 截图页面URL
     * 
     * @var string
     */
    private const SCREENSHOT_PAGE = 'https://manage.shiyue.com/screenshot/gameHandel';

    /**
     * 执行命令入口
     *
     * @return void
     */
    public function handle(): void
    {
        // 获取命令参数：频率
        $frequency = 1;

        // 获取符合条件的webhook配置列表
        $webhookConfigs = $this->getWebhookConfigs($frequency);

        // 获取所有应用，格式为 [id => app_name]
        $apps = $this->getApps();

        // 循环处理每个webhook配置
        foreach ($webhookConfigs as $config) {
            $this->processWebhook($config, $apps);
        }
    }

    /**
     * 获取webhook配置列表
     *
     * @param string $frequency 频率
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getWebhookConfigs(string $frequency)
    {
        return ExceptionHandlerWebhook::query()
            ->where('frequency', $frequency)
            ->where('status', 1)
            ->where('id', 3)
            ->get();
    }

    /**
     * 获取应用列表
     *
     * @return array 应用列表，格式为 [id => app_name]
     */
    private function getApps(): array
    {
        return AppModel::query()->get()->pluck('app_name', 'id')->toArray();
    }

    /**
     * 处理单个webhook配置
     *
     * @param ExceptionHandlerWebhook $config webhook配置
     * @param array $apps 应用列表
     * @return void
     */
    private function processWebhook($config, array $apps): void
    {
        // 获取应用名称
        $appName = $apps[$config['developer_app_id']] ?? '';
        if (empty($appName)) {
            Log::warning("找不到应用ID: {$config['developer_app_id']}");
            return;
        }

        // 获取时间范围
        $dateRange = $this->getDateRange($config['range']);

        try {
            // 获取截图数据
            $screenshotData = $this->getScreenshot(
                $config['developer_app_id'],
                $appName,
                $dateRange['start_date'],
                $dateRange['end_date'],
                $config['sort_field']
            );

            // 发送图片到webhook
            if ($screenshotData) {
                $this->sendImageToWebhook($screenshotData, $config['webhook_urls']);
            }
        } catch (\Throwable $e) {
            Log::error("处理webhook异常：{$e->getMessage()}", [
                'app_id' => $config['developer_app_id'],
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 根据范围获取日期区间
     *
     * @param int $range 范围代码 1：昨天、2：最近7天、3：最近15天、4：最近30天
     * @return array ['start_date' => 'Y-m-d', 'end_date' => 'Y-m-d']
     */
    private function getDateRange(int $range): array
    {
        $today = date('Y-m-d');

        switch ($range) {
            case 1: // 昨天
                $startDate = date('Y-m-d', strtotime('-1 day'));
                return [
                    'start_date' => $startDate,
                    'end_date' => $startDate
                ];
            case 2: // 最近7天
                return [
                    'start_date' => date('Y-m-d', strtotime('-6 day')),
                    'end_date' => $today
                ];
            case 3: // 最近15天
                return [
                    'start_date' => date('Y-m-d', strtotime('-14 day')),
                    'end_date' => $today
                ];
            case 4: // 最近30天
                return [
                    'start_date' => date('Y-m-d', strtotime('-29 day')),
                    'end_date' => $today
                ];
            default:
                return [
                    'start_date' => $today,
                    'end_date' => $today
                ];
        }
    }

    /**
     * 获取屏幕截图数据
     *
     * @param int $appId 应用ID
     * @param string $appName 应用名称
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param string $sortField 排序字段
     * @return array|null 截图数据，失败返回null
     */
    private function getScreenshot(int $appId, string $appName, string $startDate, string $endDate, string $sortField): ?array
    {
        // 构建截图页面URL
        $screenshotUrl = self::SCREENSHOT_PAGE .
            "?developer_app_id={$appId}" .
            "&app_name={$appName}" .
            "&start_date={$startDate}" .
            "&end_date={$endDate}" .
            "&sort_field={$sortField}" .
            "&screenshotDomContainer=.stat-container";

        // 设置HTTP客户端
        $client = new Client([
            'timeout' => 3600,
            'connect_timeout' => 3600,
        ]);

        // 发送请求获取截图
        $response = $client->request('POST', self::SCREENSHOT_API, [
            'json' => [
                'deviceScaleFactor' => 2,
                'web_url' => $screenshotUrl,
            ],
        ]);

        // 处理响应
        $statusCode = $response->getStatusCode();
        $result = $response->getBody()->getContents();

        if ($statusCode == 200 || $statusCode == 201) {
            return json_decode($result, true);
        } else {
            Log::error("获取截图失败，状态码: {$statusCode}", ['response' => $result]);
            return null;
        }
    }

    /**
     * 发送图片到webhook
     *
     * @param array $data 截图数据
     * @param array $webhookUrls webhook地址列表
     * @return void
     */
    private function sendImageToWebhook(array $data, array $webhookUrls): void
    {
        if (empty($data['data']['image_data']) || empty($data['data']['image_md5'])) {
            Log::error("截图数据无效", ['data' => $data]);
            return;
        }

        $payload = [
            'json' => [
                "msgtype" => "image",
                "image" => [
                    "base64" => $data['data']['image_data'],
                    "md5" => $data['data']['image_md5'],
                ]
            ]
        ];

        foreach ($webhookUrls as $url) {
            try {
                $res = HttpAgent::getInstance()->request('POST', $url, $payload);
                dump($res);
            } catch (\Throwable $e) {
                Log::error("发送webhook失败: {$e->getMessage()}", ['url' => $url]);
            }
        }
    }
}
