<?php

/**
 * 首页数据服务基础类
 * @desc 首页数据服务基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/10/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Home;

use App\Components\ClickHouse\ClickHouse;
use App\Components\Helper\CommonHelper;
use App\Model\AppModel;
use App\Model\Record;
use App\Model\VersionWhiteList;
use App\Service\Exception\BaseService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

abstract class Base extends BaseService
{
    /**
     * 消除的指标值
     *
     * @var int
     */
    public const ELIMINATE_BOUNDARY = 1000;

    /**
     * 当前周期
     *
     * @var string
     */
    public const DATE_PERIOD_KEY_CURRENT = 'current';

    /**
     * 请求对象
     *
     * @var Request
     */
    protected $request;

    /**
     * 日期类型
     *
     * @var string
     */
    protected $dateType;

    /**
     * 是否计算比例
     *
     * @var bool
     */
    protected $rate = false;

    /**
     * 是否消除
     *
     * @var bool
     */
    protected $isEliminate = false;

    /**
     * 请求对象
     *
     * @var Request
     */
    protected $eliminateBoundary = 300;

    /**
     * 初始化
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->dateType = Str::lower($request->type ?? '');
        if ($this->dateType == 'latest7') {
            $this->eliminateBoundary = 600;
        } elseif ($this->dateType == 'latest30') {
            $this->eliminateBoundary = self::ELIMINATE_BOUNDARY;
        }
    }

    /**
     * 获取异常次数
     *
     * @param string $returnDatePeriodKey
     * @param array|string[] $eventNameList
     * @param int $limit
     * @return array
     */
    protected function pullExceptionNumList(string $returnDatePeriodKey = '', array $eventNameList = ['exception_crash', 'exception_error'], int $limit = 0): array
    {
        $cache = $this->pullExceptionNumListLogic($eventNameList);
        // $appList = $this->getAppList();
        $appList = $this->getAllAppList();
        $format = [];
        $datePeriodKey = 'current';
        $eachList = $cache[$datePeriodKey]['eachList']; //分子
        $base = $cache[$datePeriodKey]['base']; //分母
        $eachListProportion = $cache['latest']['eachList']; //分子
        $baseProportion = $cache['latest']['base']; //分母
        foreach ($appList as $eachApp) {
            $eachNum = $eachList[$eachApp['app_id']]['num'] ?? 0;
            $eachNumProportion = $eachListProportion[$eachApp['app_id']]['num'] ?? 0;
            if (!$eachNum) {
                continue;
            }
            $format[$datePeriodKey][$eachApp['app_id']] = [
                'app_id' => $eachApp['app_id'],
                'app_name' => $eachApp['app_name'],
                'app_icon' => $eachApp['app_icon'],
                'unit' => '',
                'num' => $eachNum,
                'rate_num' => $eachNumProportion,
                'proportion' => $eachNumProportion != 0 ? bcadd(round(bcdiv(bcsub($eachNum, $eachNumProportion, 6), $eachNumProportion, 6) * 100, 2), 0, 2) : "0",
            ];
            //rate[START]
            if ($this->rate) {
                $numerator = $eachList[$eachApp['app_id']]['num'] ?? 0;
                $denominator = $base[$eachApp['app_id']]['num'] ?? 0;
                $numeratorProportion = $eachListProportion[$eachApp['app_id']]['num'] ?? 0;
                $denominatorProportion = $baseProportion[$eachApp['app_id']]['num'] ?? 0;
                $deviceNum   = $base[$eachApp['app_id']]['device_num'] ?? 0;
                if ($this->isEliminate && $deviceNum < $this->eliminateBoundary) {
                    unset($format[$datePeriodKey][$eachApp['app_id']]);
                    continue;
                }
                $eachRate = (!$numerator || !$denominator) ? 0 : bcadd(round(bcmul(bcdiv($numerator, $denominator, 6), 100, 6), 2), 0, 2);
                $eachRateProportion = (!$numeratorProportion || !$denominatorProportion) ? 0 : bcadd(round(bcmul(bcdiv($numeratorProportion, $denominatorProportion, 6), 100, 6), 2), 0, 2);
                $format[$datePeriodKey][$eachApp['app_id']]['unit'] = '%';
                // $format[$datePeriodKey][$eachApp['app_id']]['num'] = min($eachRate, 100);
                $format[$datePeriodKey][$eachApp['app_id']]['num'] = $eachRate;
                $format[$datePeriodKey][$eachApp['app_id']]['rate_num'] = $eachRateProportion;
                $format[$datePeriodKey][$eachApp['app_id']]['proportion'] = $eachRateProportion != "0" ? bcadd(round(bcdiv(bcsub($eachRate, $eachRateProportion, 6), $eachRateProportion, 6) * 100, 2), 0, 2) : "0";
            }
            //rate[END]
        }

        $format[$datePeriodKey] = isset($format[$datePeriodKey]) ? array_column(CommonHelper::order($format[$datePeriodKey], 'num'), null, 'app_id') : [];
        if ($returnDatePeriodKey) {
            $format = array_values($format[$returnDatePeriodKey]);
        }
        // if ($returnDatePeriodKey && $limit) {
        //     $format = CommonHelper::pagination($format, 1, $limit)['list'];
        // }
        return $format;
    }

    /**
     * 统计列表
     *
     * @param array $eventNameList
     * @return array
     */
    protected function pullExceptionNumListLogic(array $eventNameList): array
    {
        $datePeriod = $this->pullDatePeriod('timestamp');
        $ClickHouse = new ClickHouse();
        $cache = [];
        //保存Builders
        $queries = [];
        //组装Builders
        foreach ($datePeriod as $datePeriodKey => $value) {
            [$startTime, $endTime] = $value;
            $params = [
                'event_name' => $eventNameList,
                'stream_date' => [date('Y-m-d', $startTime), date('Y-m-d', $endTime)],
                'os_type' => $this->request->os_type ?? '',
            ];
            //hitbug[START]
            $queries["{$datePeriodKey}_eachResult"] = $this->getBuilder($params);
            //hitbug[END]
            if ($this->rate) {
                $queries["{$datePeriodKey}_result"] = $this->getRateBuilder($params);
            }
        }
        //并发查询
        $results = $ClickHouse->getMultiSqlData($queries);
        //组装结果
        $keys = array_keys($datePeriod);
        foreach ($keys as $key) {
            $eachResult = $results["{$key}_eachResult"] ?? [];
            if ($this->rate) {
                $result = $results["{$key}_result"] ?? [];
                $base = array_column($result, null, 'app_id'); // 去重后的联网总设备数
            }
            $eachList = array_column($eachResult, null, 'app_id'); // 去重后的 发生崩溃的设备数
            $cache[$key] = [
                'base' => $base ?? [],
                'eachList' => $eachList,
            ];
        }
        return $cache;
    }

    /**
     * @param string $status 返回樣式，值：date/日期（示例：2023-01-01），timestamp/時間戳（示例：）
     * author : <EMAIL>
     * datetime: 2023/06/30 15:02
     * memo : 獲取時間週期
     * @return array
     */
    protected function pullDatePeriod(string $status = 'date'): array
    {
        $currentTimestamp = time();
        switch ($this->dateType) {
            case 'day':
                $latestPeriodStart = $latestPeriodEnd = date("Y-m-d", strtotime("yesterday"));
                $currentPeriodStart = $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'week':
                $latestPeriodStart = date("Y-m-d", strtotime("last week monday"));
                $latestPeriodEnd = date("Y-m-d", strtotime("last week sunday"));
                $currentPeriodStart = date("Y-m-d", strtotime("this week monday"));
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
            case 'month':
                $latestPeriodStart = date("Y-m-01", strtotime("last month"));
                $latestPeriodEnd = date("Y-m-t", strtotime("last month"));
                $currentPeriodStart = date("Y-m-01", $currentTimestamp);
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
            case 'today': // 今天
                $latestPeriodStart = date("Y-m-d", strtotime("-1 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-1 days"));
                $currentPeriodStart = date("Y-m-d", $currentTimestamp);
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest30': // 近30天 含：今天
                $latestPeriodStart = date("Y-m-d", strtotime("-59 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-30 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-29 days"));
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest_year': // 近一年 含：今天
                $latestPeriodStart = date("Y-m-d", strtotime("-731 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-366 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-365 days"));
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest7': //含：今天
                $latestPeriodStart = date("Y-m-d", strtotime("-13 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-7 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-6 days"));
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
            default:
                $latestPeriodStart = date("Y-m-d", strtotime("-1 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-1 days"));
                $currentPeriodStart = date("Y-m-d", $currentTimestamp);
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
        }
        // 判断是否有传 start_date 和 end_date
        $start_date = $this->request->start_date ?? '';
        $end_date = $this->request->end_date ?? '';
        if ($start_date && $end_date && empty($this->dateType)) {
            $currentPeriodStart = Carbon::parse($start_date)->toDateString();
            $currentPeriodEnd = Carbon::parse($end_date)->toDateString();
            // 计算时间差值
            $days = Carbon::parse($currentPeriodStart)->diffInDays(Carbon::parse($currentPeriodEnd)) + 1;
            $latestPeriodStart = Carbon::parse($currentPeriodStart)->subDays($days)->toDateString();
            $latestPeriodEnd = Carbon::parse($currentPeriodEnd)->subDays($days)->toDateString();
            // 获取设备数量限制，判断结束时间-开始时间是否今天、近7天，近30天
            // 判断是否近7天
            $isNear7Days = Carbon::parse($currentPeriodStart)->isBetween(Carbon::parse($currentPeriodEnd)->subDays(7)->toDateString(), Carbon::parse($currentPeriodEnd)->toDateString());
            // 判断是否近30天
            $isNear30Days = Carbon::parse($currentPeriodStart)->isBetween(Carbon::parse($currentPeriodEnd)->subDays(31)->toDateString(), Carbon::parse($currentPeriodEnd)->toDateString());
            $this->eliminateBoundary = 300;
            if ($isNear7Days) {
                $this->eliminateBoundary = 600;
            } elseif ($isNear30Days) {
                $this->eliminateBoundary = 1000;
                $latestPeriodStart = Carbon::parse($currentPeriodStart)->subMonth()->toDateString();
                $latestPeriodEnd = Carbon::parse($latestPeriodStart)->endOfMonth()->toDateString();
            }
        }
        return [
            'current' => [
                $status === 'timestamp' ? strtotime("$currentPeriodStart 00:00:00") : $currentPeriodStart,
                $status === 'timestamp' ? strtotime("$currentPeriodEnd 23:59:59") : $currentPeriodEnd,
            ],
            'latest' => [
                $status === 'timestamp' ? strtotime("$latestPeriodStart 00:00:00") : $latestPeriodStart,
                $status === 'timestamp' ? strtotime("$latestPeriodEnd 23:59:59") : $latestPeriodEnd,
            ]
        ];
    }

    /**
     * 获取SQL的Builder
     *
     * @param array $params
     * @return mixed
     */
    abstract protected function getBuilder(array $params);

    /**
     * 获取Rate的SQLBuilder
     *
     * @param array $params
     * @return mixed
     */
    abstract protected function getRateBuilder(array $params);

    /**
     * 获取所有APP列表
     *
     * @return array
     */
    protected function getAllAppList(): array
    {
        $list = AppModel::query()->where("status", 1)->get()->toArray();
        $format = [];
        foreach ($list as $value) {
            $format[$value['id']] = [
                'app_id' => $value['id'],
                'app_name' => $value['app_name'],
                'app_icon' => $value['app_icon'],
            ];
        }
        return $format;
        // $cookie = $this->request->header('Cookie');
        // $redisKey = RedisKeyEnum::STRING['STRING:AppList:'] . md5($cookie);
        // return RedisHandler::autoGet($redisKey, function () use ($cookie) {
        //     $envi = (env('APP_ENV') === 'production') ? '' : 'test-';
        //     $domain = "https://{$envi}developer-manager.shiyue.com";
        //     $api = "/authV2/getAllAppList";
        //     $result = CommonHelper::commonHttpGet("{$domain}{$api}", [
        //         'page' => 1,
        //         'per_page' => 1000,
        //     ], ['Cookie' => $cookie]);
        //     $format = [];
        //     // if ($list = ($result['data']['list'] ?? [])) {
        //     if ($list = ($result['data'] ?? [])) {
        //         foreach ($list as $value) {
        //             $format[$value['id']] = [
        //                 'app_id' => $value['id'],
        //                 'app_name' => $value['app_name'],
        //                 'app_icon' => $value['app_icon'],
        //             ];
        //         }
        //     }
        //     return $format;
        // }, RedisHandler::INIT['coldDataTimeout']);
    }

    /**
     * 获取白名单异常ID
     *
     * @param $type
     * @return array
     */
    protected function getExceptionBlockIdWhite($type): array
    {
        return Record::query()
            ->when($type, function (Builder $query, $type) {
                return $query->where('type', $type);
            })
            ->where('is_add_white_list', 1)
            ->pluck('exception_block_id')
            ->toArray();
    }

    /**
     * 获取版本白名单
     *
     * @return array
     */
    protected function getAppVersionWhite(): array
    {
        $result = VersionWhiteList::query()
            ->select(['developer_app_id', 'app_version'])
            ->groupBy('developer_app_id', 'app_version')
            ->get();
        $list = [];
        foreach ($result as $item) {
            $list[$item['developer_app_id']][] = $item['app_version'];
        }
        return $list;
    }
}
