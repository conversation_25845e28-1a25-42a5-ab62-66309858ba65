<?php

/**
 * 崩溃和异常分析列表数据
 * @desc 崩溃和异常分析列表数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/09/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Http\Logic\TapdLogic;
use App\Model\BaseModel;
use App\Model\ClickHouse\ExceptionStat;
use App\Model\ClickHouse\ExceptionVersion;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\ExceptionHandler;
use App\Model\Record;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\StarRocks\FilterKeyword;
use App\Model\StarRocks\StarRocksDB;
use App\Model\TapdBug;
use App\Service\Exception\BaseService;
use App\Service\Exception\KeyWordSearch;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExceptionList extends BaseService
{
    use KeyWordSearch;

    /**
     * 24小时内
     */
    public const HOUR_24 = '24_hour';

    /**
     * 7天内
     */
    public const DAY_7 = '7_day';

    /**
     * HTTP请求对象
     *
     * @var Request
     */
    private $request;
    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;
    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;
    /**
     * 时间范围
     *
     * @var array
     */
    private $timeRange;
    /**
     * 上报时间
     *
     * @var array
     */
    private $streamTime;

    /**
     * 原始上报时间
     * @var string
     */
    private $rawStartDate;

    /**
     * 原始上报时间（end）
     * @var string
     */
    private $rawEndDate;
    /**
     * 最后上报时间
     *
     * @var array
     */
    private $lastStreamTime;
    /**
     * 首次上报时间
     *
     * @var array
     */
    private $firstStreamTime;
    /**
     * 过滤条件数组
     *
     * @var array
     */
    private $filters;
    /**
     * 标签
     *
     * @var string
     */
    private $labelName = '';
    /**
     * 处理人ID
     *
     * @var array
     */
    private $handlerId = [];
    /**
     * 处理状态
     *
     * @var array
     */
    private $handlerStatus = [];
    /**
     * 是否剔除白名单
     *
     * @var int
     */
    private $isFilter = 0;
    /**
     * 崩溃时长
     *
     * @var int
     */
    private $useDuration;

    /**
     * 关联录屏
     *
     * @var int
     */
    private $relateRecord;

    /**
     * 关联性能
     *
     * @var int
     */
    private $relatePerf;

    /**
     * 关联TAPD
     *
     * @var int
     */
    private $relateTapd;

    /**
     * 是否有附件
     *
     * @var int
     */
    private $hasAttachment;

    /**
     * 崩溃插件版本
     *
     * @var string
     */
    private $crashPluginVersion;

    /**
     * 需要过滤的关键词ID列表
     *
     * @var array
     */
    private $filterKeywordIds = [];

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->request = $request;
        $this->initParameter();
    }

    /**
     * 初始化参数
     *
     * @return void
     */
    private function initParameter()
    {
        $filters = $this->getFilters();
        //获取数据进行处理
        foreach ($filters as $item) {
            foreach ($item['columns'] as $key => $column) {
                //在请求参数中获取
                $value = $this->request->input($key);
                //null、空字符串、空数组跳过
                if ($value === null || $value === '' || $value === []) {
                    continue;
                }
                //赋值
                $this->filters[$column] = $item['callback']($value);
            }
        }
        //os_type要特殊处理，如果为0，不需要查询
        if (isset($this->filters['os_type']) && $this->filters['os_type'] == 0) {
            unset($this->filters['os_type']);
        }
        //效能后台ID
        $this->extraAppId = $this->request->input('developer_app_id');
        //时间名称
        $this->eventName = self::EVENT_NAME[$this->request->input('type')];
        //获取时间参数
        $timeRange = $this->request->input('time_range');
        $starTime = $this->request->input('start_time');
        $endTime = $this->request->input('end_time');
        $this->rawStartDate = $starDate = $this->request->input('start_date');
        $this->rawEndDate = $endDate = $this->request->input('end_date');
        $firstStartDate = $this->request->input('first_start_date');
        $firstEndDate = $this->request->input('first_end_date');
        $lastStartDate = $this->request->input('last_start_date');
        $lastEndDate = $this->request->input('last_end_date');
        //上报时间
        if ($starTime && $endTime) {
            $this->timeRange = [strtotime($starTime), strtotime($endTime)];
        }
        //上报时间
        if ($starDate && $endDate) {
            $this->streamTime = [strtotime($starDate), strtotime($endDate)];
        }
        //首次上报时间
        if ($firstStartDate && $firstEndDate) {
            $this->firstStreamTime = [strtotime($firstStartDate), strtotime($firstEndDate)];
        }
        //判断是否有时间范围
        if ($timeRange) {
            //获取首次上报的开始时间，没有则赋值给0
            $firstStreamTimeStart = empty($this->firstStreamTime) ? 0 : $this->firstStreamTime[0];
            //时间范围的开始时间
            $timeRangeStart = time() - $this->getTimeRange($timeRange);
            //判断时间范围的开始时间是否大于首次上报的开始时间，大于则采用时间范围的时间
            if ($timeRangeStart > $firstStreamTimeStart) {
                $this->firstStreamTime = [$timeRangeStart, time()];
            }
        }
        //最后上报时间
        if ($lastStartDate && $lastEndDate) {
            $this->lastStreamTime = [strtotime($lastStartDate), strtotime($lastEndDate)];
        }
        //标签
        if ($this->request->input('label_name')) {
            $this->labelName = $this->request->input('label_name');
        }
        //处理人
        if ($this->request->input('handler')) {
            $this->handlerId = explode(',', $this->request->input('handler'));
        }
        //处理状态
        if ($this->request->input('issue_status')) {
            $this->handlerStatus = explode(',', $this->request->input('issue_status'));
        }
        //是否剔除白名单
        if ($this->request->input('is_filter')) {
            $this->isFilter = intval($this->request->input('is_filter'));
        }
        //奔溃时长
        if ($this->request->input('use_duration')) {
            $this->useDuration = intval($this->request->input('use_duration'));
        }
        //需要过滤的关键词ID列表
        if ($this->request->input('filter_keyword_ids')) {
            $this->filterKeywordIds = json_decode($this->request->input('filter_keyword_ids'), true);
        }
        // 是否关联录屏
        if ($this->request->input('relate_record')) {
            $this->relateRecord = intval($this->request->input('relate_record'));
        }
        // 是否关联性能
        if ($this->request->input('relate_perf')) {
            $this->relatePerf = intval($this->request->input('relate_perf'));
        }
        // 是否关联TAPD
        if ($this->request->input('relate_tapd')) {
            $this->relateTapd = intval($this->request->input('relate_tapd'));
        }
        // 是否有附件
        if ($this->request->input('has_attachment')) {
            $this->hasAttachment = intval($this->request->input('has_attachment'));
        }
        // 崩溃插件版本
        if ($this->request->input('crash_plugin_version')) {
            $this->crashPluginVersion = $this->request->input('crash_plugin_version');
        }
    }

    /**
     * 获取过滤的规则
     *
     * @return array[]
     */
    private function getFilters(): array
    {
        return [
            [
                'columns' => [
                    'crash_type' => 'type', //崩溃类型
                    'app_version' => 'app_version', //APP版本
                    'channel' => 'release_store', //发布渠道
                    'app_page' => 'current_page_title', //APP页面
                    'os_version' => 'os_version', //系統版本
                    'device_model' => 'device_model', //机型
                    'sdk_version' => 'version', //SDK版本
                    'manufacturer' => 'manufacturer', //厂商
                    'sdk_package_name' => 'sdk_package_name', //SDK包名
                    'inner_version' => 'inner_version', //游戏资源版本
                ],
                'callback' => function ($value) {
                    return json_decode($value, true);
                },
            ],
            [
                'columns' => [
                    'is_emulator' => 'is_emulator', //是否模擬器
                    'server_dev_str' => 'server_dev_str', //设备ID
                    'account_id' => 'account_id', //账号ID
                    'role_id' => 'role_id', //角色ID
                    'role_name' => 'role_name', //角色名称
                    'exception_block_id' => 'exception_block_id', //异常ID
                    'origin_stacks' => 'explain', //【崩溃/错误】詳情
                    'server_id' => 'server_id', //服务器ID
                ],
                'callback' => function ($value) {
                    return $value;
                },
            ],
            [
                'columns' => [
                    'os_type' => 'os_type', //系统版本 all/android/ios/pc
                ],
                'callback' => function ($value) {
                    return strval($value);
                },
            ],
            [
                'columns' => [
                    'break_status' => 'is_success', //是否越獄，值：0否，1是
                    'console_status' => 'operate_status', //是否前台，值：0前台，1后台
                ],
                'callback' => function ($value) {
                    return intval($value);
                },
            ],
        ];
    }

    /**
     * 获取列表
     *
     * @param $orderField
     * @param $orderSequence
     * @param $pageIndex
     * @param $pageLimit
     * @return array
     */
    public function getList($orderField, $orderSequence, $pageIndex, $pageLimit): array
    {
        $ClickHouse = new ClickHouse();
        $ExceptionStat = new ExceptionStat();
        //stat[START]
        $subBuilder = $this->getSubSql()->groupBy(DB::raw('upper(exception_block_id)'));
        $matchBuilder = $this->getSubSql('upper(exception_block_id) as exception_block_id, count(*) as match_num, count(distinct server_dev_str) as dev_match_num')->groupBy(DB::raw('upper(exception_block_id)'));
        $statBuilder = $ExceptionStat
            ->where('extra_app_id', $this->extraAppId) //where()自動加反引號
            ->where('event_name', $this->eventName)
            ->joinSub($ClickHouse->getSqlBindings($subBuilder), 't', function ($join) use ($ExceptionStat) {
                $join->on('t.exception_block_id', '=', $ExceptionStat->getTable() . '.exception_block_id');
            })
            ->joinSub($ClickHouse->getSqlBindings($matchBuilder), 'match', function ($join) use ($ExceptionStat) {
                $join->on('match.exception_block_id', '=', $ExceptionStat->getTable() . '.exception_block_id');
            });
        if ($this->lastStreamTime) {
            $statBuilder->whereBetween('max_stream_time', $this->lastStreamTime);
        }
        if ($this->firstStreamTime) {
            $statBuilder->whereBetween('min_stream_time', $this->firstStreamTime);
        }
        // 判断如果最新上报时间和最后上报时间都为空，则需要特殊处理一下
        if (empty($this->lastStreamTime) && empty($this->firstStreamTime)) {
            $statBuilder->where('max_stream_time', '>=', $this->streamTime[0]);
        }
        //stat[END]
        //拼装并行查询sql
        $queries['total'] = $this->getSubSql("COUNT(DISTINCT upper(exception_block_id)) AS `total`");
        $queries['list'] = (clone $statBuilder)->selectRaw("upper({$ExceptionStat->getTable()}.exception_block_id) as exception_block_id, min_stream_time AS `stream_min_stream_time`, max_stream_time AS `stream_max_stream_time`, server_dev_str_count AS `device_num`, count AS `block_num`, match.match_num, match.dev_match_num")
            ->orderBy($orderField, $orderSequence)
            ->skip(($pageIndex - 1) * $pageLimit - 1)
            ->limit($pageLimit);
        //多条并行查询
        $results = $ClickHouse->getMultiSqlData($queries);
        $total = (int)($results['total'][0]['total'] ?? 0);
        $list = $results['list'] ?? [];
        $format = [];
        if ($list) {
            $format = $this->format($list);
        }
        return [
            $total,
            $format,
        ];
    }

    /**
     * 获取子查询SQL
     *
     * @param string $selectRaw
     * @return string|Builder
     */
    private function getSubSql(string $selectRaw = 'upper(exception_block_id) as exception_block_id')
    {
        $builder = (new UserLogDataAll())
            ->selectRaw($selectRaw)
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->when($this->streamTime, function (Builder $query, $streamTime) {
                $format = "Y-m-d H:i:s";
                $sd = \DateTime::createFromFormat($format, $this->rawStartDate);
                $ed = \DateTime::createFromFormat($format, $this->rawEndDate);
                if ($sd && $ed && $sd->format($format) == $this->rawStartDate && $ed->format($format) == $this->rawEndDate) {
                    $query->where('stream_time', '>=', $streamTime[0])->where('stream_time', '<=', $streamTime[1]);
                }
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
            })
            ->when($this->timeRange, function (Builder $query, $streamTime) {
                return $query->where('stream_time', '>=', $streamTime[0])
                    ->where('stream_time', '<=', $streamTime[1]);
            })
            ->when($this->firstStreamTime, function (Builder $query, $firstStreamTime) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $firstStreamTime[0]), date('Y-m-d', $firstStreamTime[1])]);
            })
            ->when($this->lastStreamTime, function (Builder $query, $lastStreamTime) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $lastStreamTime[0]), date('Y-m-d', $lastStreamTime[1])]);
            })
            ->when($this->useDuration, function ($query, $useDuration) {
                return $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$useDuration}");
            })
            ->when($this->crashPluginVersion, function ($query, $crashPluginVersion) {
                return $query->whereRaw("`basic_info_json` LIKE '%crash_plugin_ver%' AND get_json_object(`basic_info_json`, 'crash_plugin_ver') = '{$crashPluginVersion}'");
            })
            ->when($this->relateRecord > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where record_id > 0 and server_dev_str != '协议未被同意')");
            })
            ->when($this->relatePerf > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where perf_id > 0 and server_dev_str != '协议未被同意')");
            })
            ->when($this->relateTapd > 0, function ($query) {
                if ($this->relateTapd == 1) {
                    return $query->whereRaw("upper(exception_block_id) in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$this->extraAppId} and bind_status = 1)");
                } else {
                    return $query->whereRaw("upper(exception_block_id) not in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$this->extraAppId} and bind_status = 1)");
                }
            })
            ->when($this->hasAttachment > 0, function ($query) {
                return $query->whereRaw("NULL_OR_EMPTY(`exception_file2`) = false");
            })
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            });

        //other[START]
        if ($this->isFilter) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            // $whereNotInExceptionBlockIdList = array_column(Record::getRecords($this->extraAppId, $type), 'exception_block_id', 'exception_block_id');
            // if ($whereNotInExceptionBlockIdList) {
            //     $builder->whereNotIn(DB::raw("upper(exception_block_id)"), array_map(function ($item) {
            //         return strtoupper($item);
            //     }, $whereNotInExceptionBlockIdList));
            // }
            $builder->whereRaw("upper(exception_block_id) not in (" . Record::getRecordsSql($this->extraAppId, $type) . ")");
        }
        // 标签筛选
        if ($this->labelName) {
            $labelResult = $this->getExceptionBlockIdListByLabelName($this->eventName, $this->labelName);
            if ($labelResult) {
                $builder->whereIn(DB::raw("upper(exception_block_id)"), array_map(function ($item) {
                    return strtoupper($item);
                }, array_values($labelResult)));
            } else {
                $builder->where(DB::raw("upper(exception_block_id)"), '00');
            }
        }
        // 处理人筛选
        if ($this->handlerId) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            $handlerIdResult = ExceptionHandler::getExceptionBlockIdByHandler($this->extraAppId, $this->handlerId, $type);
            $builder->whereRaw("upper(exception_block_id) in ({$handlerIdResult})");
        }
        // 处理状态筛选
        if ($this->handlerStatus) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            list($opt, $sql) = Record::getHandleStatusSql($this->extraAppId, $type, $this->handlerStatus);
            $builder->whereRaw("upper(exception_block_id) {$opt} (" . $sql . ")");
        }
        //other[END]

        $builder = $this->whereFilter($builder);

        //过滤关键词
        $subSql = StarRocksDB::toSql(DB::table(FilterKeyword::TABLE_NAME)
            ->select(['keyword'])
            ->when($this->filterKeywordIds, function ($query) {
                return $query->whereIn('uuid', $this->filterKeywordIds);
            })
            ->where('developer_app_id', $this->extraAppId));
        if (empty($this->filterKeywordIds)) {
            $this->filterKeyWordWhere($builder, $subSql);
        } else {
            $this->filterKeyWordWhere($builder, $subSql, 'in');
        }

        return $builder;
    }

    /**
     * 过滤条件
     *
     * @param $builder
     * @return $builder
     */
    private function whereFilter($builder)
    {
        $builder->when($this->filters, function ($query, $filters) {
            foreach ($filters as $field => $value) {
                //值类型判断
                if (is_array($value)) {
                    $query->whereIn($field, $value);
                } elseif ($field === 'explain') { //判断是否错误详情搜索
                    $query->where(function ($query) use ($value) {
                        $this->explainWhere($query, $value);
                    });
                } elseif ($field === 'role_name') { //判断是否角色名称搜索
                    $query->where(function ($query) use ($value) {
                        return $query->whereRaw("`role_name` LIKE ?", ['%' . $value . '%'])
                            ->orWhereRaw("`extra` LIKE ?", ['%' . $value . '%']);
                    });
                } elseif ($field === 'role_id') { //判断是否角色ID搜索
                    $query->where(function ($query) use ($value) {
                        return $query->whereRaw("`role_id` = ?", [$value])
                            ->orWhereRaw("`extra` LIKE ?", ['%' . $value . '%']);
                    });
                } elseif ($field === 'server_id') { //判断是否服务器ID搜索
                    $query->where(function ($query) use ($value) {
                        $arr = explode(',', $value);
                        foreach ($arr as $v) {
                            $query->orWhereRaw("`extra` LIKE ?", ['%{"key":"serverID","value":"' . $v . '","name":"服务器的ID"}%']);
                        }
                        return $query;
                    });
                } else {
                    $query->where($field, $value);
                }
            }
            return $query;
        });
        return $builder;
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        // 判断时间 结束时间 减去 开始时间 是否超过一个月
        $startDate = Carbon::parse($this->rawStartDate);
        $endDate = Carbon::parse($this->rawEndDate);
        if ($startDate->diffInDays($endDate) > 30) {
            // 抛出异常
            throw new \Exception('错误详情筛选，时间范围不能超过一个月');
        }
        $columns = ['explain_desc', 'subject_name'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }

    /**
     *关键词where条件
     *
     * @param $builder
     * @param string $subSql
     * @param string $opt
     * @return void
     */
    private function filterKeyWordWhere($builder, string $subSql, string $opt = 'not in'): void
    {
        $this->keyWordSearch($builder, $subSql, [
            'extra_app_id' => $this->extraAppId,
            'event_name' => $this->eventName,
            'start_date' => !empty($this->streamTime) ? date('Y-m-d', $this->streamTime[0]) : null,
            'end_date' => !empty($this->streamTime) ? date('Y-m-d', $this->streamTime[1]) : null,
        ], $opt);
    }

    /**
     * @param array $list
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:44
     * memo : 格式化
     */
    public function format(array $list): array
    {
        $blockIds = array_column($list, 'exception_block_id');
        $results = $this->getRelationData($blockIds);
        //异常详细信息
        $exceptionDetail = array_column($results['list'], null, 'exception_block_id');
        //版本
        $versions = [];
        foreach ($results['version'] as $item) {
            $versions[$item['exception_block_id']][] = $item['version'];
        }
        //标签
        $labelList = $this->getLabelListByExceptionList($blockIds);
        //处理人、状态、白名单
        $recordAbstractList = $this->matchRecordAbstract($blockIds);
        //tabp
        $tapdBugList = $this->getTapdBugList($blockIds);
        //格式化处理
        $format = [];
        foreach ($list as $value) {
            $item = [
                'exception_block_id' => $value['exception_block_id'],
                'category' => (int)$exceptionDetail[$value['exception_block_id']]['type'],
                'os_type' => (int)$exceptionDetail[$value['exception_block_id']]['os_type'],
                'event_name' => $this->eventName,
                'name' => $exceptionDetail[$value['exception_block_id']]['subject_name'],
                'explain' => $exceptionDetail[$value['exception_block_id']]['explain'],
                'crash_count' => (int)$value['block_num'],
                'crash_user_count' => (int)$value['device_num'],
                'first_happen_time' => (int)$value['stream_min_stream_time'],
                'first_report_time' => (int)$value['stream_min_stream_time'],
                'last_happen_time' => (int)$value['stream_max_stream_time'],
                'last_report_time' => (int)$value['stream_max_stream_time'],
                'handler' => $recordAbstractList[$value['exception_block_id']]['handler'] ?? '',
                'handler_id' => $recordAbstractList[$value['exception_block_id']]['handler_id'] ?? '',
                'status' => $recordAbstractList[$value['exception_block_id']]['status'] ?? 1,
                'record_id' => $recordAbstractList[$value['exception_block_id']]['record_id'] ?? 0,
                'is_white_list' => $recordAbstractList[$value['exception_block_id']]['is_add_white_list'] ?? 0,
                'bug_id' => $tapdBugList[$value['exception_block_id']]['tapd_bug_id'] ?? '',
                'label_list' => $labelList[$value['exception_block_id']] ?? [],
                'match_num' => (int)$value['match_num'],
                'dev_match_num' => (int)$value['dev_match_num'],
                'version' => $this->getMinMaxVersions($versions[$value['exception_block_id']] ?? []),
                'time_range' => $this->getTimeRange((int)$value['stream_min_stream_time']),
                '14_day_trend' => $this->handleDayTrend($value, $results['14_day_trend']),
                '24_hour_trend' => $this->handleHourTrend($value, $results['24_hour_trend']),
            ];
            // 判断 crash_count 是否小于 match_num
            if ($item['crash_count'] < $item['match_num']) {
                $item['crash_count'] = $item['match_num'];
            }
            // 判断 dev_match_num 是否小于 crash_user_count
            if ($item['crash_user_count'] < $item['dev_match_num']) {
                $item['crash_user_count'] = $item['dev_match_num'];
            }
            $format[] = $item;
        }
        return $format;
    }

    /**
     * 获取时间范围
     *
     * @param $value
     * @return int|string
     */
    private function getTimeRange($value)
    {
        $list = [
            self::HOUR_24 => 86400,
            self::DAY_7 => 604800,
        ];
        //判断value值是否是字符串，是则返回时间戳
        if (is_string($value)) {
            return $list[$value] ?? 0; //没有默认为0
        }
        //判断是否int类型
        if (is_numeric($value)) {
            $timeRange = time() - $value;
            //循环判断
            foreach ($list as $key => $item) {
                //判断时间是否符合
                if ($timeRange < $item) {
                    return $key;
                }
            }
            return "";
        }
        //返回null
        return null;
    }

    /**
     * 获取关联数据
     *
     * @param array $blockIds
     * @return array
     */
    private function getRelationData(array $blockIds): array
    {
        //列表
        $queries['list'] = (new UserLogDataAll())
            ->selectRaw('upper(`exception_block_id`) as exception_block_id, any_value(`type`) AS `type`, any_value(`os_type`) AS `os_type`, any_value(`subject_name`) AS `subject_name`, any_value(`explain_desc`) AS `explain`')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->when($this->lastStreamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamDate[0]), date('Y-m-d', $streamDate[1])]);
            })
            ->when($this->firstStreamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamDate[0]), date('Y-m-d', $streamDate[1])]);
            })
            ->when($this->streamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamDate[0]), date('Y-m-d', $streamDate[1])]);
            })
            ->when($this->timeRange, function (Builder $query, $streamTime) {
                return $query->where('stream_time', '>=', $streamTime[0])
                    ->where('stream_time', '<=', $streamTime[1]);
            })
            ->groupByRaw('upper(`exception_block_id`)');
        //版本
        $queries['version'] = (new ExceptionVersion())
            ->selectRaw('upper(exception_block_id) as exception_block_id, version')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->when($this->lastStreamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [Carbon::parse(date('Y-m-d', $streamDate[0]))->subMonth()->toDateString(), date('Y-m-d', $streamDate[1])]);
            })
            ->when($this->firstStreamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [Carbon::parse(date('Y-m-d', $streamDate[0]))->subMonth()->toDateString(), date('Y-m-d', $streamDate[1])]);
            })
            ->when($this->streamTime, function ($query, $streamDate) {
                return $query->whereBetween('stream_date', [Carbon::parse(date('Y-m-d', $streamDate[0]))->subMonth()->toDateString(), date('Y-m-d', $streamDate[1])]);
            })
            ->groupBy(DB::raw('upper(exception_block_id)'), 'version');
        // 14天趋势
        $queries['14_day_trend'] = ExceptionStreamAll::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, stream_date, count(1) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_date', '>=', Carbon::now()->subDays(13)->toDateString())
            ->where('stream_date', '<=', Carbon::now()->toDateString())
            ->groupBy(DB::raw('upper(exception_block_id)'), 'stream_date')
            ->orderBy(DB::raw('upper(exception_block_id)'))
            ->orderBy('stream_date');
        // 24小时趋势
        $queries['24_hour_trend'] = ExceptionStreamAll::query()
            ->selectRaw("upper(exception_block_id) as exception_block_id, from_unixtime(stream_time, '%Y-%m-%d %H') as stream_date, count(1) as num")
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_date', '>=', Carbon::now()->subHours(24)->toDateString())
            ->where('stream_time', '>=', Carbon::now()->subHours(24)->timestamp)
            ->groupBy(DB::raw('upper(exception_block_id)'), DB::raw("from_unixtime(stream_time, '%Y-%m-%d %H')"))
            ->orderBy(DB::raw('upper(exception_block_id)'))
            ->orderBy('stream_date');
        //多条并行查询
        return (new ClickHouse())->getMultiSqlData($queries);
    }

    /**
     * 根据异常列表获取标签列表
     *
     * @param array $blockIds
     * @return array
     */
    private function getLabelListByExceptionList(array $blockIds): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('label_name', DB::raw('upper(exception_unique_id) as exception_unique_id'))
            ->where('developer_app_id', $this->extraAppId)
            ->whereIn(DB::raw('upper(exception_unique_id)'), $blockIds)
            ->get()->toArray();
        $format = [];
        foreach ($result as $record) { //避免循環查庫
            $format[$record->exception_unique_id][] = [
                'id' => "{$this->extraAppId}##" . $record->label_name,
                'label_name' => $record->label_name,
                'exception_block_id' => $record->exception_unique_id,
            ];
        }
        return $format;
    }

    /**
     * 匹配记录摘要信息
     *
     * @param array $blockIds
     * @return array
     */
    private function matchRecordAbstract(array $blockIds): array
    {
        $result = Record::query()
            ->select(['record_id', 'is_add_white_list', 'status', 'handler_id', 'handler', DB::raw('upper(exception_block_id) as exception_block_id')])
            ->where('developer_app_id', $this->extraAppId)
            ->where('type', self::TYPE[$this->eventName])
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    /**
     * 根据exception_block_id获取最新且未解绑的缺陷记录
     *
     * @param array $blockIds
     * @return array
     */
    private function getTapdBugList(array $blockIds): array
    {
        // 获取当前项目绑定的item_id
        $tapdAccountDetail = TapdLogic::pullTapdAccountDetailCache($this->extraAppId);
        $result = TapdBug::query()
            ->select([DB::raw('upper(exception_block_id) as exception_block_id'), 'tapd_bug_id'])
            ->where('developer_app_id', $this->extraAppId)
            ->where('type', self::TYPE[$this->eventName]) // 1 崩溃 2错误
            ->where('bind_status', 1) // 1 已绑定 2未绑定
            ->when(!empty($tapdAccountDetail), function ($query) use ($tapdAccountDetail) {
                $query->where('tapd_account_id', $tapdAccountDetail['id']);
            })
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    /**
     * 获取最大和最小版本
     *
     * @param array $versions
     * @return array
     */
    private function getMinMaxVersions(array $versions): array
    {
        $minVersion = $versions[0] ?? '';
        $maxVersion = $versions[0] ?? '';
        foreach ($versions as $version) {
            if ($this->compareVersions($version, $minVersion) < 0) {
                $minVersion = $version;
            }
            if ($this->compareVersions($version, $maxVersion) > 0) {
                $maxVersion = $version;
            }
        }
        return [
            'min' => $minVersion,
            'max' => $maxVersion,
        ];
    }

    /**
     * 比较版本
     *
     * @param $version1
     * @param $version2
     * @return int
     */
    private function compareVersions($version1, $version2): int
    {
        $version1Parts = explode('.', $version1);
        $version2Parts = explode('.', $version2);

        $numParts = max(count($version1Parts), count($version2Parts));

        for ($i = 0; $i < $numParts; $i++) {
            $part1 = isset($version1Parts[$i]) ? intval($version1Parts[$i]) : 0;
            $part2 = isset($version2Parts[$i]) ? intval($version2Parts[$i]) : 0;
            if ($part1 < $part2) {
                return -1;
            } elseif ($part1 > $part2) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * 处理趋势数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleDayTrend($item, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = [];
        foreach ($data as $value) {
            $dataIndexedById[$value['exception_block_id']][] = $value;
        }

        $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
        // 用日期作为key
        $statData = array_column($statData, null, 'stream_date');
        // 判断是否存在没有日期的数据
        $startDate = Carbon::now()->subDays(13)->toDateString();
        $endDate = Carbon::now()->toDateString();
        $newStatData = [];
        while ($startDate <= $endDate) {
            // 判断是否存在
            if (!isset($statData[$startDate])) {
                $newStatData[$startDate] = [
                    'exception_block_id' => $item['exception_block_id'],
                    'stream_date' => $startDate,
                    'num' => '0',
                ];
            } else {
                $newStatData[$startDate] = $statData[$startDate];
            }
            $startDate = Carbon::parse($startDate)->addDay()->toDateString();
        }
        return array_values($newStatData);
    }

    /**
     * 处理趋势数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleHourTrend($item, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = [];
        foreach ($data as $value) {
            $dataIndexedById[$value['exception_block_id']][] = $value;
        }

        $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
        // 用日期作为key
        $statData = array_column($statData, null, 'stream_date');
        // 判断是否存在没有日期的数据
        $startDate = Carbon::now()->subHours(24)->timestamp;
        $endDate = Carbon::now()->timestamp;
        $newStatData = [];
        while ($startDate <= $endDate) {
            $date = date('Y-m-d H', $startDate);
            // 判断是否存在
            if (!isset($statData[$date])) {
                $newStatData[$date] = [
                    'exception_block_id' => $item['exception_block_id'],
                    'stream_date' => $date,
                    'num' => '0',
                ];
            } else {
                $newStatData[$date] = $statData[$date];
            }
            // 处理时间把 2024-09-24 11 转为 2024-09-24 11:00~11:59
            $newStatData[$date]['stream_date'] = $newStatData[$date]['stream_date'] . ':00~' . date('H:i', strtotime($newStatData[$date]['stream_date'] . ':59'));
            $startDate = Carbon::parse($startDate)->addHours()->timestamp;
        }
        return array_values($newStatData);
    }
}
