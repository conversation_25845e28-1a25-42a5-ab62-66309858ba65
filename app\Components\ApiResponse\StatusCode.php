<?php

namespace App\Components\ApiResponse;

/**
 * 状态码
 */
class StatusCode
{
    const C_SUCCESS = 0;
    //登录、权限公共模块状态码 1开头
    const C_PARAM_INVAILD = 1000;
    const C_PARAM_ERROR = 1001;
    const C_LOGIN_INVAILD = 1002;
    const C_LOGIN_ERROR = 1003;
    const C_NO_HTTPS = 1004;
    const C_SYS_EXCAPTION = 1005;
    const C_PERMISSION_ERROR = 1006;
    const C_STORE_ERROR = 1007;
    const C_LOGIN_NO_PASSWORD_NAME = 1008;
    const C_LOGIN_CAPTCHA_ERROR = 1009;
    const C_LOGIN_FILE_TYPE_ERROR = 1010;
    const C_ILLEGAL_OPERATE = 1011;
    const C_EXIST_DATA = 1012;

    //热更新后台 4开头
    //SDK版本模块状态码 41开头

    //预警模块状态码 53开头
    const C_WARNING_NAME_REPETITION = 5300;
    const C_WARNING_UPDATE_ERROR = 5310;

    // tapd
    const C_NOT_BOUND_TAPD_PROJECT = 6001;
    const C_CREATE_TAPD_BUG_FAIL = 6002;
    const C_ITEM_ID_ERROR = 6003;
    const C_ITEM_ID_BOUND = 6004;
    const C_RELATION_TAPD_BUG_FAIL = 6005;

    /**
     * 获取状态码对应错误提示
     * @param $code
     * @return string
     */
    public static function getErrorMessage($code)
    {
        return self::$codeMessage[$code] ?? '--';
    }

    public static $codeMessage = [
        self::C_SUCCESS => 'success',
        //系统错误
        self::C_PARAM_INVAILD => '缺少必传参数',
        self::C_PARAM_ERROR => '提交数据格式错误',
        self::C_LOGIN_INVAILD => '账号未登录',
        self::C_LOGIN_ERROR => '登录失败，账号密码不匹配',
        self::C_SYS_EXCAPTION => '系统繁忙，请稍后重试',
        self::C_NO_HTTPS => '非https协议',
        self::C_PERMISSION_ERROR => '非法获取权限',
        self::C_STORE_ERROR => '保存失败',
        self::C_LOGIN_NO_PASSWORD_NAME => '账号密码参数必传',
        self::C_LOGIN_CAPTCHA_ERROR => '验证码验证失败',
        self::C_LOGIN_FILE_TYPE_ERROR => '上传文件的类型错误',
        self::C_ILLEGAL_OPERATE => '非法操作数据',
        self::C_EXIST_DATA => '已存在相应数据',

        self::C_WARNING_NAME_REPETITION => '预警名称已存在',
        self::C_WARNING_UPDATE_ERROR => '预警计划更新失败',

        self::C_NOT_BOUND_TAPD_PROJECT => '未绑定tapd项目',
        self::C_CREATE_TAPD_BUG_FAIL => '创建tapd缺陷失败',
        self::C_ITEM_ID_ERROR => '未查询到当前项目id的项目信息',
        self::C_ITEM_ID_BOUND => '当前项目id已被绑定',
        self::C_RELATION_TAPD_BUG_FAIL => '关联tapd缺陷失败',
    ];


}
