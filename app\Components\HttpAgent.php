<?php

/**
 * User: liuxiongrong
 * Date: 2022/6/01
 * Time: 上午10:53
 * hht请求类
 */

namespace App\Components;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class HttpAgent
{
    private static $instance;
    private $timeout = 5;    //5s
    private $connectTimeout = 3;    //3s

    private function __construct() {}

    /**
     * 静态工厂方法，返还此类的唯一实例
     */
    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 设置超时
     *
     * @param $timeout
     * @return HttpAgent
     */
    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
        return $this;
    }

    /**
     * 设置连接超时
     *
     * @param $connectTimeout
     * @return HttpAgent
     */
    public function setConnectTimeout($connectTimeout)
    {
        $this->connectTimeout = $connectTimeout;
        return $this;
    }

    /**
     * 通用请求基础方法
     * @param string $method
     * @param string $url
     * @param array $options
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function request(string $method, string $url = '', array $options = []): array
    {
        $client = new Client([
            'timeout' => $this->timeout,
            'connect_timeout' => $this->connectTimeout,
        ]);

        if (empty($url))
            return $this->setResult(false, sprintf('地址为空'));

        try {
            $response = $client->request($method, $url, $options);
            // 获取登陆后显示的页面
            $result = $response->getBody()->getContents();
            $code = $response->getStatusCode(); // 200
            $reason = $response->getReasonPhrase(); // OK

            if ($code != 200 || $reason != 'OK') {
                $message = request()->route()->getAction()['controller'] . sprintf('请求失败,code:%d,reason:%s', $code, $reason);
                Log::error(false, $message);
                return $this->setResult(false, sprintf('请求失败,code:%d,reason:%s', $code, $reason));
            } else {
                return $this->setResult(true, $result);
            }
        } catch (RequestException $e) {
            Log::info(sprintf('请求异常：%s', $e->getMessage()), ['method' => $method, 'url' => $url, 'options' => $options]);
            return $this->setResult(false, '请求异常：' . $e->getMessage());
        }
    }

    /**
     * 请求是否成功
     *
     * @param string $method
     * @param string $url
     * @param array $options
     * @return boolean
     */
    public function requestSuccess(string $method, string $url = '', array $options = []): bool
    {
        $client = new Client([
            'timeout' => $this->timeout,
            'connect_timeout' => $this->connectTimeout,
        ]);

        if (empty($url))
            return false;

        try {
            $response = $client->request($method, $url, $options);
            // 获取登陆后显示的页面
            $code = $response->getStatusCode(); // 200
            if ($code != 200) {
                return false;
            } else {
                return true;
            }
        } catch (RequestException $e) {
            return false;
        }
    }


    /**
     * 格式话返回
     * @param bool $success
     * @param $message
     * @return array
     */
    public function setResult(bool $success, $message): array
    {
        return compact('success', 'message');
    }

    public function __clone()
    {
        die('Clone is not allowed.' . E_USER_ERROR);
    }
}
