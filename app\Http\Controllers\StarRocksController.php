<?php

/**
 * User: tallman
 * DateTime: 2023/8/18 10:05
 * 备注: StarRocks查询控制器
 */

namespace App\Http\Controllers;

use App\Service\StarRocksService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StarRocksController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function query(Request $request): JsonResponse
    {
        //获取IP
        $ip = $request->getClientIp();
        //非法IP
        if (!in_array($ip, ['127.0.0.1', $request->server('SERVER_ADDR')])) {
            return $this->response(1000, [], '非法IP');
        }
        //获取参数
        $params = json_decode(file_get_contents("php://input"), true);
        //获取SQL
        $querySql = base64_decode(str_replace('_', '+', $params['sql']));
        try {
            //实例化StarRocksService
            $starRocks = new StarRocksService();
            //查询SQL
            $result = $starRocks->query($querySql);
            //返回结果
            return $this->response(0, $result);
        } catch (\Throwable|\Exception $e) {
            // 输出异常
            Log::error("StarRocks查询SQL报错：{$querySql}, 错误：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
        //返回结果
        return $this->response();
    }
}
