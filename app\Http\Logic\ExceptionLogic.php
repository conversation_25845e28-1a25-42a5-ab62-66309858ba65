<?php

namespace App\Http\Logic;

use App\Components\ClickHouse\ClickHouse;
use App\Components\Helper\CommonHelper;
use App\Components\Helper\DataHelper;
use App\Components\Redis\RedisHandler;
use App\Components\Redis\RedisKeyEnum;
use App\Model\BaseModel;
use App\Model\ClickHouse\ExceptionStat;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\ExceptionHandler;
use App\Model\Record;
use App\Model\StarRocks\FilterKeyword;
use App\Model\TapdBug;
use App\Model\VersionWhiteList;
use App\Service\Exception\BaseService;
use App\Service\Exception\KeyWordFilter;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ExceptionLogic
{
    public const TABLE_STREAM = 'exception_stream_all';

    public const TABLE_STAT = 'exception_stat_all_v2';

    //對應：$request->type
    public const EVENT_NAME = [
        1 => 'exception_crash',
        2 => 'exception_error',
        3 => 'exception_anr',
    ];

    public const TYPE = [
        'exception_crash' => 1,
        'exception_error' => 2,
        'exception_anr' => 3,
    ];

    /**
     * @param int $extraAppId
     * @param string $eventName
     * @param array $streamTime
     * @param array $statFilter
     * @param array $streamFilter
     * @param string $labelName
     * @param array $handlerId
     * @param array $handlerStatus
     * @param string $orderField
     * @param string $orderSequence
     * @param int $pageIndex
     * @param int $pageLimit
     * @param int $whiteListFilter
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:44
     * memo : 獲取異常列表
     */
    public function pullExceptionList(
        int    $extraAppId,
        string $eventName,
        array  $streamTime,
        array  $statFilter,
        array  $streamFilter,
        string $labelName,
        array  $handlerId, //處理人
        array  $handlerStatus, //處理狀態
        string $orderField,
        string $orderSequence,
        int    $pageIndex,
        int    $pageLimit,
        int    $whiteListFilter = 0 //是否剔除白名單，值：0不剔除，1剔除
    ): array {
        $ClickHouse = new ClickHouse();
        $ExceptionStat = new ExceptionStat();
        //stat[START]
        $subSql = $ClickHouse->getSqlBindings((new UserLogDataAll())
                ->selectRaw("exception_block_id")
                ->where('extra_app_id', $extraAppId)
                ->where('event_name', $eventName)
                ->when($streamTime, function ($query, $streamTime) {
                    return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
                })
                ->when(request('use_duration'), function ($query, $useDuration) {
                    return $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$useDuration}");
                })
                ->when($statFilter, function ($query, $statFilter) {
                    foreach ($statFilter as $field => $value) {
                        //值为空跳过
                        if (empty($value) && ($value !== '0' && $value !== 0)) {
                            continue;
                        }

                        //值类型判断
                        if (is_array($value)) {
                            $query->whereIn($field, $value);
                        } else {
                            $query->where($field, $value);
                        }
                    }
                    return $query;
                })
                ->when($streamFilter, function ($query, $streamFilter) {
                    foreach ($streamFilter as $field => $value) {
                        //值为空跳过
                        if (empty($value) && ($value !== '0' && $value !== 0)) {
                            continue;
                        }

                        //值类型判断
                        if (is_array($value)) {
                            $query->whereIn($field, $value);
                        } else {
                            if (in_array($field, ['explain', 'role_name'])) {
                                //字段转换
                                if ($field == 'explain') {
                                    $query->where(function ($query) use ($value) {
                                        $query->orwhereRaw("lower(explain_desc) like ? ", ['%' . mb_strtolower(urlencode($value)) . '%'])
                                            ->orWhereRaw("lower(explain_desc) like ? ", ['%' . mb_strtolower(str_replace("%20", " ", rawurlencode($value)) . '%')]) // 将转义后的空格 %20 替换回空格
                                            ->orWhereRaw("lower(explain_desc) like ? ", ['%' . mb_strtolower($value) . '%']) // 将转义后的空格 %20 替换回空格
                                            ->orWhereRaw("lower(subject_name) like ? ", ['%' . mb_strtolower(urlencode($value)) . '%'])
                                            ->orWhereRaw("lower(subject_name) like ? ", ['%' . mb_strtolower(str_replace("%20", " ", rawurlencode($value)) . '%')])
                                            ->orWhereRaw("lower(subject_name) like ? ", ['%' . mb_strtolower($value) . '%']); // 将转义后的空格 %20 替换回空格
                                    });
                                } else {
                                    //模糊匹配
                                    $query->whereRaw("`{$field}` LIKE ?", ['%' . $value . '%']);
                                }
                            } elseif ($field === 'server_id') { //判断是否服务器ID搜索
                                $query->where(function ($query) use ($value) {
                                    $arr = explode(',', $value);
                                    foreach ($arr as $v) {
                                        $query->orWhereRaw("`extra` LIKE ?", ['%{"key":"serverID","value":"' . $v . '","name":"服务器的ID"}%']);
                                    }
                                    return $query;
                                });
                            } else {
                                $query->where($field, $value);
                            }
                        }
                    }
                    return $query;
                })
                ->groupBy('exception_block_id')
        );
        $statBuilder = $ExceptionStat
            ->where('extra_app_id', $extraAppId) //where()自動加反引號
            ->where('event_name', $eventName)
            ->whereRaw("exception_block_id in ({$subSql})");
        if ($streamTime) {
            $statBuilder->whereBetween('max_stream_time', $streamTime);
        }
        //stat[END]
        //other[START]
        if ($whiteListFilter) {
            //type条件
            $type = BaseService::TYPE[$eventName] ?? 0;
            $statBuilder->whereRaw("upper(exception_block_id) not in (" . Record::getRecordsSql($extraAppId, $type) . ")");
        }
        // 标签筛选
        if ($labelName) {
            $labelResult = $this->pullExceptionBlockIdListByLabelName($eventName, $labelName);
            if ($labelResult) {
                $statBuilder->whereIn(DB::raw("upper(exception_block_id)"), array_map(function ($item) {
                    return strtoupper($item);
                }, array_values($labelResult)));
            } else {
                $statBuilder->where(DB::raw("upper(exception_block_id)"), '00');
            }
        }
        // 处理人筛选
        if ($handlerId) {
            //type条件
            $type = self::TYPE[$eventName] ?? 0;
            $handlerIdResult = ExceptionHandler::getExceptionBlockIdByHandler($extraAppId, $handlerId, $type);
            $statBuilder->whereRaw("upper(exception_block_id) in ({$handlerIdResult})");
        }
        // 处理状态筛选
        if ($handlerStatus) {
            //type条件
            $type = self::TYPE[$eventName] ?? 0;
            list($opt, $sql) = Record::getHandleStatusSql($extraAppId, $type, $handlerStatus);
            $statBuilder->whereRaw("upper(exception_block_id) {$opt} (" . $sql . ")");
        }
        //other[END]
        //拼装并行查询sql
        $queries['total'] = (clone $statBuilder)->selectRaw("count(*) AS `total`");
        $queries['list'] = (clone $statBuilder)->selectRaw("`exception_block_id`, min_stream_time AS `stream_min_stream_time`, max_stream_time AS `stream_max_stream_time`, server_dev_str_count AS `device_num`, count AS `block_num`")->orderBy($orderField, $orderSequence)->skip(($pageIndex - 1) * $pageLimit - 1)->limit($pageLimit);
        //多条并行查询
        $results = $ClickHouse->getMultiSqlData($queries);
        $total = intval($results['total'][0]['total'] ?? 0);
        $list = $results['list'] ?? [];
        $format = $this->format($extraAppId, $eventName, $list, $streamTime);
        return [
            $total,
            $format,
        ];
    }

    /**
     * @param string $eventName
     * @param string $labelName
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:38
     * memo : 根據標籤名稱獲取{$exceptionBlockIdList}
     */
    public function pullExceptionBlockIdListByLabelName(string $eventName, string $labelName): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('exception_unique_id')
            ->where('event_name', $eventName)
            ->where('label_name', $labelName)
            ->get()->toArray();
        return array_column($result, 'exception_unique_id', 'exception_unique_id');
    }

    /**
     * @param int $extraAppId
     * @param string $eventName
     * @param array $list
     * @param array $streamDate
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:44
     * memo : 格式化
     */
    public function format(int $extraAppId, string $eventName, array $list, array $streamDate = []): array
    {
        if ($list) {
            $result = (new ClickHouse())->getData((new UserLogDataAll())
                    ->selectRaw('`exception_block_id`, any_value(`type`) AS `type`, any_value(`os_type`) AS `os_type`, any_value(`subject_name`) AS `subject_name`, any_value(`explain_desc`) AS `explain`')
                    ->where('extra_app_id', $extraAppId)
                    ->where('event_name', $eventName)
                    ->whereIn('exception_block_id', array_column($list, 'exception_block_id'))
                    ->when($streamDate, function ($query, $streamDate) {
                        return $query->whereBetween('stream_date', [date('Y-m-d', $streamDate[0]), date('Y-m-d', $streamDate[1])]);
                    })
                    ->groupByRaw('`exception_block_id`')
            );
            $exceptionDetail = array_column($result, null, 'exception_block_id');
            $format = [];
            $labelList = $this->pullLabelListByExceptionList($extraAppId, $list);
            $recordAbstractList = (new CrashLogic())->matchRecordAbstract($extraAppId, self::TYPE[$eventName], $list);
            $tapdBugList = TapdBug::getTapdBugList($extraAppId, self::TYPE[$eventName], $list);
            foreach ($list as $value) {
                $format[] = [
                    'exception_block_id' => $value['exception_block_id'],
                    'category' => intval($exceptionDetail[$value['exception_block_id']]['type']),
                    'os_type' => intval($exceptionDetail[$value['exception_block_id']]['os_type']),
                    'event_name' => $eventName,
                    'name' => $exceptionDetail[$value['exception_block_id']]['subject_name'],
                    'explain' => $exceptionDetail[$value['exception_block_id']]['explain'],
                    'crash_count' => $value['block_num'],
                    'crash_user_count' => $value['device_num'],
                    'first_happen_time' => $value['stream_min_stream_time'],
                    'first_report_time' => $value['stream_min_stream_time'],
                    'last_happen_time' => $value['stream_max_stream_time'],
                    'last_report_time' => $value['stream_max_stream_time'],
                    'handler' => $recordAbstractList[$value['exception_block_id']]['handler'] ?? '',
                    'status' => $recordAbstractList[$value['exception_block_id']]['status'] ?? 1,
                    'record_id' => $recordAbstractList[$value['exception_block_id']]['record_id'] ?? 0,
                    'is_white_list' => $recordAbstractList[$value['exception_block_id']]['is_add_white_list'] ?? 0,
                    'bug_id' => $tapdBugList[$value['exception_block_id']]['tapd_bug_id'] ?? '',
                    'label_list' => $labelList[$value['exception_block_id']] ?? [],
                    //'version' => [],
                ];
            }
        }
        return $format ?? [];
    }

    /**
     * @param int $developerAppId
     * @param array $exceptionList
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:39
     * memo : 根據異常列表獲取標籤列表
     */
    public function pullLabelListByExceptionList(int $developerAppId, array $exceptionList): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('label_name', 'exception_unique_id')
            ->where('developer_app_id', $developerAppId)
            ->whereIn('exception_unique_id', array_column($exceptionList, 'exception_block_id'))
            ->get()->toArray();
        $format = [];
        foreach ($result as $record) { //避免循環查庫
            $format[$record->exception_unique_id][] = [
                'id' => "{$developerAppId}##" . $record->label_name,
                'label_name' => $record->label_name,
                'exception_block_id' => $record->exception_unique_id,
            ];
        }
        return $format;
    }

    public function pullExceptionSearch(
        int    $extraAppId,
        string $eventName,
        array  $streamTime,
        array  $statFilter,
        array  $streamFilter,
        string $labelName,
        array  $handlerId, //處理人
        array  $handlerStatus, //處理狀態
        int    $whiteListFilter = 0 //是否剔除白名單，值：0不剔除，1剔除
    ): array {
        $redisKey = RedisKeyEnum::STRING['STRING:ExceptionSearch:'] . md5(CommonHelper::prettyJsonEncode(func_get_args()));
        return RedisHandler::autoGet($redisKey, function () use (
            $extraAppId,
            $eventName,
            $streamTime,
            $statFilter,
            $streamFilter,
            $labelName,
            $handlerId, //處理人
            $handlerStatus, //處理狀態
            $whiteListFilter
        ) {
            $ClickHouse = new ClickHouse();
            $ExceptionStream = new UserLogDataAll();
            //stream[START]
            $searchOption = [
                'current_page_title',
                'app_version',
                'release_store',
                'type',
                'device_model',
                'manufacturer',
                'os_version',
                'version',
                'sdk_package_name',
                'inner_version',
                //'exception_block_id'
            ];
            $streamBuilder = $ExceptionStream
                ->where('extra_app_id', $extraAppId)
                ->when(BaseModel::FILTER_PACKAGE_NAME[$extraAppId] ?? false, function ($query) use ($extraAppId) { // 屏蔽包名
                    return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$extraAppId]);
                });
            // 判断是否有server_dev_str参数
            if (!request()->has('server_dev_str')) {
                $searchOption = [
                    'app_version',
                    'sdk_package_name',
                    'inner_version',
                ];
                $streamBuilder->whereIn('event_name', ['exception_start', $eventName]);
            } else {
                $streamBuilder->where('event_name', $eventName);
            }
            //时间范围
            if (!empty($streamTime[0]) && !empty($streamTime[1])) {
                $streamBuilder->whereBetween('stream_time', [$streamTime[0], $streamTime[1]]);
                $streamBuilder->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
            }
            //其他筛选条件
            if ($statFilter) {
                foreach ($statFilter as $field => $value) {
                    //值为空跳过
                    if (empty($value) && ($value !== '0' && $value !== 0)) {
                        continue;
                    }

                    //判断值的类型
                    if (is_array($value)) {
                        $streamBuilder->whereIn($field, $value);
                    } else {
                        $streamBuilder->where($field, $value);
                    }
                }
            }
            //other[START]
            if ($whiteListFilter) {
                //type条件
                $type = BaseService::TYPE[$eventName] ?? 0;
                $streamBuilder->whereRaw("upper(exception_block_id) not in (" . Record::getRecordsSql($extraAppId, $type) . ")");
            }
            // 标签筛选
            if ($labelName) {
                $labelResult = $this->pullExceptionBlockIdListByLabelName($eventName, $labelName);
                if ($labelResult) {
                    $streamBuilder->whereIn(DB::raw("upper(exception_block_id)"), array_map(function ($item) {
                        return strtoupper($item);
                    }, array_values($labelResult)));
                } else {
                    $streamBuilder->where(DB::raw("upper(exception_block_id)"), '00');
                }
            }
            // 处理人筛选
            if ($handlerId) {
                //type条件
                $type = self::TYPE[$eventName] ?? 0;
                $handlerIdResult = ExceptionHandler::getExceptionBlockIdByHandler($extraAppId, $handlerId, $type);
                $streamBuilder->whereRaw("upper(exception_block_id) in ({$handlerIdResult})");
            }
            // 处理状态筛选
            if ($handlerStatus) {
                //type条件
                $type = self::TYPE[$eventName] ?? 0;
                list($opt, $sql) = Record::getHandleStatusSql($extraAppId, $type, $handlerStatus);
                $streamBuilder->whereRaw("upper(exception_block_id) {$opt} (" . $sql . ")");
            }
            if ($streamFilter) { //
                foreach ($streamFilter as $field => $value) {
                    //值为空跳过
                    if (empty($value) && ($value !== '0' && $value !== 0)) {
                        continue;
                    }

                    //判断值的类型
                    if (is_array($value)) {
                        $streamBuilder->whereIn($field, $value);
                    } else {
                        if (in_array($field, ['explain', 'role_name'])) {
                            //字段转换
                            if ($field == 'explain') {
                                // $streamBuilder->where(function ($query) use ($value) {
                                //     $value = request('origin_stacks');
                                //     $columns = ['explain_desc', 'subject_name'];
                                //     $values = [urlencode($value), rawurlencode($value)];
                                //     foreach ($columns as $column) {
                                //         foreach ($values as $val) {
                                //             $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                                //                 ->orWhereRaw(
                                //                     "replace(lower({$column}), '%0a', '%20') like ? ",
                                //                     ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                                //                 );
                                //         }
                                //     }
                                // });
                            } else {
                                //模糊匹配
                                $streamBuilder->whereRaw("`{$field}` LIKE ?", ['%' . $value . '%']);
                            }
                        } elseif ($field === 'server_id') { //判断是否服务器ID搜索
                            $streamBuilder->where(function ($query) use ($value) {
                                $arr = explode(',', $value);
                                foreach ($arr as $v) {
                                    $query->orWhereRaw("`extra` LIKE ?", ['%{"key":"serverID","value":"' . $v . '","name":"服务器的ID"}%']);
                                }
                                return $query;
                            });
                        } else {
                            $streamBuilder->where($field, $value);
                        }
                    }
                }
            }
            //奔溃时长过滤
            $useDuration = request('use_duration');
            if ($useDuration) {
                $streamBuilder->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$useDuration}");
            }
            // 关联录屏
            $relateRecord = request('relate_record');
            if ($relateRecord > 0) {
                $streamBuilder->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where record_id > 0 and server_dev_str != '协议未被同意')");
            }
            // 关联性能
            $relatePerf = request('relate_perf');
            if ($relatePerf > 0) {
                $streamBuilder->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where perf_id > 0 and server_dev_str != '协议未被同意')");
            }
            // 关联tapd
            $relateTapd = request('relate_tapd');
            if ($relateTapd > 0) {
                if ($relateTapd == 1) {
                    $streamBuilder->whereRaw("upper(exception_block_id) in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$extraAppId} and bind_status = 1)");
                } else {
                    $streamBuilder->whereRaw("upper(exception_block_id) not in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$extraAppId} and bind_status = 1)");
                }
            }
            // 是否有附件
            $hasAttachment = request('has_attachment');
            if ($hasAttachment > 0) {
                $streamBuilder->whereRaw("NULL_OR_EMPTY(`exception_file2`) = false");
            }
            $result = [];
            //拼装并行查询sql
            $queries = [];
            foreach ($searchOption as $field) {
                $eachStreamBuilder = clone $streamBuilder;
                $eachStreamBuilder
                    ->selectRaw("DISTINCT `{$field}`")
                    ->orderByDesc($field);
                $queries[$field] = $eachStreamBuilder;
            }
            // 判断是否有server_dev_str参数
            if (request()->has('server_dev_str')) {
                $queries['filter_keyword'] = FilterKeyword::query()
                    ->select(['keyword', 'uuid'])
                    ->where('developer_app_id', $extraAppId)
                    ->latest();
            }
            //多条并行查询
            $results = $ClickHouse->getMultiSqlData($queries);
            foreach ($results as $key => $value) {
                $result[$key] = array_column($value, $key);
            }
            $result['filter_keyword'] = $results['filter_keyword'] ?? [];
            return $this->exceptionSearchFormat($extraAppId, $result);
        }, RedisHandler::INIT['hotDataTimeout']);
    }

    public function exceptionSearchFormat(int $developerAppId, array $result): array
    {
        $alias = [ //將數據表字段名稱映射至前端對接的名稱
            'current_page_title' => 'app_page',
            'app_version' => 'app_version',
            'release_store' => 'channel',
            'type' => 'crash_type',
            'device_model' => 'device_model',
            'manufacturer' => 'manufacturer',
            'os_version' => 'os_version',
            'version' => 'sdk_version',
            'sdk_package_name' => 'sdk_package_name',
            //-----
            'province' => 'province',
            'handler_id' => 'handler',
            'label_name' => 'label_name',
            'filter_keyword' => 'filter_keyword',
            'inner_version' => 'inner_version',
        ];
        $typeLabel = Record::$categoryMessage;
        $format = [];
        foreach ($result as $field => $value) {
            if ($field == 'exception_block_id') {
                continue;
            }

            //如果为空赋值一个空数组
            if (empty($value) || (count($value) === 1 && empty($value[0]))) {
                $format[$alias[$field]] = [];
            } else {
                foreach ($value as $v) {
                    if (!$v) {
                        continue;
                    }

                    if ($field == 'type') {
                        $format[$alias[$field]][] = [
                            'label' => $typeLabel[$v] ?? $v,
                            'value' => $v,
                        ];
                    } else if ($field === 'filter_keyword') {
                        $format[$alias[$field]][] = [
                            'label' => $v['keyword'],
                            'value' => $v['uuid'],
                        ];
                    } else {
                        $format[$alias[$field]][] = [
                            'label' => $v,
                            'value' => $v,
                        ];
                    }
                }
            }
        }
        //標籤[START]
        $format['label_name'] = $this->matchLabelList($developerAppId, /*$result['exception_block_id'] ??*/ []);
        //標籤[END]
        return $format;
    }

    public function matchLabelList(int $developerAppId, array $exceptionBlockIdList): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('label_name', 'exception_unique_id')
            ->where('developer_app_id', $developerAppId)
            //->whereIn('exception_unique_id', $exceptionBlockIdList)
            ->get()->toArray();
        $format = [];
        foreach ($result as $record) { //避免循環查庫
            $format["{$developerAppId}##" . $record->label_name] = [
                'label_id' => "{$developerAppId}##" . $record->label_name,
                'label_name' => $record->label_name,
            ];
        }
        return array_values($format ?? []);
    }

    /**
     * @param $request
     * @param int $extraAppId
     * @param string $eventName
     * @param array $streamTime
     * @param array $statFilter
     * @param array $streamFilter
     * @param string $labelName
     * @param array $handlerId
     * @param array $handlerStatus
     * @param int $isFilter
     * author : <EMAIL>
     * datetime: 2023/07/17 10:07
     * memo : 入參檢測，使用於：list/search/summary
     */
    public function checkParameter(
        $request,
        int &$extraAppId,
        string &$eventName,
        array &$streamTime,
        array &$statFilter,
        array &$streamFilter,
        string &$labelName,
        array &$handlerId,
        array &$handlerStatus,
        int &$isFilter
    ): bool {
        //$statFilter = $streamFilter = [];
        //format parameter[START]
        $extraAppId = $request->developer_app_id;
        $eventName = ExceptionLogic::EVENT_NAME[$request->type];
        $streamTime = [strtotime($request->start_date), strtotime($request->end_date)];
        //組合篩選[START]
        if ($request->crash_type ?? []) {
            $statFilter['type'] = json_decode($request->crash_type, true);
        }
        //崩潰類型
        if ($request->os_type ?? 0) {
            $statFilter['os_type'] = (string)($request->os_type);
        }
        //all/android/ios/pc
        if ($request->app_version ?? '') {
            $statFilter['app_version'] = json_decode($request->app_version, true);
        }
        if ($request->inner_version ?? '') {
            $statFilter['inner_version'] = json_decode($request->inner_version, true);
        }
        //APP版本
        //組合篩選[END]
        //精確匹配[START]
        if ($request->channel ?? '') {
            $streamFilter['release_store'] = json_decode($request->channel, true);
        }
        //發佈渠道
        if ($request->app_page ?? '') {
            $streamFilter['current_page_title'] = json_decode($request->app_page, true);
        }
        //APP頁面
        if ($request->os_version ?? '') {
            $streamFilter['os_version'] = json_decode($request->os_version, true);
        }
        //系統版本
        if ($request->device_model ?? '') {
            $streamFilter['device_model'] = json_decode($request->device_model);
        }
        //機型
        if ($request->sdk_version ?? '') {
            $streamFilter['version'] = json_decode($request->sdk_version, true);
        }
        //SDK版本
        if (in_array($request->break_status ?? -1, [0, 1])) {
            $streamFilter['is_success'] = intval($request->break_status);
        }
        //是否越獄，值：0否，1是
        if (in_array($request->console_status ?? -1, [0, 1])) {
            $streamFilter['operate_status'] = intval($request->console_status);
        }
        //是否前台，值：0前台，1后台
        if ($request->manufacturer ?? '') {
            $streamFilter['manufacturer'] = json_decode($request->manufacturer, true);
        }
        //廠商
        if ($request->sdk_package_name ?? '') {
            $streamFilter['sdk_package_name'] = json_decode($request->sdk_package_name, true);
        }
        //SDK包名
        //-----
        if (in_array($request->is_emulator ?? -1, [0, 1])) {
            $streamFilter['is_emulator'] = $request->is_emulator;
        }
        //是否模擬器//追加的
        if ($request->origin_stacks ?? '') {
            $streamFilter['explain'] = urlencode($request->origin_stacks);
        }
        //{崩潰/錯誤}詳情
        if ($request->server_dev_str ?? '') {
            $streamFilter['server_dev_str'] = $request->server_dev_str;
        }
        //設備ID
        if ($request->account_id ?? '') {
            $streamFilter['account_id'] = $request->account_id;
        }
        //賬戶ID
        if ($request->role_id ?? 0) {
            $streamFilter['role_id'] = $request->role_id;
        }
        //角色ID
        if ($request->role_name ?? '') {
            $streamFilter['role_name'] = $request->role_name;
        }
        //角色名稱
        if ($request->exception_block_id ?? '') {
            $streamFilter['exception_block_id'] = $request->exception_block_id;
        }
        //异常ID
        if ($request->server_id ?? '') {
            $streamFilter['server_id'] = $request->server_id;
        }
        //服务器ID
        //精確匹配[END]
        //dependence mysql[START]
        $labelName = $request->label_name ?: '';
        // 判断 $request->handler 是否存在逗号，存在则将 $request->handler 转换为数组
        $handlerId = json_decode($request->handler ?? '', true);
        if (empty($handlerId) && $request->handler) {
            $handlerId = explode(',', $request->handler);
        } else {
            $handlerId = [];
        }
        // 判断 $request->issue_status 是否存在逗号，存在则将 $request->issue_status 转换为数组
        $handlerStatus = json_decode($request->issue_status ?? '', true);
        if (empty($handlerStatus) && $request->issue_status) {
            $handlerStatus = explode(',', $request->issue_status);
        } else {
            $handlerStatus = [];
        }
        $isFilter = (isset($request->is_filter) && !empty($request->is_filter)) ? intval($request->is_filter) : 0; //是否剔除白名單
        //dependence mysql[END]
        return true;
    }

    public function summary(
        int    $extraAppId,
        string $eventName,
        array  $streamTime,
        array  $streamWhere,
        int    $useDuration,
        int    $whiteListFilter = 0,
        array  $rawStreamDate = []
    ): array {
        $latestStreamTime = [ //上一週期
            ($streamTime[0] - ($streamTime[1] - $streamTime[0])),
            $streamTime[0] - 1,
        ];
        if (request('use_duration') === null) {
            return $this->summarySimple(
                $extraAppId,
                $eventName,
                $streamTime,
                $streamWhere,
                $useDuration,
                $whiteListFilter,
                $rawStreamDate
            );
        }
        $currentBuilders = $this->summarySlave(
            $extraAppId,
            $eventName,
            $streamTime,
            $streamWhere,
            $useDuration,
            $whiteListFilter,
            $rawStreamDate
        ); //當前週期
        $latestBuilders = $this->summarySlave(
            $extraAppId,
            $eventName,
            $latestStreamTime,
            $streamWhere,
            $useDuration,
            $whiteListFilter,
            $rawStreamDate
        ); //上一週期
        //批量查询
        $results = (new ClickHouse())->getMultiSqlData([
            'current_numerator' => $currentBuilders['numerator_builder'],
            'current_exception_num_denominator' => $currentBuilders['exception_num_denominator_builder'],
            'current_device_num_denominator' => $currentBuilders['device_num_denominator_builder'],
            'current_exception_device_num_denominator' => $currentBuilders['exception_device_num_denominator_builder'],
            'latest_numerator' => $latestBuilders['numerator_builder'],
            'latest_exception_num_denominator' => $latestBuilders['exception_num_denominator_builder'],
            'latest_device_num_denominator' => $latestBuilders['device_num_denominator_builder'],
            'latest_exception_device_num_denominator' => $latestBuilders['exception_device_num_denominator_builder'],
        ]);
        //设置结果
        $currentResult = [
            'exceptionNum' => $results['current_numerator'][0]['exception_num'] ?? 0, //崩潰次數
            'baseExceptionNum' => $results['current_exception_num_denominator'][0]['exception_num'] ?? 0, //聯網次數
            'deviceNum' => $results['current_numerator'][0]['device_num'] ?? 0, //崩潰設備
            'baseDeviceNum' => $results['current_exception_num_denominator'][0]['device_num'] ?? 0, //聯網設備
        ];
        $latestResult = [
            'exceptionNum' => $results['latest_numerator'][0]['exception_num'] ?? 0, //崩潰次數
            'baseExceptionNum' => $results['latest_exception_num_denominator'][0]['exception_num'] ?? 0, //聯網次數
            'deviceNum' => $results['latest_numerator'][0]['device_num'] ?? 0, //崩潰設備
            'baseDeviceNum' => $results['latest_exception_num_denominator'][0]['device_num'] ?? 0, //聯網設備
        ];
        $exceptionRate = round(DataHelper::divide($currentResult['exceptionNum'], $currentResult['baseExceptionNum'], 4) * 100, 2);
        // 把 $results['current_device_num_denominator'] 里面 all_num、err_num 相加
        $currentBaseDeviceNum = 0;
        $currentBaseDeviceNumBak = 0;
        $currentDeviceNum = 0;
        foreach ($results['current_device_num_denominator'] as $item) {
            $currentBaseDeviceNum += $item['all_num'];
        }
        foreach ($results['current_exception_num_denominator'] as $item) {
            $currentBaseDeviceNumBak += $item['device_num'];
        }
        foreach ($results['current_exception_device_num_denominator'] as $item) {
            $currentDeviceNum += $item['err_num'];
        }
        if ($currentBaseDeviceNum - $currentBaseDeviceNumBak < 2000) {
            $currentBaseDeviceNum = $currentBaseDeviceNumBak;
        }
        $deviceRate = round(DataHelper::divide($currentDeviceNum, $currentBaseDeviceNum, 4) * 100, 2);
        $latestExceptionRate = round(DataHelper::divide($latestResult['exceptionNum'], $latestResult['baseExceptionNum'], 4) * 100, 2);
        // 把 $results['latest_device_num_denominator'] 里面 all_num、err_num 相加
        $latestBaseDeviceNum = 0;
        $latestDeviceNum = 0;
        foreach ($results['latest_device_num_denominator'] as $item) {
            $latestBaseDeviceNum += $item['all_num'];
        }
        foreach ($results['latest_exception_device_num_denominator'] as $item) {
            $latestDeviceNum += $item['err_num'];
        }
        $latestDeviceRate = round(DataHelper::divide($latestDeviceNum, $latestBaseDeviceNum, 4) * 100, 2);
        return [
            'crash_count' => $currentResult['exceptionNum'], //次數
            'crash_user_count' => $currentResult['deviceNum'], //設備數
            'start_count' => $currentResult['baseExceptionNum'], //聯網次數
            'start_user_count' => $currentResult['baseDeviceNum'], //聯網設備
            'crash_percent' => $exceptionRate,
            'crash_user_percent' => $deviceRate,
            //-----
            'crash_count_proportion' => DataHelper::linkRatio($currentResult['exceptionNum'], $latestResult['exceptionNum']),
            'crash_user_count_proportion' => DataHelper::linkRatio($currentResult['deviceNum'], $latestResult['deviceNum']),
            'crash_percent_proportion' => DataHelper::linkRatio($exceptionRate, $latestExceptionRate),
            'crash_user_percent_proportion' => DataHelper::linkRatio($deviceRate, $latestDeviceRate),
            'start_user_proportion' => DataHelper::linkRatio($currentResult['baseDeviceNum'], $latestResult['baseDeviceNum']),
            //-----
            "last_happen_time" => null,
            "last_event_time" => null,
        ];
    }

    /**
     * 汇总简单查询
     *
     * @param int $extraAppId
     * @param string $eventName
     * @param array $streamTime
     * @param array $streamWhere
     * @param int $useDuration
     * @param int $whiteListFilter
     * @return array
     */
    public function summarySimple(
        int    $extraAppId,
        string $eventName,
        array  $streamTime,
        array  $streamWhere,
        int    $useDuration,
        int    $whiteListFilter = 0,
        array  $rawStreamDate = []
    ): array {
        $currentBuilders = $this->summarySlave(
            $extraAppId,
            $eventName,
            $streamTime,
            $streamWhere,
            $useDuration,
            $whiteListFilter,
            $rawStreamDate
        ); //當前週期
        //批量查询
        $results = (new ClickHouse())->getMultiSqlData([
            'current_numerator' => $currentBuilders['numerator_builder'],
            'current_exception_num_denominator' => $currentBuilders['exception_num_denominator_builder'],
        ]);
        //设置结果
        $currentResult = [
            'exceptionNum' => $results['current_numerator'][0]['exception_num'] ?? 0, //崩潰次數
            'baseExceptionNum' => $results['current_exception_num_denominator'][0]['exception_num'] ?? 0, //聯網次數
        ];
        $exceptionRate = round(DataHelper::divide($currentResult['exceptionNum'], $currentResult['baseExceptionNum'], 4) * 100, 2);
        return [
            'crash_count' => 0, //次數
            'crash_user_count' => 0, //設備數
            'start_count' => 0, //聯網次數
            'start_user_count' => 0, //聯網設備
            'crash_percent' => $exceptionRate,
            'crash_user_percent' => 0,
            'crash_count_proportion' => 0,
            'crash_user_count_proportion' => 0,
            'crash_percent_proportion' => 0,
            'crash_user_percent_proportion' => 0,
            'start_user_proportion' => 0,
            "last_happen_time" => null,
            "last_event_time" => null,
        ];
    }

    public function summarySlave(
        $extraAppId,
        $eventName,
        $streamTime,
        $streamWhere,
        $useDuration,
        $whiteListFilter,
        $rawStreamDate
    ): array {
        $ExceptionStream = new UserLogDataAll();
        //type条件
        $type = self::TYPE[$eventName] ?? 0;
        //过滤白名单
        $keywordFilterService = new KeyWordFilter($extraAppId);
        $keywordFilter = $keywordFilterService->getNotStatKeyword();
        !empty($streamTime[0]) && $keywordFilterService->setDate(date('Y-m-d', $streamTime[0]));
        $appVersionWhite = !empty($whiteListFilter) ? VersionWhiteList::getVersionList($extraAppId) : null;
        $exceptionBlockIdWhite = !empty($whiteListFilter) ? Record::getExceptionBlockIds($extraAppId, $type) : null;
        //where条件
        $ExceptionStream = $ExceptionStream->where('extra_app_id', $extraAppId)
            ->when($streamTime, function (Builder $query, $streamTime) use ($rawStreamDate) {
                $format = "Y-m-d H:i:s";
                $sd = \DateTime::createFromFormat($format, $rawStreamDate[0]);
                $ed = \DateTime::createFromFormat($format, $rawStreamDate[1]);
                if ($sd && $ed && $sd->format($format) == $rawStreamDate[0] && $ed->format($format) == $rawStreamDate[1]) {
                    $query->where('stream_time', '>=', $streamTime[0])->where('stream_time', '<=', $streamTime[1]);
                }
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
            })
            ->when(isset($streamWhere['os_type']), function (Builder $query) use ($streamWhere) {
                return $query->where('os_type', $streamWhere['os_type']);
            })
            ->when(isset($streamWhere['app_version']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('app_version', $streamWhere['app_version']);
            })
            ->when(!empty($streamWhere['inner_version']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('inner_version', $streamWhere['inner_version']);
            })
            ->when(isset($streamWhere['sdk_package_name']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('sdk_package_name', $streamWhere['sdk_package_name']);
            })
            ->when(isset($streamWhere['crash_type']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('type', json_decode($streamWhere['crash_type'], true));
            })
            ->when(isset($streamWhere['is_emulator']), function (Builder $query) use ($streamWhere) {
                $query->where(function ($qu) use ($streamWhere) {
                    $qu->where('is_emulator', $streamWhere['is_emulator']);
                    // 判断 is_emulator 是否等于 0，等于0 兼容 为null的情况
                    if ($streamWhere['is_emulator'] == 0) {
                        $qu->orWhereNull('is_emulator');
                    }
                    return $qu;
                });
                return $query;
            })
            ->when($appVersionWhite, function (Builder $query, $appVersionWhite) {
                return $query->whereNotIn('app_version', $appVersionWhite);
            })
            ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                return $query->whereRaw($keywordFilterService->getFilterSql());
            })
            ->when($exceptionBlockIdWhite, function (Builder $query, $exceptionBlockIdWhite) {
                return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                    return strtoupper($item);
                }, $exceptionBlockIdWhite));
            })
            ->when((($eventName == 'exception_crash') && $useDuration), function (Builder $query) use ($useDuration) {
                return $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$useDuration}");
            })->when(BaseModel::FILTER_PACKAGE_NAME[$extraAppId] ?? false, function ($query) use ($extraAppId) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$extraAppId]);
            });

        $numeratorBuilder = (clone $ExceptionStream)
            ->selectRaw("COUNT(1) AS `exception_num`, COUNT(DISTINCT `server_dev_str`) AS `device_num`")
            ->where('event_name', $eventName);

        $exceptionNumDenominatorBuilder = (new UserLogDataAll())
            ->where('extra_app_id', $extraAppId)
            ->when($streamTime, function (Builder $query, $streamTime) use ($rawStreamDate) {
                $format = "Y-m-d H:i:s";
                $sd = \DateTime::createFromFormat($format, $rawStreamDate[0]);
                $ed = \DateTime::createFromFormat($format, $rawStreamDate[1]);
                if ($sd && $ed && $sd->format($format) == $rawStreamDate[0] && $ed->format($format) == $rawStreamDate[1]) {
                    $query->where('stream_time', '>=', $streamTime[0])->where('stream_time', '<=', $streamTime[1]);
                }
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
            })
            ->when(isset($streamWhere['os_type']), function (Builder $query) use ($streamWhere) {
                return $query->where('os_type', $streamWhere['os_type']);
            })
            ->when(isset($streamWhere['app_version']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('app_version', $streamWhere['app_version']);
            })
            ->when(isset($streamWhere['sdk_package_name']), function (Builder $query) use ($streamWhere) {
                return $query->whereIn('sdk_package_name', $streamWhere['sdk_package_name']);
            })
            ->when(BaseModel::FILTER_PACKAGE_NAME[$extraAppId] ?? false, function ($query) use ($extraAppId) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$extraAppId]);
            })
            ->when(isset($streamWhere['is_emulator']), function (Builder $query) use ($streamWhere) {
                $query->where(function ($qu) use ($streamWhere) {
                    $qu->where('is_emulator', $streamWhere['is_emulator']);
                    // 判断 is_emulator 是否等于 0，等于0 兼容 为null的情况
                    if ($streamWhere['is_emulator'] == 0) {
                        $qu->orWhereNull('is_emulator');
                    }
                    return $qu;
                });
                return $query;
            });

        return [
            'numerator_builder' => $numeratorBuilder,
            'exception_num_denominator_builder' => (clone $exceptionNumDenominatorBuilder)
                ->selectRaw("COUNT(1) AS `exception_num`, COUNT(distinct `server_dev_str`) AS `device_num`")
                ->where('event_name', 'exception_start'),
            'device_num_denominator_builder' => (clone $exceptionNumDenominatorBuilder)
                ->selectRaw("stream_date, count(distinct server_dev_str) as all_num")
                ->groupBy('stream_date'),
            'exception_device_num_denominator_builder' => (clone $exceptionNumDenominatorBuilder)
                ->selectRaw("stream_date, count(distinct server_dev_str) as err_num")
                ->where('event_name', $eventName)
                ->when($appVersionWhite, function (Builder $query, $appVersionWhite) {
                    return $query->whereNotIn('app_version', $appVersionWhite);
                })
                ->when(!empty($streamWhere['inner_version']), function (Builder $query) use ($streamWhere) {
                    return $query->whereIn('inner_version', $streamWhere['inner_version']);
                })
                ->when($exceptionBlockIdWhite, function (Builder $query, $exceptionBlockIdWhite) {
                    return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, $exceptionBlockIdWhite));
                })
                ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                    return $query->whereRaw($keywordFilterService->getFilterSql());
                })
                ->when(isset($streamWhere['crash_type']), function (Builder $query) use ($streamWhere) {
                    return $query->whereIn('type', json_decode($streamWhere['crash_type'], true));
                })
                ->groupBy('stream_date'),
        ];
    }

    public function summaryOneBlock(int $extraAppId, string $eventName, string $exceptionBlockId): array
    {
        $builder = (new ExceptionStat())
            ->selectRaw("count AS `exception_num`, server_dev_str_count AS `device_num`, min_stream_time as `first_report_time`, max_stream_time as `last_report_time`")
            ->where('extra_app_id', $extraAppId)
            ->where('event_name', $eventName)
            ->where('exception_block_id', strtoupper($exceptionBlockId));
        $result = (new ClickHouse())->getData($builder);
        return [
            'crash_count' => $result[0]['exception_num'] ?? 0,
            'crash_user_count' => $result[0]['device_num'] ?? 0,
            'first_report_time' => $result[0]['first_report_time'] ?? 0,
            'last_report_time' => $result[0]['last_report_time'] ?? 0,
        ];
    }

    /**
     * @param int $extraAppId
     * @param array $fields
     * @param array $requestParams
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/18 15:03
     * memo : 異常趨勢--異常分佈
     */
    public function pullExceptionDistribution(int $extraAppId, array $fields, array $requestParams): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:PullExceptionDistribution:'] . md5(json_encode(func_get_args()));
        return RedisHandler::autoGet($redisKey, function () use ($extraAppId, $fields, $requestParams) {
            $ClickHouse = new ClickHouse();
            $queries = [];
            foreach ($fields as $field) {
                if ($requestParams['number_mode'] == 0) { //1時，按次數統計
                    $selectRaw = "`{$field}` AS `name`, COUNT(1) AS `exception_num`, COUNT(DISTINCT `server_dev_str`) AS `num`";
                } elseif ($requestParams['number_mode'] == 1) { //2時，按次数統計
                    $selectRaw = "`{$field}` AS `name`, COUNT(1) AS `num`";
                } else { //0時，按设备統計
                    $selectRaw = "`{$field}` AS `name`, COUNT(DISTINCT `server_dev_str`) AS `num`";
                }
                // 新添加 [start]
                $ExceptionStream = new UserLogDataAll();
                //获取进入白名单的 版本集合 与 exceptionBlockId集合
                if (!empty($requestParams['white_list_filter'])) {
                    $whereNotInAppVersion = VersionWhiteList::getVersionList($extraAppId);
                    $whereNotInExceptionBlockId = Record::getExceptionBlockIds($extraAppId, $requestParams['type']);
                } else {
                    $whereNotInAppVersion = null;
                    $whereNotInExceptionBlockId = null;
                }
                $keywordFilterService = new KeyWordFilter($extraAppId);
                $keywordFilter = $keywordFilterService->getNotStatKeyword();
                !empty($requestParams['streamTime'][0]) && $keywordFilterService->setDate(date('Y-m-d', $requestParams['streamTime'][0]));
                try {
                    //过滤条件
                    $ExceptionStream = $ExceptionStream->selectRaw($selectRaw)
                        ->where('extra_app_id', $extraAppId)
                        ->whereBetween('stream_time', [$requestParams['streamTime'][0], $requestParams['streamTime'][1]])
                        ->whereBetween('stream_date', [date('Y-m-d', $requestParams['streamTime'][0]), date('Y-m-d', $requestParams['streamTime'][1])])
                        ->where($field, '!=', '')
                        ->when(!empty($requestParams['sdk_package_name']), function (Builder $query) use ($requestParams) {
                            return $query->whereIn('sdk_package_name', $requestParams['sdk_package_name']);
                        })
                        ->when(!empty($requestParams['app_version']), function (Builder $query) use ($requestParams) {
                            return $query->whereIn('app_version', $requestParams['app_version']);
                        })
                        ->when(!empty($requestParams['inner_version']), function (Builder $query) use ($requestParams) {
                            return $query->whereIn('inner_version', $requestParams['inner_version']);
                        })
                        ->when(!empty($requestParams['os_type']), function (Builder $query) use ($requestParams) {
                            return $query->where('os_type', $requestParams['os_type']);
                        })
                        ->when(isset($requestParams['is_emulator']), function (Builder $query) use ($requestParams) {
                            $query->where(function ($qu) use ($requestParams) {
                                $qu->where('is_emulator', $requestParams['is_emulator']);
                                // 判断 is_emulator 是否等于 0，等于0 兼容 为null的情况
                                if ($requestParams['is_emulator'] == 0) {
                                    $qu->orWhereNull('is_emulator');
                                }
                                return $qu;
                            });
                            return $query;
                        })
                        ->when(!empty($requestParams['exception_block_id']) && (($requestParams['type'] ?? 0) != -1), function (Builder $query) use ($requestParams) {
                            return $query->where(DB::raw('upper(exception_block_id)'), strtoupper($requestParams['exception_block_id']));
                        })
                        ->when(!empty($requestParams['type']), function (Builder $query) use ($requestParams) {
                            if ($requestParams['type'] == Record::TYPE_CRASH) {
                                return $query->where('event_name', 'exception_crash');
                            } elseif ($requestParams['type'] == Record::TYPE_ERROR) {
                                return $query->where('event_name', 'exception_error');
                            } elseif ($requestParams['type'] == Record::TYPE_ANR) {
                                return $query->where('event_name', 'exception_anr');
                            } elseif ($requestParams['type'] == -1) {
                                return $query->where('event_name', 'exception_start');
                            }
                            return $query;
                        })
                        ->when(!empty($requestParams['use_duration']), function (Builder $query) use ($requestParams) {
                            return $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$requestParams['use_duration']}");
                        })
                        ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                            return $query->whereRaw($keywordFilterService->getFilterSql());
                        })
                        ->when($whereNotInAppVersion, function (Builder $query, $whereNotInAppVersion) {
                            return $query->whereNotIn('app_version', $whereNotInAppVersion);
                        })
                        ->when($whereNotInExceptionBlockId, function (Builder $query, $whereNotInExceptionBlockId) {
                            return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                                return strtoupper($item);
                            }, $whereNotInExceptionBlockId));
                        })
                        ->when(BaseModel::FILTER_PACKAGE_NAME[$extraAppId] ?? false, function ($query) use ($extraAppId) { // 屏蔽包名
                            return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$extraAppId]);
                        })
                        ->when(!empty($requestParams['crash_type']), function ($query) use ($requestParams) { // 屏蔽包名
                            return $query->whereIn('type', json_decode($requestParams['crash_type'], true));
                        })
                        ->when(!empty($requestParams["{$field}_request"]), function ($query) use ($requestParams, $field) { // 过滤条件
                            return $query->whereIn($field, $requestParams["{$field}_request"]);
                        })
                        ->groupBy($field)
                        ->orderBy('num', 'desc')
                        ->limit($requestParams['limit'] ?? 10);
                } catch (\Exception $e) {
                    $ExceptionStream = [];
                }
                $queries[$field] = $ExceptionStream;
            }
            $list = [];
            $results = $ClickHouse->getMultiSqlData($queries);
            foreach ($results as $field => $result) {
                $sum = array_sum(array_column($result, 'num'));
                foreach ($result as $key => $item) {
                    $result[$key]['percent'] = bcadd(number_format(DataHelper::divide($item['num'] ?? 0, $sum, 4) * 100, 2), 0, 2);
                }
                $list[$field] = $result;
            }
            return $list;
        }, RedisHandler::INIT['coldDataTimeout']);
    }

    /**
     * author : <EMAIL>
     * datetime: 2023/07/18 15:35
     * memo : 異常詳情--版本信息
     * @param int $extraAppId
     * @param string $eventName
     * @param string $exceptionBlockId
     * @param array $streamTime
     * @param int $pageIndex
     * @param int $pageLimit
     * @param string $sortField
     * @param string $sortType
     * @return array
     */
    public function pullAppVersionList(int $extraAppId, string $eventName, string $exceptionBlockId, array $streamTime, int $pageIndex, int $pageLimit, string $sortField, string $sortType): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:PullAppVersionList:'] . md5(json_encode(func_get_args()));
        return RedisHandler::autoGet($redisKey, function () use ($extraAppId, $eventName, $exceptionBlockId, $streamTime, $pageIndex, $pageLimit, $sortField, $sortType) {
            $result = (new ClickHouse())->getData((new UserLogDataAll())
                    ->selectRaw("`app_version` AS `app_version_name`, COUNT( 1 ) AS `app_version_count`, COUNT( DISTINCT `server_dev_str` ) AS `app_version_user_count`, MIN( `stream_time` ) AS first_report_time, MAX( `stream_time` ) AS `last_report_time`")
                    ->where('extra_app_id', $extraAppId)
                    ->when($eventName, function (Builder $query, $eventName) {
                        return $query->where('event_name', $eventName);
                    })
                    ->when($exceptionBlockId, function (Builder $query, $exceptionBlockId) {
                        return $query->where(DB::raw('upper(exception_block_id)'), strtoupper($exceptionBlockId));
                    })
                    ->when($streamTime, function (Builder $query, $streamTime) {
                        return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
                    })
                    ->where('app_version', '!=', '')
                    ->groupBy('app_version')
                    ->orderBy($sortField, $sortType)
            );
            $pagination = CommonHelper::pagination($result, $pageIndex, $pageLimit);
            return [$pagination['total'], $pagination['list']];
        }, RedisHandler::INIT['coldDataTimeout']);
    }

    /**
     * 获取异常记录的主堆栈信息
     * @param array $params
     * @return
     */
    public function getExceptionOriginStacks(array $params)
    {
        return ExceptionQueryModelFactory::getExceptionQueryModel($params['type'])::query()
            ->where('exception_block_id', $params['exception_block_id'])
            ->where('extra_app_id', $params['developer_app_id'])
            ->orderByDesc('event_time')
            ->select(['origin_stacks_json AS origin_stacks'])
            ->firstFromCK();
    }
}
