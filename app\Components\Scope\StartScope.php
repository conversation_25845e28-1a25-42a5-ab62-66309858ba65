<?php
/**
 * StartScope.php
 *
 * User: Dican
 * Date: 2022/10/9
 * Email: <<EMAIL>>
 */

namespace App\Components\Scope;


use Illuminate\Database\Eloquent\Builder;

class StartScope extends Scope
{

    public $isEmulator;//是否为模拟器

    public function getBuilder(Builder $builder)
    {
        return parent::getBuilder($builder)
            ->when(!empty($this->defaultStartDate) && !empty($this->defaultEndDate), function ($query) {
                // $query->whereBetween('event_time', [$this->startTime, $this->endTime]);
            })->when(isset($this->isEmulator), function ($query) {
                $query->where('is_emulator', $this->isEmulator);
            })->when($this->useDuration, function ($query) {
                $query->havingRaw("CAST(JSONExtractString ( `basic_info_json`, 'use_duration' ) AS Nullable ( Int32 )) <= " . $this->useDuration);
            });
    }
}
