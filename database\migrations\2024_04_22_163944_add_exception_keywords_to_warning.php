<?php

/**
 * 添加异常关键词字段到warning表
 * @desc 添加异常关键词字段到warning表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/04/22
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddExceptionKeywordsToWarning extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->json('exception_keywords')->comment('异常关键词列表：json格式：["关键词1","关键词2","关键词3"]');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->dropColumn('exception_keywords');
        });
    }
}
