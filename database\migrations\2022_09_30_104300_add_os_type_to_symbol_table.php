<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOsTypeToSymbolTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->unsignedTinyInteger('os_type')->default(0)->after('type')->comment('平台类型;1为安卓,2为iOS');
            $table->unique(['developer_app_id', 'type', 'os_type', 'app_version']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->dropColumn('os_type');
        });
    }
}
