<?php

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Http\Logic\TapdLogic;
use App\Http\Validation\TapdValidation;
use App\Model\TapdAutoConfig;
use App\Model\TapdBug;
use App\Service\Exception\Tapd\Create;
use App\Service\Exception\Tapd\Handlers;
use App\Service\Exception\Tapd\Iteration;
use App\Service\Exception\Tapd\Relation;
use App\Service\Exception\Tapd\Releases;
use App\Service\Exception\Tapd\Version;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class TapdController extends BaseController
{
    /**
     * 创建缺陷——手动创建
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5703
     * @return JsonResponse
     */
    public function create(): JsonResponse
    {
        $params = TapdValidation::build()->developerAppId()->exceptionBlockId()
            ->title()->description()->reporterId()->reporter()->type()
            ->tapdAccountId()->severity()->createType()->versionReport()
            ->iterationId()->releaseId()->priority()->currentOwner()
            ->validate();
        try {
            [$isSuccess, $code, $bugId] = (new Create($params))->save();
            //判断创建是否成功
            if ($isSuccess) {
                return $this->response($code, compact('bugId'));
            } else {
                return $this->response($code);
            }
        } catch (\Exception $e) {
            Log::error('创建tapd缺陷接口错误,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 关联缺陷
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12265
     * @return JsonResponse
     */
    public function relation(): JsonResponse
    {
        $params = TapdValidation::build()
            ->developerAppId()->exceptionBlockId()->type()->bugId()
            ->validate();
        try {
            $params['reporter_id'] = Auth::id();
            [$isSuccess, $code, $bugId] = (new Relation($params))->save();
            //判断创建是否成功
            if ($isSuccess) {
                return $this->response($code, compact('bugId'));
            }
            return $this->response($code);
        } catch (\Exception $e) {
            Log::error('关联tapd缺陷接口错误,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 查看缺陷——返回tapd跳转链接
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5704
     * @return JsonResponse
     */
    public function link(): JsonResponse
    {
        $params = TapdValidation::build()->developerAppId()->exceptionBlockId()->bugId()->validate();
        $tapdLink = TapdBug::getTapdLink($params);
        return $this->response(0, compact('tapdLink'));
    }

    /**
     * 解绑tapd缺陷
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5705
     * @return JsonResponse
     */
    public function untie(): JsonResponse
    {
        $params = TapdValidation::build()->developerAppId()->exceptionBlockId()->bugId()->validate();
        TapdBug::untieTapd($params);
        return $this->response();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/20 16:45
     * memo : 操作項目綁定
     */
    public function itemBindHandler(Request $request)
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $developerAppId = intval($request->developer_app_id);
        $tapdAccountId = intval($request->id ?? 0);
        $itemId = (string)$request->item_id ?? '';
        $bindStatus = intval($request->bind_status ?? 1);
        $re = (new TapdLogic())->itemBindHandler($developerAppId, $tapdAccountId, $itemId, $bindStatus);
        if (is_array($re) && isset($re['code']) && !empty($re['code'])) {
            return $this->response($re['code']);
        }
        return $this->response(0, $re);
    }

    /**
     * author : <EMAIL>
     * datetime: 2023/07/20 16:46
     * memo : 拉取缺陷狀態--列表
     */
    public function pullBugStatusList(Request $request)
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'tapd_bug_id_list' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $return = (new TapdLogic())->pullBugStatusList($request->developer_app_id, $request->tapd_bug_id_list ?? '');
        return $this->response(0, $return);
    }

    /**
     * 获取缺陷处理人列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5715
     * @return JsonResponse
     */
    public function handler(): JsonResponse
    {
        $params = TapdValidation::build()->itemId()->validate();
        $result = (new Handlers($params['item_id']))->get();
        return $this->response(0, $result);
    }

    /**
     * 获取tapd迭代列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5717
     * @return JsonResponse
     */
    public function iteration(): JsonResponse
    {
        $params = TapdValidation::build()->itemId()->validate();
        $result = (new Iteration($params['item_id']))->get();
        return $this->response(0, $result);
    }

    /**
     * 获取tapd项目发布计划
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5718
     * @return JsonResponse
     */
    public function releases(): JsonResponse
    {
        $params = TapdValidation::build()->itemId()->validate();
        $result = (new Releases($params['item_id']))->get();
        return $this->response(0, $result);
    }

    /**
     * 获取tapd项目发现版本
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5728
     * @return JsonResponse
     */
    public function versions(): JsonResponse
    {
        $params = TapdValidation::build()->itemId()->validate();
        $result = (new Version($params['item_id']))->get();
        return $this->response(0, $result);
    }

    /**
     * Tapd自动提单配置
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5729
     * @return JsonResponse
     */
    public function getTapdAutoConfig()
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        // 查询数据
        $list = TapdAutoConfig::query()->where('developer_app_id', $this->request['developer_app_id'])->first();
        // 设置默认值
        if (empty($list)) {
            $list = [
                'id' => 0,
                'developer_app_id' => $this->request['developer_app_id'],
                'trigger_time' => '00:00',
                'max_num' => 500,
                'status' => 0,
                'server_id' => [],
                'inner_version' => [],
                'user_map' => [],
                'white_filter' => [],
                'webhook_url' => [],
                'package_name' => [],
                'keywords' => [],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        // 返回
        return $this->response(0, $list);
    }

    /**
     * Tapd自动提单配置
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5729
     * @return JsonResponse
     */
    public function saveTapdAutoConfig()
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'trigger_time' => 'required|string',
            'max_num' => 'required|integer|max:500',
            'status' => 'required|integer|in:0,1',
            'server_id' => 'nullable|array',
            'inner_version' => 'nullable|array',
            'user_map' => 'nullable|array',
            'white_filter' => 'nullable|array',
            'webhook_url' => 'nullable|array',
            'package_name' => 'nullable|array',
            'keywords' => 'nullable|array',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        // 保存数据
        TapdAutoConfig::query()->updateOrCreate(['developer_app_id' => $this->request['developer_app_id']], $this->request);
        // 返回
        return $this->response(0);
    }
}
