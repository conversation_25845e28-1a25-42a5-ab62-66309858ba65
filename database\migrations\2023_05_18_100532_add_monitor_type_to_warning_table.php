<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMonitorTypeToWarningTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->unsignedTinyInteger('monitor_type')->default(2)->comment('监控类型 1：大盘监控 2：问题监控');
            $table->json('receiving_phone')->comment('接收手机');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->dropColumn('monitor_type');
            $table->dropColumn('receiving_phone');
        });
    }
}
