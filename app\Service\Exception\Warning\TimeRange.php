<?php

/**
 * 预警范围服务类
 * @desc 预警范围服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Warning;

use App\Model\WarningRecord;
use App\Model\WarningRule;

class TimeRange extends Base
{
    /**
     * 获取符合这个时间范围的预警记录
     *
     * @return array
     */
    public function warningTimeRange(): array
    {
        // 获取预警规则
        $warningRule = WarningRule::query()
            ->where('warning_id', $this->params['warning_id'])->first();
        if (empty($warningRule)) {
            return [[], ''];
        }
        $warningRule = $warningRule->toArray();
        $timeRange = $this->getTimeRange($warningRule['schedule_time']); // 获取时间范围
        $timeType = $warningRule['schedule_time'] == 3600 ? 'hour' : 'day';
        // 获取当前时间范围下所有的预警记录时间段
        $warningRecords = WarningRecord::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('warning_id', $this->params['warning_id']);
        // 判断当前是否有传记录ID，有则直接读取记录ID, ID和时间或者的关系
        $warningRecords = $warningRecords->where(function ($query) use ($timeRange) {
            if (!empty($this->params['warning_record_id'])) {
                $query->where('id', $this->params['warning_record_id']);
            }
            $query->orWhere(function ($query) use ($timeRange) {
                $query->whereBetween('start_date', [$timeRange['start_date'], $timeRange['end_date']])
                    ->whereBetween('end_date', [$timeRange['start_date'], $timeRange['end_date']]);
            });
        });
        $warningRecords = $warningRecords->get();

        return [$warningRecords, $timeType];
    }

    /**
     * 获取时间范围
     *
     * @param $timeType
     * @return array
     */
    private function getTimeRange($timeType): array
    {
        $timeRange = array();
        // 判断当前预警规则的时间维度
        switch ($timeType) {
            case 3600:
                // 小时维度，默认前推24小时
                $currentTimestamp = time();  // 获取当前时间戳
                $timeRange['start_date'] = date('Y-m-d H:00:00', strtotime('-24 hours', $currentTimestamp));
                $timeRange['end_date'] = date('Y-m-d H:00:00', $currentTimestamp);
                break;
            case 86400:
                // 天维度，默认前7天
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                $timeRange['end_date'] = $yesterday . ' 23:59:59';  // 当前日期的结束时间
                $timeRange['start_date'] = date('Y-m-d 00:00:00', strtotime('-6 days', strtotime($timeRange['end_date'] . ' -1 second')));  // 往前推7天的开始时间;
                break;
        }
        return $timeRange;
    }
}
