{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5|^8.0", "box/spout": "^3.3", "fideloper/proxy": "^4.4", "guzzlehttp/guzzle": "^6.3", "laravel/framework": "^6.20.26", "laravel/tinker": "^2.5", "overtrue/laravel-wechat": "5.0.3", "spatie/laravel-permission": "5.3.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "2.6.4", "doctrine/dbal": "2.13.9", "facade/ignition": "^1.16.15", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "qcloudsms/qcloudsms_php": "0.1.*", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}