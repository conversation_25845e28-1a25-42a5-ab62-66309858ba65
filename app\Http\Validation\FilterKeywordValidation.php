<?php

/**
 * 过滤关键词表单校验
 * @desc 过滤关键词表单校验
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/10/25
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

/**
 * @method static FilterKeywordValidation build()
 */
class FilterKeywordValidation extends BaseValidation
{
    /**
     * 效能后台应用ID
     *
     * @return $this
     */
    public function developerAppId(): FilterKeywordValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 关键词
     *
     * @return $this
     */
    public function keyword(): FilterKeywordValidation
    {
        $this->rules['keyword'] = 'required|string';
        return $this;
    }

    /**
     * 是否纳入统计
     *
     * @return $this
     */
    public function isStat(): FilterKeywordValidation
    {
        $this->rules['is_stat'] = 'integer';
        return $this;
    }

    /**
     * UUID
     *
     * @return $this
     */
    public function uuid(): FilterKeywordValidation
    {
        $this->rules['uuid'] = 'required|string';
        return $this;
    }

    /**
     * 页码
     *
     * @return $this
     */
    public function page(): FilterKeywordValidation
    {
        $this->rules['page'] = 'int';
        return $this;
    }

    /**
     * 分页大小
     *
     * @return $this
     */
    public function perPage(): FilterKeywordValidation
    {
        $this->rules['per_page'] = 'int';
        return $this;
    }
}
