<?php

/**
 * starRocks数据仓库相关配置
 * @desc starRocks数据仓库相关配置
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/21
 * @todo 这里是后续需要跟进的功能说明
 */

return [
    //数据仓地址
    'starRocks_host_perf' => env('STAR_ROCKS_HOST_PERF', ''),
    //数据仓的用户名
    'starRocks_username_perf' => env('STAR_ROCKS_USERNAME_PERF', ''),
    //数据仓的密码
    'starRocks_password_perf' => env('STAR_ROCKS_PASSWORD_PERF', ''),
    //数据仓的数据库
    'starRocks_database_perf' => env('STAR_ROCKS_DATABASE_PERF', 'sdk_log'),
    //数据仓的端口
    'starRocks_port_perf' => env('STAR_ROCKS_PORT_PERF', 9030),
];
