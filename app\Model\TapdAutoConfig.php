<?php

/**
 * tapd自动提单配置
 * @desc tapd自动提单配置
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/06
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model;

class TapdAutoConfig extends BaseModel
{
    protected $table = 'tapd_auto_config';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'trigger_time',
        'max_num',
        'status',
        'server_id',
        'inner_version',
        'user_map',
        'white_filter',
        'webhook_url',
        'created_at',
        'updated_at',
        'package_name',
        'keywords',
    ];

    /**
     * 强制转换的属性
     *
     * @var array
     */
    protected $casts = [
        'server_id' => 'array',
        'inner_version' => 'array',
        'user_map' => 'array',
        'white_filter' => 'array',
        'webhook_url' => 'array',
        'package_name' => 'array',
        'keywords' => 'array',
    ];
}
