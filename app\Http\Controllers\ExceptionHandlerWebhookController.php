<?php

/**
 * 异常处理人巡检配置表控制器
 * @desc 异常处理人巡检配置表控制器
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2025/04/09
 */

namespace App\Http\Controllers;

use App\Model\ExceptionHandlerWebhook;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ExceptionHandlerWebhookController extends Controller
{
    /**
     * 获取异常处理人巡检配置列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            $query = ExceptionHandlerWebhook::query();
            // 根据developer_app_id搜索
            $query->where('developer_app_id', $request->developer_app_id);
            // 分页
            $data = $query->orderBy('id', 'desc')->paginate($request->input('per_page', 15));
            // 返回数据
            return $this->response(0, [
                'total' => $data->total(),
                'list' => $data->items()
            ]);
        } catch (\Exception $e) {
            Log::error('获取异常处理人巡检配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1000, [], $e->getMessage());
        }
    }

    /**
     * 创建异常处理人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'frequency' => 'required|integer|in:1,2,3',
                'range' => 'required|integer|in:1,2,3,4,5',
                'webhook_urls' => 'required|array',
                'status' => 'required|integer|in:0,1',
                'sort_field' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 创建异常处理人巡检配置
            $webhook = new ExceptionHandlerWebhook($request->only([
                'developer_app_id',
                'frequency',
                'range',
                'webhook_urls',
                'status',
                'sort_field'
            ]));

            $webhook->operator = Auth::user()->alias;
            $webhook->save();

            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('创建异常处理人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 更新异常处理人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer',
                'frequency' => 'required|integer|in:1,2,3',
                'range' => 'required|integer|in:1,2,3,4,5',
                'webhook_urls' => 'required|array',
                'status' => 'required|integer|in:0,1',
                'sort_field' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 更新异常处理人巡检配置
            ExceptionHandlerWebhook::query()->where('id', $request->id)->update([
                'frequency' => $request->frequency,
                'range' => $request->range,
                'webhook_urls' => $request->webhook_urls,
                'status' => $request->status,
                'sort_field' => $request->sort_field,
                'operator' => Auth::user()->alias
            ]);
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('更新异常处理人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 删除异常处理人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 删除异常处理人巡检配置
            ExceptionHandlerWebhook::where('id', $request->id)->delete();
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('删除异常处理人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 更新异常处理人巡检配置状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function status(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer',
                'status' => 'required|integer|in:0,1',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 更新异常处理人巡检配置状态
            ExceptionHandlerWebhook::query()->where('id', $request->id)->update([
                'status' => $request->status,
                'operator' => Auth::user()->alias
            ]);
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('更新异常处理人巡检配置状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
