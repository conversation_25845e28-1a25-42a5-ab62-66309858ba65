<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><PERSON>a Email: zhang<PERSON><EMAIL>
 * Date: 2022/6/2 11:09
 * 直接调用user_log_data_all库的封装
 */

namespace App\Model\ClickHouse;

use Arr;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Eloquent
 * @mixin Builder
 * @method static \Illuminate\Database\Eloquent\Builder getFromCK()
 * @method static \Illuminate\Database\Eloquent\Builder firstFromCK()
 */
class UserLogDataAll extends Model
{
    protected $table = 'exception_stream_all';
    //需转换类型的字段
    protected $casts = [];

    const PROJECT_EXCEPTION = 9;

    /**
     * 格式化时间时间格式
     */
    const FORMAT_EVENT_TIME = "FROM_UNIXTIME(`stream_time`,'%Y-%m-%d')";
    const FORMAT_DEV_CREATE_TIME = "FROM_UNIXTIME(`dev_create_time`,'%Y-%m-%d')";
    const FORMAT_EVENT_HOUR = "FROM_UNIXTIME(`stream_time`,'%Y-%m-%d %H')";
    const FORMAT_DEV_CREATE_HOUR = "FROM_UNIXTIME(`dev_create_time`,'%Y-%m-%d %H')";
    /**
     * @var null 在ck里面表示数据项目
     */
    static $projectId = self::PROJECT_EXCEPTION;

    /**
     * 查询构建
     * @return UserLogDataAll
     */
    public static function query()
    {
        if (empty(static::$projectId)) {
            return (new static());
        }
        return (new static())->where('project_id', static::$projectId);
    }

    /**
     * 查询执行
     * @param $query
     * @return array
     */
    public function scopeGetFromCK($query)
    {
        $data = \ClickHouse::getData($query);
        if (empty($this->casts)) {
            return $data;
        }

        //格式化需要转移的字段
        foreach ($data as $key => $datum) {
            foreach ($datum as $column => $value) {
                $formatType = $this->casts[$column] ?? null;
                $data[$key][$column] = $this->getFormatValue($formatType, $value);
            }
        }
        return $data;
    }

    /**
     * 执行first操作
     * @param $query
     * @return mixed|null
     */
    public function scopeFirstFromCk($query)
    {
        $query = $query->limit(1);
        $data = \ClickHouse::getData($query);
        $ret = is_array($data) ? Arr::first($data) : [];
        $ret = empty($ret) ? [] : $ret;

        if (empty($this->casts)) {
            return $ret;
        }

        //格式化需要转移的字段
        foreach ($ret as $column => $value) {
            $formatType = $this->casts[$column] ?? null;
            $ret[$column] = $this->getFormatValue($formatType, $value);
        }
        return $ret;
    }

    /**
     * 格式化返回的数据
     * @param $type
     * @param $value
     * @return mixed
     */
    public function getFormatValue($type, $value)
    {
        switch ($type) {
            case 'array':
            case 'json':
                $value = json_decode($value, true) ?? [];
                break;
        }
        return $value;
    }
}
