<?php

namespace App\Http\Validation;

/**
 * @method static TapdValidation build()
 */
class TapdValidation extends BaseValidation
{
    public function developerAppId(): TapdValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    public function exceptionBlockId(): TapdValidation
    {
        $this->rules['exception_block_id'] = 'required|string';
        return $this;
    }

    public function bugId(): TapdValidation
    {
        $this->rules['bug_id'] = 'required|string';
        return $this;
    }

    public function itemId(): TapdValidation
    {
        $this->rules['item_id'] = 'required|string';
        return $this;
    }

    /**
     * 缺陷标题
     *
     * @return $this
     */
    public function title(): TapdValidation
    {
        $this->rules['title'] = 'required|string';
        return $this;
    }

    /**
     * 缺陷详情
     *
     * @return $this
     */
    public function description(): TapdValidation
    {
        $this->rules['description'] = 'required|string';
        return $this;
    }

    /**
     * 创建人id
     *
     * @return $this
     */
    public function reporterId(): TapdValidation
    {
        $this->rules['reporter_id'] = 'required|integer';
        return $this;
    }

    /**
     * 创建人
     *
     * @return $this
     */
    public function reporter(): TapdValidation
    {
        $this->rules['reporter'] = 'required|string';
        return $this;
    }

    /**
     * 异常类型
     *
     * @return $this
     */
    public function type(): TapdValidation
    {
        $this->rules['type'] = 'required|integer';
        return $this;
    }

    /**
     * tap绑定表id
     *
     * @return $this
     */
    public function tapdAccountId(): TapdValidation
    {
        $this->rules['tapd_account_id'] = 'nullable|integer';
        return $this;
    }

    /**
     * 严重程度
     *
     * @return $this
     */
    public function severity(): TapdValidation
    {
        $this->rules['severity'] = 'nullable|string';
        return $this;
    }

    /**
     * 创建类型
     *
     * @return $this
     */
    public function createType(): TapdValidation
    {
        $this->rules['create_type'] = 'nullable|integer';
        return $this;
    }

    /**
     * 发现版本
     *
     * @return $this
     */
    public function versionReport(): TapdValidation
    {
        $this->rules['version_report'] = 'nullable|string';
        return $this;
    }

    /**
     * 迭代
     *
     * @return $this
     */
    public function iterationId(): TapdValidation
    {
        $this->rules['iteration_id'] = 'nullable|string';
        return $this;
    }

    /**
     * 发布计划
     *
     * @return $this
     */
    public function releaseId(): TapdValidation
    {
        $this->rules['release_id'] = 'nullable|integer';
        return $this;
    }

    /**
     * 优先级
     *
     * @return $this
     */
    public function priority(): TapdValidation
    {
        $this->rules['priority'] = 'nullable|string';
        return $this;
    }

    /**
     * 处理人
     *
     * @return $this
     */
    public function currentOwner(): TapdValidation
    {
        $this->rules['current_owner'] = 'nullable|string';
        return $this;
    }
}
