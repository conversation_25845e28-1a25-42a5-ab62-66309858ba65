<?php

namespace App\Model\ClickHouse;

class Anr extends UserLogDataAll
{
    protected $casts = [
        'detail_info' => 'array', 'memory_info' => 'array', 'expand' => 'array',
        'console_info' => 'array', 'page_info' => 'array', 'uuid_info' => 'array',
    ];

    static $projectId = self::PROJECT_EXCEPTION;

    public static function query()
    {
        return parent::query()->where('event_name', 'exception_anr');
    }

    public const COUNT = "ANR次数";
    public const RATE = "ANR率";
    public const USER_COUNT = "ANR人数";
    public const USER_RATE = "ANR用户比例";
}
