<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTapdAccountTable extends Migration
{

    /**
     * Run the migrations.
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('tapd_account', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('developer_app_id');
            $table->string('item_id')->comment('项目ID');
            $table->integer('bind_status')->default(1)->comment('绑定状态，值：0未绑定，1已绑定');
            $table->timestamps();
        });
        \DB::connection('exception')->statement("ALTER TABLE `tapd_account` comment 'tapd项目表'");
    }

    /**
     * Reverse the migrations.
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('tapd_account');
    }

}
