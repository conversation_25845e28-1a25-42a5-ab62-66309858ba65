<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLabelTable extends Migration
{

    /**
     * Run the migrations.
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('label', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('developer_app_id');
            //分組條件[START]
            $table->string('subject_name',100)->comment('错误名称');
            $table->text('explain')->comment('崩溃详情');
            $table->integer('type')->comment('公共类型');
            $table->string('os_type',100)->comment('系统类型，值：1android，2ios，3windows');
            $table->string('event_name',100)->comment('事件名称，值：exception_crash/崩溃，exception_error/错误');
            $table->string('exception_unique_id',100)->default('')->comment('[唯一的]异常标识/clickhouse（已废弃）');
            //分組條件[END]
            $table->string('exception_unique_index',100)->comment('[唯一的]异常标识/label');
            $table->string('label_name', 100)->comment('标签名称');
            $table->timestamps();
            $table->unique(['developer_app_id', 'label_name', 'exception_unique_index'],'developerappid_labelname_exceptionuniqueindex');
            $table->index(['developer_app_id', 'label_name'],'developer_app_id_label_name');
            // $table->index(['exception_index'],'exception_index');//
        });
        \DB::connection('exception')->statement("ALTER TABLE `label` comment '标签管理'");
    }

    /**
     * Reverse the migrations.
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('label');
    }

}
