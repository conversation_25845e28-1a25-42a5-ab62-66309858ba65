<?php

namespace App\Http\Controllers;

use App\Http\Logic\HomeLogic;
use App\Service\Exception\Home\CrashRankList;
use App\Service\Exception\Home\ErrorRankList;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class HomeController extends Controller
{

    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request->all();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/06/30 10:56
     * memo : 排行數據/http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5534
     */
    public function rankCommonList(Request $request): JsonResponse
    {
        $cookie = $request->header('Cookie');
        $type = $request->type ?? 'LATEST30';
        $list = (new HomeLogic())->rankCommonList($cookie, $type);
        return $this->response(0, $list);
    }

    /**
     * 首页联网设备数
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/07
     * @doc
     */
    public function networkNumList(Request $request): JsonResponse
    {
        $cookie = $request->header('Cookie');
        $type = $request->type ?? 'LATEST30';
        $list = (new HomeLogic())->networkNumList($cookie, $type);
        return $this->response(0, $list);
    }

    /**
     * 首页崩溃率排行
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/07
     * @doc
     */
    public function crashRankList(Request $request): JsonResponse
    {
        $list = (new CrashRankList($request))->list();
        return $this->response(0, $list);
    }

    /**
     * 首页错误率排行
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/07
     * @doc
     */
    public function errorRankList(Request $request): JsonResponse
    {
        $list = (new ErrorRankList($request))->list();
        return $this->response(0, $list);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/06/30 10:57
     * memo : 排行搜索/http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5535
     */
    public function rankSearchList(Request $request): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'type' => 'string',//enum: day week month
        ]);
        if ($validator->fails()) return $this->response(1000);
        $cookie = $request->header('Cookie');
        $type = $request->type ?? 'day';
        $keyword = $request->keyword ?? '';
        $list = (new HomeLogic())->rankSearchList($cookie, $type, $keyword);
        return $this->response(0, $list);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/03 16:51
     * memo : 圖標（hitbug）/http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5536
     */
    public function hitbugChart(Request $request): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) return $this->response(1000);
        $developerAppId = intval($request->developer_app_id);
        $startTime = strtotime($request->start_date ?? (date("Y-m-d", strtotime("-6 days")) . ' 00:00:00'));
        $endTime = strtotime($request->end_date ?? (date('Y-m-d', time()) . ' 23:59:59'));
        $list = (new HomeLogic())->hitbugChart($developerAppId, $startTime, $endTime);
        return $this->response(0, $list);
    }


}
