<?php

/**
 * 统计数据脚本
 * @desc 统计数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Http\Logic\HomeLogic;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class LoadHomeDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'load:home:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '加载首页数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //打印日志
            Log::info("执行加载首页数据脚本开始");
            // 加载首页数据
            $list = HomeLogic::$datePeriodType;
            // 移除年的
            unset($list['LATEST_YEAR']);
            // 设置获取的条件
            (new HomeLogic())->loadHomeDataCache($list);
            //打印日志
            Log::info("执行加载首页数据脚本完成");
        } catch (\Exception $e) {
            Log::error("执行加载首页数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
