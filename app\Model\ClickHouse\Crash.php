<?php
/**
 * 异常崩溃上报CK模型
 * Crash.php
 *
 * User: Dican
 * Date: 2022/9/21
 * Email: <<EMAIL>>
 */

namespace App\Model\ClickHouse;


class Crash extends UserLogDataAll
{
    protected $casts = [
        'detail_info' => 'array', 'memory_info' => 'array', 'expand' => 'array',
        'console_info' => 'array', 'page_info' => 'array', 'uuid_info' => 'array',
    ];

    static $projectId = self::PROJECT_EXCEPTION;

    public static function query()
    {
        return parent::query()->where('event_name', 'exception_crash');
    }

    public const COUNT = "崩溃次数";
    public const RATE = "崩溃率";
    public const USER_COUNT = "崩溃人数";
    public const USER_RATE = "崩溃用户比例";

}
