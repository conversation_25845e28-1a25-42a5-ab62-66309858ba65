<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCategoryIndexToRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            //
            $table->dropIndex(['developer_app_id', 'type', 'name']);
            $table->index(['developer_app_id', 'type', 'name', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            $table->index(['developer_app_id', 'type', 'name']);
            $table->dropIndex(['developer_app_id', 'type', 'name', 'category']);
        });
    }
}
