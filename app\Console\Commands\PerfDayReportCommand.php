<?php

/**
 * 日常巡检脚本
 * @desc 日常巡检脚本
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/10/15
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\Helper\Curl;
use App\Model\AppModel;
use App\Model\InspectionConfig;
use App\Model\Record;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\VersionWhiteList;
use App\Service\Exception\KeyWordFilter;
use App\Service\WXGroupNoticeService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PerfDayReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perf:day:report {id? : 配置ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '日常巡检脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        $id = $this->argument('id');
        // 获取数据
        $list = InspectionConfig::query()
            ->where('status', 1)
            ->where('category', 1)
            ->when($id, function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->orderBy('created_at')
            ->get();
        //获取APP名称
        $apps = AppModel::query()->pluck('app_name', 'id');
        //当前时间
        $currentDate = date('H') . ':00';
        //循环处理
        foreach ($list as $config) {
            // 判断触发时间，如果不等于则跳过
            if ($currentDate != $config['trigger_time']) {
                continue;
            }
            $data = $this->getData($config['developer_app_id'], $config['os_type']);
            $this->sendMessage($config, $data, $apps[$config['developer_app_id']]);
        }
    }

    /**
     * 获取数据
     *
     * @param $developerAppId
     * @param $osType
     * @return array
     */
    private function getData($developerAppId, $osType)
    {
        //过滤白名单
        $keywordFilterService = new KeyWordFilter($developerAppId);
        $keywordFilter = $keywordFilterService->getNotStatKeyword();
        $keywordFilterService->setDate(Carbon::yesterday()->toDateString());
        $appVersionWhite = VersionWhiteList::getVersionList($developerAppId);
        $crashExceptionBlockIdWhite = Record::getExceptionBlockIds($developerAppId, 1);
        $errorExceptionBlockIdWhite = Record::getExceptionBlockIds($developerAppId, 2);
        $data = ExceptionStreamAll::query()
            ->selectRaw("event_name, count(1) as num, count(distinct server_dev_str) as dev_num")
            ->where('extra_app_id', $developerAppId)
            ->where('stream_date', Carbon::yesterday()->toDateString())
            ->where('os_type', $osType)
            ->when($appVersionWhite, function ($query, $appVersionWhite) {
                $date = Carbon::yesterday()->toDateString();
                $version = "'" . implode("','", $appVersionWhite) . "'";
                return $query->whereRaw("(upper(exception_block_id) not in (select upper(exception_block_id) from exception_stream_all where stream_date = '{$date}' and app_version in ({$version}))  or exception_block_id is null)");
            })
            ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                return $query->where(function ($q) use ($keywordFilterService) {
                    return $q->whereRaw($keywordFilterService->getFilterSql())->orWhereRaw('exception_block_id is null');
                });
            })
            ->when($crashExceptionBlockIdWhite, function ($query, $crashExceptionBlockIdWhite) {
                return $query->where(function ($q) use ($crashExceptionBlockIdWhite) {
                    return $q->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, $crashExceptionBlockIdWhite))->orWhereRaw('exception_block_id is null');
                });
            })
            ->when($errorExceptionBlockIdWhite, function ($query, $errorExceptionBlockIdWhite) {
                return $query->where(function ($q) use ($errorExceptionBlockIdWhite) {
                    return $q->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, $errorExceptionBlockIdWhite))->orWhereRaw('exception_block_id is null');
                });
            })
            ->groupBy('event_name')
            ->orderByRaw('event_name')
            ->getFromSR();

        // 获取线上性能的数据
        $response = Curl::get("https://tool-manager.shiyue.com/home/<USER>");

        return [
            'hitbug' => array_column($data, null, 'event_name'),
            'perfmate' => json_decode($response, true),
        ];
    }

    /**
     * 推送消息
     *
     * @param $config
     * @param $data
     * @param $appName
     * @return void
     */
    private function sendMessage($config, $data, $appName)
    {
        $osTypeText = [1 => 'Android', 2 => 'iOS', 3 => 'PC', 4 => 'MINI', 5 => 'HARMONY'];
        // 获取操作系统名称
        $osTypeText = $osTypeText[$config['os_type']] ?? '';
        $date = date('Y年m月d日', Carbon::yesterday()->timestamp);

        $perfMateScoreNum = $data['perfmate']['score_num'];
        $perfmateLowRate = round(bcdiv($data['perfmate']['low_score_num'], $perfMateScoreNum > 0 ? $perfMateScoreNum : 1, 6) * 100, 2);
        $perfmateMemory = "内存平均峰值：{$data['perfmate']['avg_memory_score']}M（建议值不高于1600M）";
        if ($data['perfmate']['avg_memory_score'] > 1600) {
            $perfmateMemory = "<font color='red'>{$perfmateMemory}【异常】</font>";
        }
        $perfmateSmoothnes = "游戏卡顿率：{$data['perfmate']['smoothness_score']}%（建议值不高于2%）";
        if ($data['perfmate']['smoothness_score'] > 2) {
            $perfmateSmoothnes = "<font color='red'>{$perfmateSmoothnes}【异常】</font>";
        }
        $perfmateScore = "性能平均分数：{$data['perfmate']['score']}分（建议值80）";
        if ($data['perfmate']['score'] < 80) {
            $perfmateScore = "<font color='red'>{$perfmateScore}【异常】</font>";
        }
        $perfmateBatteryPower = "功耗平均值：{$data['perfmate']['battery_power']}mW";
        if ($data['perfmate']['battery_power'] <= 0) {
            $perfmateBatteryPower = "";
        }
        $battleStutter = "游戏战斗场景卡顿率：{$data['perfmate']['battle_stutter']}%（建议值不高于2%）";
        if ($data['perfmate']['battle_stutter'] > 2) {
            $battleStutter = "<font color='red'>{$battleStutter}【异常】</font>";
        }

        // hitbug
        $hitbugExceptionStartNum = $data['hitbug']['exception_start']['num'] ?? 0;
        $hitbugCrashNumRate = round(bcdiv($data['hitbug']['exception_crash']['num'] ?? 0, $hitbugExceptionStartNum > 0 ? $hitbugExceptionStartNum : 1, 6) * 100, 2);
        $hitbugCrashNumText = "崩溃次数率：{$hitbugCrashNumRate}% （建议值不高于1%）";
        if ($hitbugCrashNumRate > 2) {
            $hitbugCrashNumText = "<font color='red'>{$hitbugCrashNumText}【异常】</font>";
        }
        $hitbugExceptionStartDevNum = $data['hitbug']['exception_start']['dev_num'] ?? 0;
        $hitbugCrashDevNumRate = round(bcdiv($data['hitbug']['exception_crash']['dev_num'] ?? 0, $hitbugExceptionStartDevNum > 0 ? $hitbugExceptionStartDevNum : 1, 6) * 100, 2);
        $hitbugCrashNumDevText = "崩溃设备率：{$hitbugCrashDevNumRate}% （建议值不高于2%）";
        if ($hitbugCrashDevNumRate > 2) {
            $hitbugCrashNumDevText = "<font color='red'>{$hitbugCrashNumDevText}【异常】</font>";
        }
        $hitbugErrorNumRate = round(bcdiv($data['hitbug']['exception_error']['num'] ?? 0, $hitbugExceptionStartNum > 0 ? $hitbugExceptionStartNum : 1, 6) * 100, 2);
        $hitbugErrorNumText = "错误次数率：{$hitbugErrorNumRate}% （建议值不高于30%）";
        if ($hitbugErrorNumRate > 30) {
            $hitbugErrorNumText = "<font color='red'>{$hitbugErrorNumText}【异常】</font>";
        }
        $hitbugErrorDevNumRate = round(bcdiv($data['hitbug']['exception_error']['dev_num'] ?? 0, $hitbugExceptionStartDevNum > 0 ? $hitbugExceptionStartDevNum : 1, 6) * 100, 2);
        $hitbugErrorDevNumText = "错误设备率：{$hitbugErrorDevNumRate}% （建议值不高于50%）";
        if ($hitbugErrorDevNumRate > 50) {
            $hitbugErrorDevNumText = "<font color='red'>{$hitbugErrorDevNumText}【异常】</font>";
        }

        //判断联网设备是都为0, 如果为0，不发送消息
        if ($hitbugExceptionStartDevNum == 0 || $perfMateScoreNum == 0) {
            return;
        }

        $text = <<<TEXT
{$appName}性能巡检日报 {$osTypeText}系统
\r\n
{$date}
\r\n
【质量】
联网设备总数：{$hitbugExceptionStartDevNum}台
{$hitbugCrashNumText}
{$hitbugCrashNumDevText}
{$hitbugErrorNumText}
{$hitbugErrorDevNumText}
\r\n
【性能】
{$perfmateScore}
游戏运行次数性能不达标率：{$perfmateLowRate}%
{$perfmateMemory}
{$perfmateSmoothnes}
{$battleStutter}
TEXT;
        if ($perfmateBatteryPower) {
            $text .= <<<TEXT

{$perfmateBatteryPower}
\r\n
*发热指标下个版本迭代
TEXT;
        } else {
            $text .= <<<TEXT

\r\n
*发热指标下个版本迭代
TEXT;
        }
        //批量发送
        foreach ($config['webhook_url'] as $url) {
            $service = new WXGroupNoticeService($url, true);
            $service->wxGroupNotify($text, 'markdown');
        }
    }
}
