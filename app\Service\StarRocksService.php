<?php

/**
 * StarRocks操作方法
 * @desc StarRocks操作方法
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use Illuminate\Support\Facades\Log;

class StarRocksService
{
    //连接句柄
    private $conn;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $starRocks = config('starRocks');
        $conn = new \mysqli($starRocks['starRocks_host'], $starRocks['starRocks_username'], $starRocks['starRocks_password'], $starRocks['starRocks_database'], $starRocks['starRocks_port']);
        if ($conn->connect_error) {
            Log::error("starRocks连接失败: " . $conn->connect_error);
            die("starRocks连接失败: " . $conn->connect_error);
        }
        $this->conn = $conn;
    }

    /**
     * 查询
     * @param $sql
     * @return array
     */
    public function query($sql)
    {
        $result = $this->conn->query($sql);

        // 检查查询是否成功执行
        if ($result === false) {
            $error = $this->conn->error;
            $errno = $this->conn->errno;
            Log::error("StarRocks查询失败: [{$errno}] {$error} - SQL: {$sql}");
            throw new \Exception("StarRocks查询失败: [{$errno}] {$error}");
        }

        $res = $result->fetch_all(MYSQLI_ASSOC);
        $result->free_result();
        return $res;
    }

    /**
     * 执行
     * @param $sql
     * @return bool|\mysqli_result
     */
    public function execute($sql)
    {
        $result = $this->conn->query($sql);

        // 检查查询是否成功执行
        if ($result === false) {
            $error = $this->conn->error;
            $errno = $this->conn->errno;
            Log::error("StarRocks执行失败: [{$errno}] {$error} - SQL: {$sql}");
            throw new \Exception("StarRocks执行失败: [{$errno}] {$error}");
        }

        return $result;
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }

    /**
     * 关闭连接
     */
    public function close()
    {
        $this->conn->close();
    }
}
