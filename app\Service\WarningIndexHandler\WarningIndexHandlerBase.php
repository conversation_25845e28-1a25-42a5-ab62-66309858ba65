<?php

/**
 * 问题维度预警基类
 */

namespace App\Service\WarningIndexHandler;

use App\Components\ClickHouse\ClickHouse;
use App\Model\BaseModel;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Warning;
use Carbon\Carbon;

abstract class WarningIndexHandlerBase
{
    /**
     * 预警统计开始时间
     *
     * @var int
     */
    protected $startTime;

    /**
     * 预警统计结束时间
     *
     * @var int
     */
    protected $endTime;

    /**
     * 预警
     *
     * @var Warning
     */
    protected $warning;

    /**
     * 初始化
     */
    public function __construct(Warning $warning)
    {
        $now = Carbon::now();
        $this->startTime = (clone $now)->subHours()->startOfHour()->timestamp;
        $this->endTime = (clone $now)->startOfHour()->timestamp;
        $this->warning = $warning;
    }

    /**
     * 查询数据
     *
     * @return array
     */
    public function getData(): array
    {
        return array_column((new ClickHouse())->getData($this->getBuilder()), null, 'extra_app_id');
    }

    /**
     * 查询构造器
     */
    abstract protected function getBuilder();

    /**
     * 查询构造器基础方法
     */
    protected function getBaseBuilder()
    {
        return (new UserLogDataAll())
            ->selectRaw('count(*) as num, count(distinct server_dev_str) as dev_num, array_distinct(array_agg(upper(exception_block_id))) as blocks')
            ->where('stream_date', '>=', date('Y-m-d', $this->startTime))
            ->where('stream_date', '<=', date('Y-m-d', $this->endTime))
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_time', '<', $this->endTime)
            ->when($this->warning->app_version, function ($query, $appVersion) {
                return $query->whereIn('app_version', $appVersion);
            })
            ->when($this->warning->os_type, function ($query, $osType) {
                return $query->where('os_type', $osType);
            })
            ->where('extra_app_id', $this->warning->developer_app_id)
            ->where('server_dev_str', '!=', '')
            ->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[6]);
    }
}
