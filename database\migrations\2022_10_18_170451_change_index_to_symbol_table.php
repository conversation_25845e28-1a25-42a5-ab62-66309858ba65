<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeIndexToSymbolTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->dropUnique(['developer_app_id', 'type', 'os_type', 'app_version']);
            $table->index(['developer_app_id', 'os_type', 'app_version']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->unique(['developer_app_id', 'type', 'os_type', 'app_version']);
            $table->dropIndex(['developer_app_id', 'os_type', 'app_version']);
        });
    }
}
