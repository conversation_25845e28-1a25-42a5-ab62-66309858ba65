<?php

/**
 * 异常处理配置表控制器
 * @desc 异常处理配置表控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/03/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Model\ExceptionHandlerConfig;
use App\Model\StarRocks\ExceptionHandler;
use App\Model\StarRocks\ExceptionRecordNew;
use App\Model\StarRocks\StarRocksDB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ExceptionHandlerController extends Controller
{
    /**
     * 获取异常处理配置列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15846
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'keyword' => 'nullable|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            $query = ExceptionHandlerConfig::query();
            // 根据keyword模糊搜索
            if ($request->has('keyword')) {
                $query->where('keyword', 'like', '%' . $request->keyword . '%');
            }
            // 根据developer_app_id搜索
            $query->where('developer_app_id', $request->developer_app_id);
            // 分页
            $data = $query->orderBy('id', 'desc')->paginate($request->input('pre_page', 15));
            // 返回数据
            return $this->response(0, [
                'total' => $data->total(),
                'list' => $data->items()
            ]);
        } catch (\Exception $e) {
            Log::error('获取异常处理配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1000, [], $e->getMessage());
        }
    }

    /**
     * 创建异常处理配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15847
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'keyword' => 'required|string|max:500',
                'handler' => 'required|array',
                'handler.*.handler_id' => 'required|integer',
                'handler.*.handler' => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 创建异常处理配置
            $handler = new ExceptionHandlerConfig($request->only([
                'developer_app_id',
                'keyword',
                'handler'
            ]));

            $handler->operator = Auth::user()->alias;
            $handler->save();

            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('创建异常处理配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 更新异常处理配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15851
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer',
                'keyword' => 'required|string|max:500',
                'handler' => 'required|array',
                'handler.*.handler_id' => 'required|integer',
                'handler.*.handler' => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 更新异常处理配置
            ExceptionHandlerConfig::query()->where('id', $request->id)->update([
                'keyword' => $request->keyword,
                'handler' => $request->handler,
                'operator' => Auth::user()->alias
            ]);
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('更新异常处理配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 删除异常处理配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15850
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 删除异常处理配置
            ExceptionHandlerConfig::where('id', $request->id)->delete();
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('删除异常处理配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 统计异常处理匹配数据
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15852
     * @param Request $request
     * @return JsonResponse
     */
    public function stat(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'start_date' => 'required|date|date_format:Y-m-d',
                'end_date' => 'required|date|date_format:Y-m-d'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }

            // 计算当前周期和上一周期日期范围
            list($prevStartDate, $prevEndDate) = $this->calculatePrevPeriod($request->start_date, $request->end_date);

            // 获取表名
            $exceptionHandlerTable = ExceptionHandler::TABLE_NAME;
            $exceptionRecordTable = ExceptionRecordNew::TABLE_NAME;

            // 获取白名单异常ID的子查询
            $whiteListSql = $this->getWhiteListSubQuery();

            // 当前周期查询
            $statSubQuery = $this->getStatSubQuery($request->start_date, $request->end_date, $request->developer_app_id);
            $handler = $this->getHandlerData($statSubQuery, $whiteListSql, $request->developer_app_id, $exceptionHandlerTable, $exceptionRecordTable);

            // 上一周期查询
            $prevStatSubQuery = $this->getStatSubQuery($prevStartDate, $prevEndDate, $request->developer_app_id);
            $prevHandler = $this->getHandlerData($prevStatSubQuery, $whiteListSql, $request->developer_app_id, $exceptionHandlerTable, $exceptionRecordTable);

            // 处理环比数据
            $result = $this->calculateChainRatio($handler, $prevHandler);

            // 返回数据
            return $this->response(0, $result);
        } catch (\Exception $e) {
            Log::error('获取异常处理配置统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 统计异常处理匹配小时数据
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15852
     * @param Request $request
     * @return JsonResponse
     */
    public function hourStat(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'start_date' => 'required|date|date_format:Y-m-d H:i:s',
                'end_date' => 'required|date|date_format:Y-m-d H:i:s'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }

            $handler = ExceptionHandler::query()
                ->selectRaw('exception_handler.handler, exception_record_new.handler_keyword_id, count(distinct exception_record_new.record_id) as total')
                ->join('exception_record_new', 'exception_handler.record_id', '=', 'exception_record_new.record_id')
                ->where('exception_handler.developer_app_id', $request->developer_app_id)
                ->where('exception_record_new.type', 2)
                ->where('exception_handler.handler_id', '!=', 0)
                ->where('exception_record_new.is_add_white_list', 0)
                ->where('exception_record_new.handler_keyword_id',  '!=', 0)
                ->whereBetween('exception_record_new.created_at', [$request->start_date, $request->end_date])
                ->groupBy('exception_handler.handler', 'exception_record_new.handler_keyword_id')
                ->getFromSR();

            // 关键词ID转换为关键词
            $newHandler = [];
            if (!empty($handler)) {
                $keywordMap = ExceptionHandlerConfig::query()
                    ->whereIn('id', array_column($handler, 'handler_keyword_id'))
                    ->where('developer_app_id', $request->developer_app_id)
                    ->pluck('keyword', 'id');
                foreach ($handler as $item) {
                    if (isset($keywordMap[$item['handler_keyword_id']])) {
                        $item['keyword'] = $keywordMap[$item['handler_keyword_id']];
                    } else {
                        continue;
                    }
                    $newHandler[] = $item;
                }
            }

            // 返回数据
            return $this->response(0, $newHandler);
        } catch (\Exception $e) {
            Log::error('获取异常处理配置统计小时接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 计算上一个周期的日期范围
     * 
     * @param string $startDate 当前周期开始日期
     * @param string $endDate 当前周期结束日期
     * @return array 包含上一周期开始和结束日期的数组
     */
    private function calculatePrevPeriod($startDate, $endDate)
    {
        $currentStartDate = new \DateTime($startDate);
        $currentEndDate = new \DateTime($endDate);
        $interval = $currentStartDate->diff($currentEndDate);
        $daysDiff = $interval->days + 1; // 包含开始和结束日期

        $prevEndDate = (clone $currentStartDate)->modify('-1 day');
        $prevStartDate = (clone $prevEndDate)->modify('-' . ($daysDiff - 1) . ' days');

        return [$prevStartDate->format('Y-m-d'), $prevEndDate->format('Y-m-d')];
    }

    /**
     * 获取统计子查询
     * 
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param int $developerAppId 应用ID
     * @return string SQL查询语句
     */
    private function getStatSubQuery($startDate, $endDate, $developerAppId)
    {
        return StarRocksDB::toSql(
            DB::table('exception_stat_all_view')
                ->selectRaw('exception_block_id, sum(count) as total')
                ->whereBetween("stream_date", [$startDate, $endDate])
                ->where('extra_app_id', $developerAppId)
                ->groupBy('exception_block_id')
        );
    }

    /**
     * 获取白名单异常ID的子查询
     * 
     * @return string SQL查询语句
     */
    private function getWhiteListSubQuery()
    {
        return StarRocksDB::toSql(
            DB::table('exception_record_new')
                ->select('exception_block_id')
                ->where('type', 2)
                ->where('is_add_white_list', 1)
        );
    }

    /**
     * 获取处理器数据
     * 
     * @param string $statSubQuery 统计子查询
     * @param string $whiteListSql 白名单子查询
     * @param int $developerAppId 应用ID
     * @param string $exceptionHandlerTable 异常处理表名
     * @param string $exceptionRecordTable 异常记录表名
     * @return array 处理器数据
     */
    private function getHandlerData($statSubQuery, $whiteListSql, $developerAppId, $exceptionHandlerTable, $exceptionRecordTable)
    {
        return ExceptionHandler::query()
            ->selectRaw("{$exceptionHandlerTable}.handler_id, any_value({$exceptionHandlerTable}.handler) as handler, count({$exceptionRecordTable}.exception_block_id) as num, sum(t.total) as exception_num, count(case when {$exceptionRecordTable}.status = 1 then 1 end) as pending, sum(case when {$exceptionRecordTable}.status = 1 then t.total else 0 end) as exception_pending, count(case when {$exceptionRecordTable}.status = 2 then 1 end) as processing, sum(case when {$exceptionRecordTable}.status = 2 then t.total else 0 end) as exception_processing, count(case when {$exceptionRecordTable}.status = 3 then 1 end) as processed, sum(case when {$exceptionRecordTable}.status = 3 then t.total else 0 end) as exception_processed")
            ->join($exceptionRecordTable, "{$exceptionRecordTable}.record_id", '=', "{$exceptionHandlerTable}.record_id")
            ->joinSub($statSubQuery, 't', function ($join) use ($exceptionHandlerTable) {
                $join->on('t.exception_block_id', '=', "{$exceptionHandlerTable}.exception_block_id");
            })
            ->whereRaw("upper({$exceptionHandlerTable}.exception_block_id) not in (select upper(exception_block_id) from ({$whiteListSql}) as white_list)")
            ->where("{$exceptionHandlerTable}.developer_app_id", $developerAppId)
            ->where("{$exceptionHandlerTable}.type", 2)
            ->where("{$exceptionHandlerTable}.handler_id", '!=', 0)
            ->groupBy("{$exceptionHandlerTable}.handler_id")
            ->orderBy("num", 'desc')
            ->getFromSR();
    }

    /**
     * 计算环比数据
     * 
     * @param array $currentData 当前周期数据
     * @param array $prevData 上一周期数据
     * @return array 包含环比数据的结果
     */
    private function calculateChainRatio($currentData, $prevData)
    {
        // 将上一周期数据转为以handler_id为键的映射
        $prevHandlerMap = [];
        foreach ($prevData as $item) {
            $prevHandlerMap[$item['handler_id']] = $item;
        }

        $result = [];
        foreach ($currentData as $current) {
            $handlerId = $current['handler_id'];
            $chainData = [
                'num_ratio' => '0',
                'exception_num_ratio' => '0',
                'pending_ratio' => '0',
                'exception_pending_ratio' => '0',
                'processing_ratio' => '0',
                'exception_processing_ratio' => '0',
                'processed_ratio' => '0',
                'exception_processed_ratio' => '0',
            ];

            // 计算环比数据
            if (isset($prevHandlerMap[$handlerId])) {
                $prev = $prevHandlerMap[$handlerId];
                $chainData['num_ratio'] = $this->calculateRatio($current['num'], $prev['num']);
                $chainData['exception_num_ratio'] = $this->calculateRatio($current['exception_num'], $prev['exception_num']);
                $chainData['pending_ratio'] = $this->calculateRatio($current['pending'], $prev['pending']);
                $chainData['exception_pending_ratio'] = $this->calculateRatio($current['exception_pending'], $prev['exception_pending']);
                $chainData['processing_ratio'] = $this->calculateRatio($current['processing'], $prev['processing']);
                $chainData['exception_processing_ratio'] = $this->calculateRatio($current['exception_processing'], $prev['exception_processing']);
                $chainData['processed_ratio'] = $this->calculateRatio($current['processed'], $prev['processed']);
                $chainData['exception_processed_ratio'] = $this->calculateRatio($current['exception_processed'], $prev['exception_processed']);
            }

            $current['chain_ratio'] = $chainData;
            $result[] = $current;
        }

        return $result;
    }

    /**
     * 计算环比比率
     * 
     * @param float $current 当前值
     * @param float $prev 上期值
     * @return string 环比率，百分比形式
     */
    private function calculateRatio($current, $prev)
    {
        if ($prev == 0) {
            return $current > 0 ? '100' : '0';
        }

        return bcadd(round(($current - $prev) / $prev * 100, 2), 0, 2);
    }
}
