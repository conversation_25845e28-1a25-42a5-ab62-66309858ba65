<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecordNewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('record_new', function (Blueprint $table) {
            $table->bigIncrements('record_id');
            $table->string('exception_unique_id', 32)->default('')->comment('异常类型唯一id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedTinyInteger('type')->default(0)->comment('异常类型 1为崩溃异常');
            $table->unsignedTinyInteger('category')->default(0)->comment('具体异常类型');
            $table->string('name', 512)->comment('异常说明');
            $table->text('explain')->nullable()->comment('异常描述');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台类型;1为安卓,2为iOS');
            $table->unsignedTinyInteger('status')->default(1)->comment('处理状态, 1未处理，2处理中，3已处理');
            $table->unsignedInteger('handler_id')->default(0)->comment('处理人id');
            $table->string('handler', 64)->nullable()->comment('处理人');
            $table->timestamps();
            $table->index(['developer_app_id', 'type', 'os_type']);
            $table->index(['developer_app_id', 'type', 'name', 'category']);
            $table->index('created_at');
            $table->index('exception_unique_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `record_new` comment '新异常记录表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('record');
    }
}
