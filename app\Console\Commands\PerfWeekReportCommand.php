<?php

/**
 * 周报巡检脚本
 * @desc 周报巡检脚本
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/12/06
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Model\AppModel;
use App\Model\InspectionConfig;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PerfWeekReportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perf:week:report {ids? : 配置ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '周报巡检脚本';

    /**
     * 执行逻辑
     *
     * @return void
     */
    public function handle(): void
    {
        //判断是否忽略
        $ids = $this->argument('ids');
        // 获取数据
        $list = InspectionConfig::query()
            ->where('status', 1)
            ->where('category', 2)
            ->when($ids, function ($query, $ids) {
                return $query->whereIn('id', explode(',', $ids));
            })
            ->orderBy('created_at')
            ->get();
        //获取APP，把id作为key
        $apps = AppModel::query()->get()->keyBy('id')->toArray();
        //当前时间
        $currentDate = date('H') . ':00';
        //获取当前是周几
        $week = Carbon::now()->dayOfWeek;
        //判断$week等于0，则$week=7
        if ($week == 0) {
            $week = 7;
        }
        //循环处理
        foreach ($list as $config) {
            //判断$config['data']是否等于$week
            if (!$ids && ($config['day'] + 1) != $week) {
                continue;
            }
            // 判断触发时间，如果不等于则跳过
            if (!$ids && $currentDate != $config['trigger_time']) {
                continue;
            }
            //获取app名称和icon
            $appName = $apps[$config['developer_app_id']]['app_name'];
            $appIcon = $apps[$config['developer_app_id']]['app_icon'];
            try {
                $client = new Client([
                    'timeout' => 3600,
                    'connect_timeout' => 3600,
                ]);
                $response = $client->request('POST', 'https://fe-tool-api.shiyue.com/screenshot/getWebScreenShot', [
                    'json' => [
                        'deviceScaleFactor' => 2,
                        'height' => null,
                        'web_url' => "https://manage.shiyue.com/screenshot?os_type={$config['os_type']}&developer_app_id={$config['developer_app_id']}&developer_app_name={$appName}&developer_app_icon={$appIcon}",
                        'width' => 800,
                    ],
                ]);
                $result = $response->getBody()->getContents();
                $code = $response->getStatusCode(); // 200
                if ($code == 201 || $code == 200) {
                    $data = json_decode($result, true);
                    $this->sendImg($data, $config['webhook_url']);
                } else {
                    Log::error("请求失败：" . $result);
                }
            } catch (\Throwable $e) {
                Log::error("请求失败：{$e->getMessage()}, {$e->getTraceAsString()}");
            }
        }
    }

    /**
     * 发送图片
     *
     * @param $data
     * @param $urls
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function sendImg($data, $urls)
    {
        foreach ($urls as $url) {
            HttpAgent::getInstance()->request('POST', $url, [
                'json' => [
                    "msgtype" => "image",
                    "image" => [
                        "base64" => $data['data']['image_data'],
                        "md5" => $data['data']['image_md5'],
                    ]
                ]
            ]);
        }
    }
}
