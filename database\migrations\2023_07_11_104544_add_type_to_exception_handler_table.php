<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeToExceptionHandlerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('exception_handler', function (Blueprint $table) {
            $table->unsignedTinyInteger('type')->default(0)->comment('异常类型 1为崩溃异常');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('exception_handler', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
}
