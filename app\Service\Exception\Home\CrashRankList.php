<?php

/**
 * 首页崩溃排行服务类
 * @desc 首页崩溃排行服务类
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2023/10/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Home;

use App\Model\BaseModel;
use App\Model\ClickHouse\UserLogDataAll;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CrashRankList extends Base
{
    protected $rate = true;

    protected $isEliminate = true;

    /**
     * 获取列表
     *
     * @return array
     */
    public function list(): array
    {
        return [
            'type' => 'crash',
            'list' => $this->pullExceptionNumList(self::DATE_PERIOD_KEY_CURRENT, ['exception_crash'], 10),
        ];
    }

    /**
     * 获取SQL的Builder
     *
     * @param array $params
     * @return mixed
     */
    protected function getBuilder(array $params)
    {
        //过滤白名单
        $appVersionWhite = $this->getAppVersionWhite();
        $exceptionBlockIdWhite = $this->getExceptionBlockIdWhite(1);
        $builder = (new UserLogDataAll())
            ->selectRaw('`extra_app_id` AS `app_id`, COUNT(*) AS `num`')
            ->whereNotIn('extra_app_id', [0, 76])
            ->whereIn('event_name', $params['event_name'])
            ->whereBetween('stream_date', $params['stream_date'])
            ->when($params['os_type'], function (Builder $query, $osType) {
                return $query->where('os_type', $osType);
            })
            ->when($exceptionBlockIdWhite, function (Builder $query, $exceptionBlockIdWhite) { //排除白名单异常
                return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map('strtoupper', $exceptionBlockIdWhite));
            })
            ->whereNotIn('extra_app_id', [3, 22, 30, 36, 35, 37, 46, 51, 52, 64, 68, 69, 70, 71, 72, 73, 75])
            ->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[6])
            ->whereRaw("upper(exception_block_id) not in (select upper(a.exception_block_id) as exception_block_id from (select extra_app_id, exception_block_id, unnest from exception_stream_keyword, unnest(keywords) AS unnest where array_length(keywords) > 0 and event_name = 'exception_crash' and stream_date >= '{$params['stream_date'][0]}') as a inner join (select developer_app_id, keyword from exception_filter_keyword where is_stat = 0) as b on a.extra_app_id = b.developer_app_id and a.unnest = b.keyword group by a.exception_block_id)")
            ->groupBy('extra_app_id');
        //排除白名单版本
        foreach ($appVersionWhite as $key => $item) {
            $builder->where(function (Builder $query) use ($key, $item) {
                $sdkPackageName = BaseModel::FILTER_PACKAGE_NAME[$key] ?? [];
                return $query->whereNotIn('app_version', $item)
                    ->when($sdkPackageName, function ($query) use ($sdkPackageName) {
                        // 屏蔽包名
                        return $query->whereNotIn('sdk_package_name', $sdkPackageName);
                    })->orWhere("extra_app_id", '!=', $key);
            });
        }
        return $builder;
    }

    /**
     * 获取Rate的SQLBuilder
     *
     * @param array $params
     * @return mixed
     */
    protected function getRateBuilder(array $params)
    {
        // $appVersionWhite = $this->getAppVersionWhite();
        $builder = (new UserLogDataAll())
            ->selectRaw('`extra_app_id` AS app_id, COUNT(*) AS num, COUNT(distinct server_dev_str) AS device_num')
            ->whereNotIn('extra_app_id', [0, 76])
            ->where('event_name', 'exception_start')
            ->when($params['os_type'], function (Builder $query, $osType) {
                return $query->where('os_type', $osType);
            })
            ->whereNotIn('extra_app_id', [3, 22, 30, 36, 35, 37, 46, 51, 52, 64, 68, 69, 70, 71, 72, 73, 75])
            ->whereBetween('stream_date', $params['stream_date'])
            ->groupBy('extra_app_id');
        return $builder;
    }
}
