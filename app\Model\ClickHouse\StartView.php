<?php

/**
 * 统计视图
 * @desc 统计视图
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/06/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\ClickHouse;

class StartView extends UserLogDataAll
{
    protected $table = 'exception_stat_all_view';

    /**
     * 查询构建
     * @return UserLogDataAll
     */
    public static function query()
    {
        return (new static());
    }
}
