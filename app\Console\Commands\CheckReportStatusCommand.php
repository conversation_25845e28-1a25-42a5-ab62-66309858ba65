<?php

/**
 * 检测报告状态脚本
 * @desc 检测报告状态脚本
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/29
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\Record;
use App\Service\ExceptionHandleService;
use App\Service\StarRocksService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckReportStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:report:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检测报告状态脚本';

    /**
     * 处理
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        $sql = <<<SQL
WITH today_data AS (
    SELECT version, upper(exception_block_id) as exception_block_id
    FROM exception_stat_version_v3
    WHERE stream_date = CURRENT_DATE()
      AND upper(exception_block_id) IN
          (select upper(exception_block_id) from exception_record_new where status = 3)),
     historical_data AS (
         SELECT version, upper(exception_block_id) as exception_block_id
         FROM exception_stat_version_v3
         WHERE stream_date < CURRENT_DATE()
           AND upper(exception_block_id) IN (select upper(exception_block_id) from exception_record_new where status = 3))
SELECT t.exception_block_id, t.version
FROM today_data t
INNER JOIN historical_data h ON t.version = h.version AND t.exception_block_id = h.exception_block_id
WHERE h.version IS NULL
GROUP BY t.exception_block_id, t.version
ORDER BY t.exception_block_id, t.version
SQL;

        $result = (new StarRocksService())->query($sql);

        // 判断是否有数据
        foreach ($result as $item) {
            // 打印日志
            Log::info("报告状态更改，报告ID：{$item['exception_block_id']},报告版本：{$item['version']}");
            // 获取当前报告记录
            $record = Record::query()
                ->where(DB::raw("upper(exception_block_id)"), strtoupper($item['exception_block_id']))
                ->first();
            // 修改报告状态
            (new ExceptionHandleService([
                'comment' => "{$item['version']}版本重复出现",
                'developer_app_id' => $record['developer_app_id'],
                'exception_block_id' => $item['exception_block_id'],
                'handlers' => [
                    [
                        'handler_id' => $record['handler_id'],
                        'handler_name' => $record['handler']
                    ]
                ],
                'is_status_change' => 1,
                'record_id' => $record['record_id'],
                'status' => 4,
                'type' => $record['type'],
            ]))->updateExceptionState();
        }
    }
}
