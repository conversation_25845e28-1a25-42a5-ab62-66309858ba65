<?php

/**
 * tapd自动提单脚本
 * @desc tapd自动提单脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/11/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Jobs\CreateTapdBugJob;
use App\Model\GlobalConfig;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\TapdAutoConfig;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class TapdAutoBugCommand extends Command
{
    /**
     * 筛选字段
     *
     * @var array
     */
    const FILTER_COLUMNS = ['explain_desc', 'subject_name'];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tapd:auto:bug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tapd自动提单脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        // 打印日志
        Log::info('tapd自动提单脚本开始执行');
        // 获取tapd自动提单列表
        $list = TapdAutoConfig::query()
            ->where('status', 1)
            ->get();
        // 获取当前时间的小时
        $currentDate = date('H') . ':00';
        // 判断提单时间是否符合
        foreach ($list as $item) {
            // 判断时间是否相等
            if ($item['trigger_time'] == $currentDate) {
                // 打印日志
                Log::info('tapd自动提单脚本命中数据：' . json_encode($item));
                // 获取配置信息
                $config = GlobalConfig::query()
                    ->where('developer_app_id', $item['developer_app_id'])
                    ->value('config') ?? '{}';
                $config = json_decode($config, true);
                // 获取当前时间的时间戳
                $endTime = time();
                $startTime = $endTime - (24 * 60 * 60);
                // 获取数量
                $num = $this->getNum($item['developer_app_id'], $startTime, $endTime);
                $whiteNum = $this->getWhiteNum($item['developer_app_id'], $startTime, $endTime, $item['white_filter']);
                // 缓存的key
                $key = date('Ymd') . '_tapd_auto_bug_' . $item['developer_app_id'];
                // 记录数据到缓存中
                Redis::hMSet($key, [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'num' => $num,
                    'white_num' => $whiteNum,
                    'config' => json_encode($config),
                    'tapd_auto_config' => json_encode($item),
                ]);
                // 设置过期时间
                Redis::expire($key, 24 * 60 * 60);
                // 获取数据
                $this->pushData($item, $key);
            }
        }
        // 打印日志
        Log::info('tapd自动提单脚本结束执行');
    }

    /**
     * 推送数据
     *
     * @param $config
     * @param $key
     */
    private function pushData($config, $key)
    {
        $redis = Redis::connection();
        // 获取缓存内容
        $cacheData = $redis->hGetAll($key);
        // 获取数据
        $builder = ExceptionStreamAll::query()
            ->where('exception_stream_all.extra_app_id', $config['developer_app_id'])
            ->where('exception_stream_all.stream_date', '>=', date('Y-m-d', $cacheData['start_time']))
            ->where('exception_stream_all.stream_date', '<=', date('Y-m-d', $cacheData['end_time']))
            ->where('exception_stream_all.stream_time', '>=', $cacheData['start_time'])
            ->where('exception_stream_all.stream_time', '<=', $cacheData['end_time'])
            ->whereIn('exception_stream_all.event_name', ['exception_crash', 'exception_error'])
            ->when($config['server_id'], function ($query, $serverIds) {
                foreach ($serverIds as $v) {
                    $query->orWhereRaw("`extra` LIKE ?", ['%{"value":"' . $v . '","key":"serverName","name":"服务器ID"}%']);
                }
                return $query;
            })
            ->when($config['inner_version'], function ($query, $innerVersion) {
                return $query->whereIn('inner_version', $innerVersion);
            })
            ->when($config['package_name'], function ($query, $packageName) {
                return $query->whereIn('sdk_package_name', $packageName);
            })
            ->when($config['keywords'], function ($query, $keywords) {
                return $query->where(function ($query) use ($keywords) {
                    $values = $this->getKeywordList($keywords);
                    foreach (self::FILTER_COLUMNS as $column) {
                        $keys1 = array_keys($values['1']);
                        foreach ($keys1 as $v1) {
                            $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like? ", '%' . $v1 . '%');
                        }
                        $keys2 = array_keys($values['2']);
                        foreach ($keys2 as $v2) {
                            $query->orWhereRaw("replace(lower({$column}), '%0a', '%20') like? ", '%' . $v2 . '%');
                        }
                    }
                    return $query;
                });
            })
            ->when($config['white_filter'], function ($query, $whiteList) {
                return $query->where(function ($query) use ($whiteList) {
                    $values = $this->getKeywordList($whiteList);
                    foreach (self::FILTER_COLUMNS as $column) {
                        $keys1 = array_keys($values['1']);
                        foreach ($keys1 as $v1) {
                            $query->whereRaw("replace(lower({$column}), '%0a', '+') not like? ", '%' . $v1 . '%');
                        }
                        $keys2 = array_keys($values['2']);
                        foreach ($keys2 as $v2) {
                            $query->whereRaw("replace(lower({$column}), '%0a', '%20') not like? ", '%' . $v2 . '%');
                        }
                    }
                    return $query;
                });
            })
            ->whereRaw("exception_stream_all.exception_block_id not in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$config['developer_app_id']} and bind_status = 1)");
        // 获取条数
        $total = (clone $builder)
            ->selectRaw('count(distinct exception_stream_all.exception_block_id) as num')
            ->firstFromSR();
        // 获取数据
        $result = (clone $builder)
            ->selectRaw('exception_stream_all.event_name, exception_stream_all.exception_block_id, any_value(subject_name) as subject_name, any_value(explain_desc) as explain_desc, any_value(origin_stacks_json) as origin_stacks_json, max(stream_time) as stream_time, count(1) as num, min(min_stream_time) as min_stream_time, array_distinct(array_agg(sdk_package_name)) as sdk_package_name, array_distinct(array_agg(inner_version)) as inner_version')
            ->join('exception_stat_all_v2', function ($join) {
                return $join->on("exception_stream_all.exception_block_id", "=", "exception_stat_all_v2.exception_block_id")
                    ->on("exception_stream_all.extra_app_id", "=", "exception_stat_all_v2.extra_app_id");
            })
            ->groupByRaw('exception_stream_all.event_name, exception_stream_all.exception_block_id')
            ->orderByRaw('exception_stream_all.event_name, num desc')
            ->limit($config['max_num'])
            ->getFromSR();
        // 判断totalNum是否等于$config['max_num']
        $totalNum = count($result);
        if ($totalNum == $config['max_num']) {
            $redis->hSet($key, 'hit_num', $total['num'] ?? 0);
        } else {
            $redis->hSet($key, 'hit_num', $totalNum);
        }
        // 遍历数据
        foreach ($result as $value) {
            CreateTapdBugJob::dispatch($value, $key)->onQueue('create_tapd_bug_queue');
        }
    }

    /**
     * 获取白名单异常数
     *
     * @param $extraAppId
     * @param $startTime
     * @param $endTime
     * @param $whiteList
     * @return int
     */
    private function getWhiteNum($extraAppId, $startTime, $endTime, $whiteList): int
    {
        // 判断 whiteList 是否为空
        if (empty($whiteList)) {
            return 0;
        }
        $result = ExceptionStreamAll::query()
            ->selectRaw('count(distinct exception_block_id) as num')
            ->where('extra_app_id', $extraAppId)
            ->where('stream_date', '>=', Carbon::parse($startTime)->toDateString())
            ->where('stream_date', '<=', Carbon::parse($endTime)->toDateString())
            ->where('stream_time', '>=', $startTime)
            ->where('stream_time', '<=', $endTime)
            ->whereIn('event_name', ['exception_crash', 'exception_error'])
            ->when($whiteList, function ($query, $whiteList) {
                return $query->where(function ($query) use ($whiteList) {
                    $values = $this->getKeywordList($whiteList);
                    foreach (self::FILTER_COLUMNS as $column) {
                        $keys1 = array_keys($values['1']);
                        foreach ($keys1 as $v1) {
                            $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like? ", '%' . $v1 . '%');
                        }
                        $keys2 = array_keys($values['2']);
                        foreach ($keys2 as $v2) {
                            $query->orWhereRaw("replace(lower({$column}), '%0a', '%20') like? ", '%' . $v2 . '%');
                        }
                    }
                    return $query;
                });
            })
            ->firstFromSR();
        return $result['num'] ?? 0;
    }

    /**
     * 获取整理好的关键词列表
     *
     * @param $keywords
     * @return array
     */
    private function getKeywordList($keywords): array
    {
        //整理过滤的关键词
        $values = [];
        foreach ($keywords as $value) {
            $urlEncodeValue = mb_strtolower(urlencode($value));
            $rawUrlEncodeValue = mb_strtolower(rawurlencode($value));
            $values['1'][mb_strtolower($value)] = $value;
            $values['1'][$urlEncodeValue] = $value;
            $values['1'][$rawUrlEncodeValue] = $value;
            $values['2'][str_replace(["%27", "%24"], ["\'", "$"], $urlEncodeValue)] = $value;
            $values['2'][str_replace(["%27", "%24"], ["\'", "$"], $rawUrlEncodeValue)] = $value;
        }
        return $values;
    }

    /**
     * 获取异常数
     *
     * @param $extraAppId
     * @param $startTime
     * @param $endTime
     * @return int
     */
    private function getNum($extraAppId, $startTime, $endTime): int
    {
        $result = ExceptionStreamAll::query()
            ->selectRaw('count(distinct exception_block_id) as num')
            ->where('extra_app_id', $extraAppId)
            ->where('stream_date', '>=', Carbon::parse($startTime)->toDateString())
            ->where('stream_date', '<=', Carbon::parse($endTime)->toDateString())
            ->where('stream_time', '>=', $startTime)
            ->where('stream_time', '<=', $endTime)
            ->whereIn('event_name', ['exception_crash', 'exception_error'])
            ->firstFromSR();
        return $result['num'] ?? 0;
    }
}
