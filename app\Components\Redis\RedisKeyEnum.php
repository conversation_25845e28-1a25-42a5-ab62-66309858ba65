<?php

declare (strict_types = 1);

namespace App\Components\Redis;

/**
 * Class RedisKeyEnum
 * @package Baichuan\Library\Constant
 * author : z<PERSON><PERSON><PERSON><PERSON>@gmail.com
 * memo : 緩存鍵名枚舉
 */
class RedisKeyEnum
{

    public const STRING = [
        'STRING:Example:' => 'STRING:Example:',
        'STRING:MutexName:' => 'STRING:MutexName:',
        'STRING:MutexResult:' => 'STRING:MutexResult:',
        'STRING:MatchRecordAbstract:' => 'STRING:MatchRecordAbstract:',
        'STRING:ExceptionResult:' => 'STRING:ExceptionResult:', //異常列表--查詢結構（含：1總數，2列表）
        'STRING:AppList:' => 'STRING:AppList:', //首頁數據--APP列表
        'STRING:PullPerformanceRankList:' => 'STRING:PullPerformanceRankList:', //首頁數據--性能排行列表
        'STRING:PullExceptionNumList:' => 'STRING:PullExceptionNumList:', //首頁數據--異常次數
        'STRING:PullActiveDeviceNumList:' => 'STRING:PullActiveDeviceNumList:', //首頁數據--聯網設備數量
        'STRING:HitbugChart:' => 'STRING:HitbugChart:', //首頁數據--圖標（hitbug）
        'STRING:ExceptionList:' => 'STRING:ExceptionList:', //異常列表--記錄
        'STRING:ExceptionSearch:' => 'STRING:ExceptionSearch:', //異常列表--篩選
        'STRING:SummarySlaveMutex:' => 'STRING:SummarySlaveMutex:', //異常趨勢--統計（互斥鎖）
        'STRING:GetClickHouseData:' => 'STRING:GetClickHouseData:', //clickhouse返回結果
        'STRING:IdemExecute:' => 'STRING:IdemExecute:', //通用冪等緩存前綴
        'STRING:ExceptionSummary:' => 'STRING:ExceptionSummary:', //異常趨勢--統計
        'STRING:PullExceptionDistribution:' => 'STRING:PullExceptionDistribution:', //異常趨勢--異常分佈
        'STRING:PullAppVersionList:' => 'STRING:PullAppVersionList:', //異常詳情--版本信息
    ];

    public const HASH = [
        //'HASH:Unlock:' => 'HASH:Unlock:',//
    ];

    public const LIST = [ // 「LIST」conflicts with the system keyword
    ];

    public const SET = [
    ];

    public const SORTED_SET = [
    ];

    /**
     * 异常列表数据导出的key
     *
     * @return string
     */
    public static function getExceptionExportDataKey($taskId)
    {
        return sprintf('exception:export:data:%s', $taskId);
    }
}
