<?php

/**
 * 外挂数据处理脚本
 * @desc 外挂数据处理脚本
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/04
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionStreamAll;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExternalDataProcessorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'external:data:processor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '外挂数据处理脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            // 获取当前时间的前10分钟
            $tenMinutesAgo = Carbon::now()->subMinutes(10);
            // 获取外挂数据
            $query = ExceptionStreamAll::query()
                ->select(['extra_app_id', 'exception_merge_id', 'stream_date', 'stream_time', 'event_time', 'dev_create_time', 'server_dev_str', 'os_type', 'version', 'os_version', 'app_version', 'is_success', 'operate_status', 'release_store', 'manufacturer', 'device_model', 'account_id', 'role_id', 'role_name', 'ip', 'sdk_package_name', 'extra', 'explain_desc', 'basic_info_json', 'is_emulator', 'inner_version', 'exception_image'])
                ->where('stream_date', '>=', $tenMinutesAgo->toDateString())
                ->where('stream_time', '>=', $tenMinutesAgo->timestamp)
                ->where('extra_app_id', 13);
            $query->where(function ($query) {
                $keywords = ['该玩家疑似开挂', '该玩家开挂修改伤害', '该玩家开挂加速'];
                foreach ($keywords as $keyword) {
                    $this->explainWhere($query, $keyword);
                }
            });
            $list = $query->getFromSR();
            // 循环处理数据
            foreach ($list as $item) {
                // 处理数据
                $item['extra'] = json_decode($item['extra'], true);
                $item['basic_info_json'] = json_decode($item['basic_info_json'], true);
                $item['explain_desc'] = urldecode($item['explain_desc']);
                dd(json_encode($item, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            Log::error("执行外挂数据处理脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    public function writeExternalInitData()
    {
        {
"android_id": "d4087d607c2b8a2b",
"app_name": "永远的蔚蓝星球",
"app_package_name": "com.yydwlxqcy13.cs",
"app_process_name": "",
"app_sign_info": "讯飞输入法黑鲨版 搜狗输入法",
"app_version": "1.1.0",
"bluetooth_address": "",
"cpu_abis": "arm64-v8a",
"cpu_cores": "8",
"cpu_cur": "1785600",
"cpu_framework": "arm64-v8a,armeabi-v7a,armeabi",
"cpu_max": "1785600",
"cpu_min": "307200",
"cpu_name": "0",
"device_board": "katyusha",
"device_brand": "blackshark",
"device_display": "KTUS2208110CN00MP5 test-keys",
"device_hardware": "qcom",
"device_id": "KTUS2208110CN00MP5",
"device_manufacturer": "blackshark",
"device_model": "SHARK KTUS-A0",
"device_name": "katyusha",
"device_product": "KTUS-A0",
"device_security_patch": "2022-06-01",
"device_serial_id": "unknown",
"device_tags": "release-keys",
"device_time": "1660148763000",
"device_type": "user",
"dns": "",
"extra_app_id": "13",
"ip": "**************",
"is_accessibility": "0",
"is_airplane_mode": "0",
"is_cloudPhone": "0",
"is_emulator": "0",
"is_mock_location": "0",
"is_root": "0",
"is_tablet": "0",
"is_use_debug": "0",
"is_using_vpn": "0",
"keyboard": "0",
"language": "zh",
"last_boot_time": "1748273887897",
"locale_display_language": "中文",
"locale_iso_3_country": "CHN",
"locale_iso_3_language": "zho",
"mac": "",
"mcc": "460",
"mnc": "00",
"network_operator": "46000",
"network_operator_name": "中国移动",
"network_type": "NETWORK_WIFI",
"os_type": "1",
"os_version": "12",
"phone_type": "PHONE_TYPE_GSM",
"resolution": "1080*2400",
"ringer_mode": "2",
"rom_info": "V11.0.4.0.JOYUI",
"screen_density": "2.8812501",
"screen_density_dpi": "461",
"screen_physical_size": "6.659950764088215",
"sdk_ver": "1.2.0",
"server_dev_str": "d4087d607c2b8a2b",
"session_id": "0437cb05b177cffd6e1bbc84754ff0c6",
"slot_count": "2",
"stream_date": "2025-06-04 14:08:41",
"time_zone_id": "GMT+08:00",
"ui_mode_type": "UI_MODE_TYPE_NORMAL",
"vpn_address": ""
}
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        $columns = ['explain_desc'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }
}
