<?php

namespace App\Service;

use App\Model\Record;
use App\Model\StarRocks\ExceptionHandler;
use App\Model\StarRocks\ExceptionProcess;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ExceptionHandleService
{

    private $params;

    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 更新异常记录，实现状态变更
     */
    public function updateExceptionState()
    {
        // 判断操作类型，1是状态变更、0是添加评论
        if ($this->params['is_status_change'] == ExceptionProcess::STATE_CHANGE) {
            $exception = Record::query()
                ->where('developer_app_id', $this->params['developer_app_id'])
                ->where('record_id', $this->params['record_id'])
                ->where(DB::raw('upper(exception_block_id)'), strtoupper($this->params['exception_block_id']))
                ->first();
            $exception->status = $this->params['status']; // 更新异常记录状态
            $exception->handler = collect($this->params['handlers'])->pluck('handler_name')->implode(',');
            $exception->save();

            // 异常处理人表添加异常处理人
            $this->addExceptionHandler();
        }
        // 添加异常处理过程记录
        $this->addExceptionProcess();
    }

    /**
     * 添加异常处理过程记录
     */
    public function addExceptionProcess()
    {
        $data = [];
        if (\Auth::user()) {
            $data['operator_id'] = \Auth::id();
            $data['operator'] = \Auth::user()->alias;
        } else {
            $data['operator_id'] = 0;
            $data['operator'] = '';
        }
        $data['id'] = Str::uuid()->toString();
        $data['record_id'] = $this->params['record_id'];
        $data['exception_block_id'] = $this->params['exception_block_id'];
        $data['type'] = $this->params['type'];
        $data['developer_app_id'] = $this->params['developer_app_id'];
        $data['comment'] = isset($this->params['comment']) ? $this->params['comment'] : '';
        if ($this->params['is_status_change'] == ExceptionProcess::STATE_CHANGE) {
            // 添加异常处理记录——状态变更
            $data['handler_id'] = collect($this->params['handlers'])->pluck('handler_id')->implode(',');
            $data['handler'] = collect($this->params['handlers'])->pluck('handler_name')->implode(',');
            $data['status'] = $this->params['status'];
            $data['is_status_change'] = ExceptionProcess::STATE_CHANGE;
            ExceptionProcess::query()->createFromSR($data);
        } else if ($this->params['is_status_change'] == ExceptionProcess::ADD_COMMENT) {
            // 添加异常处理记录——添加评论
            $data['status'] = 0; // 添加评论无需记录处理状态，置位0
            $data['is_status_change'] = ExceptionProcess::ADD_COMMENT;
            ExceptionProcess::query()->createFromSR($data);
        }
    }

    /**
     * 获取最新异常记录处理状态
     * @return \App\Model\BaseModel|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getExceptionState()
    {
        $result = ExceptionProcess::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where(DB::raw('upper(exception_block_id)'), strtoupper($this->params['exception_block_id']))
            ->where('is_status_change', 1)
            ->orderBy('created_at', 'desc')
            ->limit(1)
            ->firstFromSR();
        if ($result) {
            $result['status'] = intval($result['status']);
        }
        return $result;
    }

    /**
     * 获取异常记录处理流程列表
     * @return \App\Model\BaseModel[]|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function getExceptionProcessList()
    {
        return ExceptionProcess::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where(DB::raw('upper(exception_block_id)'), strtoupper($this->params['exception_block_id']))
            ->orderBy('created_at', 'desc')
            ->getFromSR();
    }

    /**
     * 异常处理人表添加异常处理人
     * @throws \Exception
     */
    public function addExceptionHandler()
    {
        $result = ExceptionHandler::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('record_id', $this->params['record_id'])
            ->where(DB::raw('upper(exception_block_id)'), strtoupper($this->params['exception_block_id']))
            ->getFromSR();
        $i = 0;
        // 新增异常记录的处理人记录
        foreach ($this->params['handlers'] as $handler) {
            // 查询是否存在异常处理人记录
            $id = $result[$i]['id'] ?? Str::uuid()->toString();
            $i++;
            ExceptionHandler::query()->createFromSR([
                'id' => $id,
                'developer_app_id' => $this->params['developer_app_id'],
                'record_id' => $this->params['record_id'],
                'handler_id' => $handler['handler_id'],
                'handler' => $handler['handler_name'],
                'exception_block_id' => strtoupper($this->params['exception_block_id']),
                'type' => $this->params['type'],
            ]);
        }
        // 判断i是否等于$result的长度
        $count = count($result);
        if ($i < $count) {
            // 删除多余的异常处理人记录 
            for ($j = $i; $j < $count; $j++) {
                ExceptionHandler::query()->createFromSR([
                    'id' => $result[$j]['id'],
                    'developer_app_id' => $this->params['developer_app_id'],
                    'record_id' => $this->params['record_id'],
                    'handler_id' => 0,
                    'handler' => '',
                    'exception_block_id' => strtoupper($this->params['exception_block_id']),
                    'type' => $this->params['type'],
                ]);
            }
        }
    }
}
