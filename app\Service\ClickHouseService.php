<?php
/**
 * Created by phpstorm
 * User: liuxr
 * Date: 2022/5/30
 * Time: 10:57
 * TODO:上报推送信息至CK
 */

namespace App\Service;

use App\Jobs\ClickHouseUploadJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ClickHouseService
{
    /**
     * 表字段
     *
     * @var string[]
     */
    const COLUMNS = [
        'extra_app_id',
        'event_name',
        'exception_block_id',
        'stream_date',
        'stream_time',
        'timezone',
        'event_time',
        'dev_create_time',
        'server_dev_str',
        'type',
        'os_type',
        'version',
        'os_version',
        'app_version',
        'is_success',
        'operate_status',
        'release_store',
        'manufacturer',
        'device_model',
        'current_page_title',
        'app_id',
        'game_id',
        'scene_id',
        'referrer_scene_id',
        'project_id',
        'system_source',
        'account_id',
        'role_id',
        'role_name',
        'ip',
        'origin_country',
        'origin_province',
        'origin_city',
        'current_country',
        'current_province',
        'current_city',
        'subject_name',
        'sdk_package_name',
        'extra',
        'explain_desc',
        'content',
        'search_content',
        'page_info_json',
        'game_info_json',
        'basic_info_json',
        'memory_info_json',
        'console_info_json',
        'origin_stacks_json',
        'duration',
        'is_emulator',
        'exception_merge_id',
    ];

    /**
     * 获取文件路径
     *
     * @return string
     */
    protected static function getFilePath(): string
    {
        return "app/exception-stream-all/" . date('Y-m') . '/' . date('YmdHis') . '_' . Str::random() . '.csv';
    }

    /**上传sdk事件到新的用户数据仓库
     * 对接的接口文档地址:
     * http://developer.shiyuegame.com/showdoc/web/#/114?page_id=1480
     * @param array $data
     * @return bool
     */
    public static function uploadLogToCK(array $data): bool
    {
        try {
            //公用参数
            $commonData = [
                'account_id' => intval($data['account_id'] ?? 0),
                //系统来源,1：浏览器、2：公众号:、3：游戏客户端，4：服务端
                'system_source' => 3,
                //代表数据项目ID，系统唯一，如1:社区:、2:积分商城、3:游戏客户端，4:用户体系，5:活动通用，6:web支付, 9:效能系统
                'project_id' => 9,
                'game_id' => intval($data['project_id'] ?? ''),//项目id
                'scene_id' => 0,//场景id
                'referrer_scene_id' => 0,//进入页面前的场景标识
            ];
            //生成csv文件
            $path = (new WriteCsvFileService(self::getFilePath(), self::COLUMNS, [array_merge($commonData, $data)]))->write();
            //同步到数仓
            (new SyncFileToStarRocksService($path, self::COLUMNS, 'exception_stream_all'))->sync();
            //返回成功
            return true;
        } catch (\Exception $e) {
            Log::error('starRocks数据仓库数据上报失败：' . PHP_EOL . '错误信息：' . $e->getMessage() . ',代码行数:' . $e->getLine());
        }
        return false;
    }

//    /**上传sdk事件到新的用户数据仓库
//     * 对接的接口文档地址:
//     * http://developer.shiyuegame.com/showdoc/web/#/114?page_id=1480
//     * @param array $data
//     * @return bool
//     * @throws GuzzleException
//     */
//    public static function uploadLogToCK(array $data)
//    {
//        try {
//            $clickHouse = config('clickHouse');
//            //进行加密
//            $ts = (string)time();
//            $secret = $clickHouse['secret'];
//            $key = $clickHouse['key'];
//            $nonce = 'abcde121';
//            //公用参数
//            $commonData = [
//                'account_id' => intval($data['account_id'] ?? 0),
//                //系统来源,1：浏览器、2：公众号:、3：游戏客户端，4：服务端
//                'system_source' => 3,
//                //代表数据项目ID，系统唯一，如1:社区:、2:积分商城、3:游戏客户端，4:用户体系，5:活动通用，6:web支付, 9:效能系统
//                'project_id' => 9,
//                'game_id' => intval($data['project_id'] ?? ''),//项目id
//                'scene_id' => 0,//场景id
//                'referrer_scene_id' => 0,//进入页面前的场景标识
//            ];
//            $postData = [
//                'body' => array_merge($commonData, $data),
//                'sign' => md5($secret . $key . $ts . $nonce . $secret),
//                'ts' => $ts,
//                'key' => $key,
//                'nonce' => $nonce,
//            ];
//
//            $res = HttpAgent::getInstance()->request("POST", $clickHouse['upload_data_warehouse_url'], ['json' => $postData]);
//            $resultDecode = json_decode($res["message"], true);
//            if ($res["success"] == true && $resultDecode['code'] == 0) {
//                Log::info('upload CK success：' . PHP_EOL . '请求数据：' . json_encode($postData, JSON_UNESCAPED_UNICODE) . PHP_EOL . '返回结果：' . $res["message"]);
//                return true;
//            } else {
//                Log::error('ck数据仓库数据上报失败：' . PHP_EOL  . '请求数据：' . json_encode($postData) . PHP_EOL . '返回结果：' . $res["message"]);
//                return false;
//            }
//        } catch (\Exception $e) {
//            Log::error('ck数据仓库数据上报失败：' . PHP_EOL . '错误信息：' . $e->getMessage() . ',代码行数:' . $e->getLine());
//            return false;
//        }
//    }

    /**
     * 异步上传ck数据
     * @param $data
     * @return void
     */
    static function asyncUpload($data)
    {
        dispatch(new ClickHouseUploadJob($data))->onQueue('ck_upload');
    }
}
