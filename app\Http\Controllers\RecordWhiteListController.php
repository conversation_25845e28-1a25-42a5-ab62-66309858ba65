<?php

namespace App\Http\Controllers;

use App\Components\Scope\Scope;
use App\Service\RecordWhiteListService;
use Illuminate\Http\Request;

class RecordWhiteListController extends BaseController
{
    /**
     * @var array 请求参数
     */
    protected $params;

    /**
     * @var RecordWhiteListService 异常记录白名单服务类
     */
    protected $recordWhiteListService;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->params = $request->toArray();
        $this->recordWhiteListService = new RecordWhiteListService($this->params);
    }

    /**
     * 异常记录白名单列表
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3951
     */
    public function list()
    {
        try {
            $result = $this->recordWhiteListService->recordsWhiteList();
            return $this->response(0, $result);
        } catch (Exception $e) {
            \Log::error('获取异常记录列表白名单失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 操作异常记录，从白名单中添加/删除异常记录
     * 支持批量操作
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3952
     */
    public function operate()
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'operate_type' => 'required|integer',
                "exception_block_ids" => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }

            // 更新异常记录白名单状态
            $this->recordWhiteListService->updated();

            return $this->response();
        } catch (Exception $e) {
            \Log::error('异常记录白名单列表操作失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
