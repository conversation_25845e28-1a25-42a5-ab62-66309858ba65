<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddWhiteListToRecordNewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('record_new', function (Blueprint $table) {
            $table->unsignedTinyInteger('is_add_white_list')->default(0)->comment('是否添加白名单，1:已添加、0:未添加');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('record_new', function (Blueprint $table) {
            $table->dropColumn('is_add_white_list');
        });
    }
}
