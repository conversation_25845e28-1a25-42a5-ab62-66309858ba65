<?php
/**
 * WXGroupNoticeService.php
 * <AUTHOR>
 * @date 2022/10/19
 * @email <EMAIL>
 */

namespace App\Service;

use App\Components\HttpAgent;
use GuzzleHttp\Exception\GuzzleException;

class WXGroupNoticeService
{
    private $url;
    private $isMentioned;

    /**
     * @param string $url webhook地址
     * @param bool $isMentioned 是否提醒群内所有人
     */
    public function __construct(string $url, bool $isMentioned = false)
    {
        $this->url = $url;
        $this->isMentioned = $isMentioned;
    }

    /**
     * 企业微信群通知
     * @param string $content
     * @param string $type markdown或者text
     * @return bool
     */
    public function wxGroupNotify(string $content, string $type = 'text'): bool
    {
        try {
            $list = null;
            if ($this->isMentioned) {
                $list = ["@all"];
            }
            $string = json_encode($this->messageContent($content, $list, $type));
            //判断编码
            $cur_encode = mb_detect_encoding($string, ['ASCII', 'GB2312', 'GBK', 'BIG5', 'UTF-8']);
            //进行http请求
            $res = HttpAgent::getInstance()->request(
                'POST', $this->url,
                [
                    //将编码转为 'UTF-8'
                    'body' => mb_convert_encoding($string, 'UTF-8', $cur_encode),
                    'headers' => ['content-type' => 'application/json']
                ]
            );
            return $res["success"];
        } catch (GuzzleException $e) {
            \Log::error('企业微信通知失败：' . PHP_EOL . '错误信息：' . $e->getMessage() . ',代码行数:' . $e->getLine());
            return false;
        }

    }

    /**
     * 按照请求参数的格式拼数据
     * @param $content
     * @param array $list
     * @param string $type
     * @return array
     */
    public function messageContent($content, array $list, string $type = 'text'): array
    {
        return [
            'msgtype' => $type,
            $type => array_merge(['content' => $content], ["mentioned_list" => $list])
        ];
    }
}
