<?php

/**
 * CrashScope.php
 *
 * User: Dican
 * Date: 2022/9/22
 * Email: <<EMAIL>>
 */

namespace App\Components\Scope;

use App\Model\BaseModel;
use App\Model\ExceptionHandler;
use App\Model\Record;
use App\Model\VersionWhiteList;
use App\Service\Exception\KeyWordFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CrashScope extends Scope
{
    //查询时间类型 1为发生时间 2为上报时间
    const TIME_TYPE_CREATE = 1;
    const TIME_TYPE_UPLOAD = 2;

    public $crashExplains; //崩溃描述集合
    public $recordId;
    public $timeType; //查询时间类型
    public $breakStatus; //越狱状态
    public $consoleStatus; //前后台状态
    public $crashType; //崩溃类型
    public $issueStatus; //issue状态
    public $appPage; //APP页面
    public $handler; //处理人
    public $originStacks; //崩溃详情
    public $crashNames; //崩溃说明集合
    public $crashCategories; //具体崩溃类型集合
    public $roleId; //角色id
    public $roleName; //角色名称
    public $name; //崩溃说明
    public $explain; //崩溃说明
    public $category; //具体崩溃类型
    public $isEmulator; //是否为模拟器
    public $exceptionUniqueId; //异常类型唯一id
    public $exceptionBlockId; //异常类型唯一id
    public $relateRecord; // 关联录屏
    public $relatePerf; // 关联性能
    public $hasAttachment; // 是否有附件
    public $crashPluginVersion; // 崩溃插件版本

    public $serverId; //服务器ID

    public function getBuilder(Builder $builder, $isFilterPackageName = true)
    {
        //判断当需不需要进行查询崩溃名
        $isCheck = false;
        //处理状态和处理人和具体异常类型的筛选 通过MySQL查询得出的崩溃名和崩溃描述和具体崩溃类型 再根据崩溃名+崩溃描述+具体崩溃类型查询CK
        if ($this->recordId) {
            $crashes = Record::where('developer_app_id', $this->developerAppId) //研发效能的id
                ->where('type', $this->type)
                ->when(!empty($this->recordId), function ($query) {
                    $query->where('record_id', $this->recordId);
                })->when(!empty($this->exceptionUniqueId), function ($query) {
                    $query->whereIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, is_array($this->exceptionUniqueId) ? $this->exceptionUniqueId : [$this->exceptionUniqueId]));
                })->get(['name', 'explain', 'category']);
            //如果崩溃个数为0 则将崩溃名和崩溃描述和具体崩溃类型置空并设置需要查询崩溃名
            if (count($crashes) === 0) {
                $isCheck = true;
                $this->crashNames = [""];
                $this->crashExplains = [""];
                $this->crashCategories = [-1];
            }
            foreach ($crashes as $crash) {
                $this->crashNames[] = addslashes($crash->name);
                $this->crashExplains[] = addslashes($crash->explain);
                $this->crashCategories[] = $crash->category;
            }
        }
        return parent::getBuilder($builder)->where(function ($query) {
            $query->whereIn('type', Record::$categoryIndex)->orWhere('type', null);
        })
            ->when((!empty($this->crashNames)) || $isCheck, function ($query) {
                $query->whereIn('subject_name', $this->crashNames);
            })->when((!empty($this->crashExplains)) || $isCheck, function ($query) {
                $query->whereIn('explain_desc', $this->crashExplains);
            })->when((!empty($this->crashCategories)) || $isCheck, function ($query) {
                $query->where(function ($query) {
                    $query->whereIn('type', $this->crashCategories)->orWhere('type', null);
                });
            })->when($this->isTimeFilter, function ($query) {
                if ($this->timeType == self::TIME_TYPE_CREATE) {
                    $query->whereBetween('dev_create_time', [$this->startTime, $this->endTime]);
                } elseif ($this->timeType == self::TIME_TYPE_UPLOAD) {
                    $query->whereBetween('stream_date', [date('Y-m-d', $this->startTime), date('Y-m-d', $this->endTime)]);
                }
            })->when(!empty($this->innerVersion), function ($query) {
                $query->whereIn('inner_version', $this->innerVersion);
            })->when(isset($this->breakStatus), function ($query) {
                $query->where('is_success', $this->breakStatus);
            })->when(isset($this->consoleStatus), function ($query) {
                $query->where('operate_status', $this->consoleStatus);
            })->when(isset($this->roleId), function ($query) {
                $query->where(function ($query) {
                    return $query->whereRaw("`role_id` = ?", [$this->roleId])
                        ->orWhereRaw("`extra` LIKE ?", ['%' . $this->roleId . '%']);
                });
            })->when(isset($this->roleName), function ($query) {
                $query->where(function ($query) {
                    return $query->whereRaw("`role_name` LIKE ?", ['%' . $this->roleName . '%'])
                        ->orWhereRaw("`extra` LIKE ?", ['%' . $this->roleName . '%']);
                });
            })->when(isset($this->serverId), function ($query) {
                $query->where(function ($query) {
                    $arr = explode(',', $this->serverId);
                    foreach ($arr as $v) {
                        $query->orWhereRaw("`extra` LIKE ?", ['%{"key":"serverID","value":"' . $v . '","name":"服务器的ID"}%']);
                    }
                    return $query;
                });
            })->when(isset($this->originStacks), function ($query) {
                $query->where(function ($query) {
                    $columns = ['explain_desc', 'subject_name', 'origin_stacks_json'];
                    $values = [$this->originStacks, urlencode($this->originStacks), rawurlencode($this->originStacks)];
                    foreach ($columns as $column) {
                        foreach ($values as $value) {
                            $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($value) . '%'])
                                ->orWhereRaw(
                                    "replace(lower({$column}), '%0a', '%20') like ? ",
                                    ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $value)) . '%']
                                );
                        }
                    }
                });
            })->when(!empty($this->appPage), function ($query) {
                $query->whereIn('current_page_title', json_decode($this->appPage, true));
            })->when(!empty($this->crashType), function ($query) {
                $query->where(function ($query) {
                    $query->whereIn('type', json_decode($this->crashType, true))->orWhere('type', null);
                });
            })->when(isset($this->name), function ($query) {
                $query->where('subject_name', addslashes($this->name));
            })->when(isset($this->explain), function ($query) {
                if (is_array($this->explain)) {
                    $query->whereIn('explain_desc', $this->explain);
                } else {
                    $query->where('explain_desc', addslashes($this->explain));
                }
            })
            // ->when(isset($this->category), function ($query) {
            //     $query->where(function ($query) {
            //         $query->where('type', $this->category)->orWhere('type', null);
            //     });
            // })
            ->when(isset($this->isEmulator), function ($query) {
                $query->where('is_emulator', $this->isEmulator);
            })->when(isset($this->exceptionBlockId), function ($query) {
                $query->whereIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                    return strtoupper($item);
                }, is_array($this->exceptionBlockId) ? $this->exceptionBlockId : [$this->exceptionBlockId]));
            })->when(isset($this->isFilter), function ($query) {
                $exceptionBlockIds = Record::getExceptionBlockIds($this->developerAppId, $this->type);
                $appVersion = VersionWhiteList::getVersionList($this->developerAppId);
                $keywordFilterService = new KeyWordFilter($this->developerAppId);
                $keywordFilter = $keywordFilterService->getNotStatKeyword();
                !empty($this->defaultStartDate) && $keywordFilterService->setDate(date('Y-m-d', strtotime($this->defaultStartDate)));
                $query->when(!empty($exceptionBlockIds), function ($query) use ($exceptionBlockIds) {
                    $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, $exceptionBlockIds));
                })->when(!empty($appVersion), function ($query) use ($appVersion) {
                    $query->whereNotIn('app_version', $appVersion);
                })->when($keywordFilter, function ($query) use ($keywordFilterService) {
                    return $query->whereRaw($keywordFilterService->getFilterSql());
                });
            })->when(!empty($this->handler), function ($query) { // 根据处理人筛选出的id数组，进行记录筛选
                $handlerId = json_decode($this->handler, true);
                if (empty($handlerId)) {
                    $handlerId = explode(',', $this->handler);
                }
                $handlerIdResult = ExceptionHandler::getExceptionBlockIdByHandler($this->developerAppId, $handlerId, $this->type);
                return $query->whereRaw("upper(exception_block_id) in ({$handlerIdResult})");
            })->when(!empty($this->issueStatus), function ($query) { // 根据处理状态筛选出的id数组，进行记录筛选
                $handlerStatus = json_decode($this->issueStatus, true);
                if (empty($handlerStatus)) {
                    $handlerStatus = explode(',', $this->issueStatus);
                }
                list($opt, $sql) = Record::getHandleStatusSql($this->developerAppId, $this->type, $handlerStatus);
                return $query->whereRaw("upper(exception_block_id) {$opt} (" . $sql . ")");
            })->whereRaw('NULL_OR_EMPTY(subject_name) = false')
            ->when($this->useDuration, function ($query) {
                $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$this->useDuration}");
            })
            ->when($this->crashPluginVersion, function ($query, $crashPluginVersion) {
                return $query->whereRaw("`basic_info_json` LIKE '%crash_plugin_ver%' AND get_json_object(`basic_info_json`, 'crash_plugin_ver') = '{$crashPluginVersion}'");
            })
            ->when($this->hasAttachment > 0, function ($query) {
                return $query->whereRaw("NULL_OR_EMPTY(`exception_file2`) = false");
            })
            ->when($this->relateRecord > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where record_id > 0 and server_dev_str != '协议未被同意')");
            })
            ->when($this->relatePerf > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where perf_id > 0 and server_dev_str != '协议未被同意')");
            })->when($isFilterPackageName && (BaseModel::FILTER_PACKAGE_NAME[$this->developerAppId] ?? false), function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->developerAppId]);
            });
    }
}
