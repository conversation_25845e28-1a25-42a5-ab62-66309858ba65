<?php

declare(strict_types=1);

namespace App\Components\Helper;

use App\Components\Redis\RedisHandler;
use GuzzleHttp\Cookie\CookieJar;
use Throwable;

/**
 * class TraceHelper
 * @package Component
 * author : zeng<PERSON><PERSON><PERSON>@msn.com
 * datetime : 2023-05-24 16:41
 * memo : 待添加：1方法執行時間，匯總至redis/排行
 */
class CommonHelper
{

    /**
     * @param $variable
     * @param string $title
     * author : <EMAIL>
     * datetime : 2023-05-24 16:48
     * memo : 存在IO瓶頸，僅使用於開發調試
     */
    public static function xdebug($variable, string $title = 'defaultTitle'): void
    {
        if (env('APP_ENV') !== 'local') return;
        $traceInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        //co(function() use($variable, $title, $traceInfo){
        try {
            //$path = (env('APP_ENV') == 'localhost') ? "/windows/runtime/hyperf.log" : BASE_PATH . "/runtime/xdebug-0000-00-" . date("d") . ".log";//keep it for one month
            $path = '/windows/runtime/exceptionManager.log';
            $scriptName = $line = '';
            if ($traceInfo[0]) {//last track
                $file = $traceInfo[0]['file'];
                $line = $traceInfo[0]['line'];
                $startIndex = strrpos($file, DIRECTORY_SEPARATOR);
                $scriptName = substr($file, $startIndex + 1);
            }
            //end-----
            //special type conversion，start-----
            if (true === $variable) {
                $variable = 'TRUE(BOOL)';
            } elseif (false === $variable) {
                $variable = 'FALSE(BOOL)';
            } elseif (null === $variable) {
                $variable = 'NULL';
            } elseif ('' === $variable) {
                $variable = "(empty string)";
            } elseif ($variable instanceof Throwable) {
                $variable = [
                    'file' => $variable->getFile() . "(line:{$variable->getLine()})",
                    'message' => $variable->getMessage(),
                ];
                $title .= "Throwable";
            }
            //special type conversion，end-----
            $startMemory = memory_get_usage();
            $content = @print_r($variable, true);
            $usedMemory = memory_get_usage() - $startMemory;
            //##################################################
            //compatible file_put_contents() cannot be created automatically
            if (!file_exists($path)) @touch($path);
            $space = @count(file($path)) ? "\n\n\n" : ''; //interval control(3-1/line)
            //input layout，start-----
            $template = "{$space}//" . date('Y-m-d H:i:s') . ",start-----\n";
            $template .= "//{$title}(" . $_SERVER['DOCUMENT_ROOT'] . ">>{$scriptName}/line:{$line}/memory:{$usedMemory}byte)\n";
            $template .= "{$content}\n";
            $template .= "//end-----";
            //input layout，end-----
            if (abs(filesize($path)) > 1024 * 1024 * 1024) {//flush beyond the limit/1024m
                @file_put_contents($path, $template/*, LOCK_EX*/); //TODO:阻塞風險
            } else {
                @file_put_contents($path, $template, FILE_APPEND/* | LOCK_EX*/);
            }
        } catch (\Throwable $e) {
            //TODO:none...
        }
        //});
    }

    public static function checkExpendTime(callable $callable, string $label = 'defaultTitle')
    {
        $t1 = microtime(true);
        $result = $callable();
        $t2 = microtime(true);
        self::xdebug(sprintf("%0.10f", $t2 - $t1), $label);
        return $result;
    }


//    public static function commonCurlPost($url, array $body, array $header = ['Content-Type' => 'application/json']){
//        $config = [
//            'json' => !empty($body) ? $body : new \stdClass(),
//            'headers' => $header,
//            'timeout' => 3,
//        ];
//        $client = new \GuzzleHttp\Client($config);
//        return json_decode((string)$client->post($url, $body)->getBody(), true);
//    }

    public static function commonHttpPost(
        string $uri,
        array $body = [],
        array $header = ['Content-Type' => 'application/json'],
        array $cookieDetail = [],
        string $cookieDomain = ''
    )
    {
        $config = [
            'timeout' => 5,
            'headers' => $header,
            'json' => $body,
        ];
        if($cookieDetail && $cookieDomain){
            $config['cookies'] = CookieJar::fromArray($cookieDetail, $cookieDomain);
        }
        $client = new \GuzzleHttp\Client($config);
        $response = $client->post($uri, $config);
        return json_decode((string)$response->getBody(), true);

    }

    public static function commonHttpGet(
        string $uri,
        array $query = [],
        array $header = [],
        array $cookieDetail = [],
        string $cookieDomain = ''
    )
    {
        $config = [
            'query' => $query,
        ];
        if($header) $config['headers'] = $header;
        if($cookieDetail && $cookieDomain) $config['cookies'] = CookieJar::fromArray($cookieDetail, $cookieDomain);
        $client = new \GuzzleHttp\Client($config);
        return json_decode((string)$client->request('GET', $uri, $config)->getBody(), true);
    }

    public static function prettyJsonEncode($object, ?int $flag = JSON_PRETTY_PRINT): string
    {
        //JSON_PRETTY_PRINT//易讀格式（即：自動換行）
        $flagCounter = JSON_UNESCAPED_SLASHES/*不轉義反斜杠*/ | JSON_UNESCAPED_UNICODE/*unicode轉至中文*/;
        if (!$flag) {
            $flagCounter |= $flag;
        }
        return json_encode($object, $flagCounter);
    }

    public static function pagination(array $list, int $pageIndex, int $pageSize): array
    {
        $recordNum = count($list);
        $pageLimit = intval(ceil($recordNum / $pageSize));
        if ($pageIndex < 1) {
            $pageIndex = 1;
        } elseif ($pageIndex > $pageLimit && $pageLimit != 0 ) {
            $pageIndex = $pageLimit;
        }
        $start = intval(($pageIndex - 1) * $pageSize);
        $currentList = $list ? array_slice($list, $start, $pageSize) : [];
        return [
            'list' => $currentList,
            'page_index' => $pageIndex,
            'page_size' => $pageSize,
            'page_limit' => $pageLimit,
            'total' => $recordNum,
        ];
    }

    /**
     * @param array $array
     * @param string $slaveField
     * @param string $sort
     * @return array
     * author : <EMAIL>
     * datetime: 2023/06/29 19:08
     * memo : 二維數組排序
     */
    public static function order(array $array, string $slaveField, string $sort = 'DESC'): array
    {
        $newArray = $valueArray = [];
        foreach ($array as $key => $value) {
            $valueArray[$key] = $value[$slaveField];
        }
        if (strtoupper($sort) === 'ASC') {
            asort($valueArray);
        } else {
            arsort($valueArray);
        }
        reset($valueArray);
        foreach ($valueArray as $key => $value) {
            $newArray[$key] = $array[$key];
        }
        return array_values($newArray);
    }

    /**
     * @param callable $callable
     * @param int $ttl
     * @return mixed
     * @throws \ReflectionException
     * author : <EMAIL>
     * datetime: 2023/07/18 10:15
     * memo : 通用冪等緩存
     */
    function idemExecute(callable $callable, int $ttl = RedisHandler::INIT['ttl'])
    {
        //example : App\Controller\IndexController_index_203b44837a4e70669009dd664e81769a
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $parameter = (new \ReflectionFunction($callable))->getStaticVariables();
        $unique = md5(json_encode($parameter));
        $redisKey = (isset($trace[1]['class'], $trace[1]['function']) ? ("{$trace[1]['class']}_{$trace[1]['function']}_") : ((string)time() . "_")) . $unique;
        $Redis = RedisHandler::redisInstance();
        $result = $Redis->get($redisKey);
        if (false === $result) {
            $result = json_encode($callable());
            $Redis->set($redisKey, $result, $ttl);
        }
        return json_decode($result, true);
    }

}
