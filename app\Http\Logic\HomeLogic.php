<?php

namespace App\Http\Logic;

use App\Components\ClickHouse\ClickHouse;
use App\Components\Helper\CommonHelper;
use App\Components\Redis\RedisHandler;
use App\Components\Redis\RedisKeyEnum;
use App\Model\BaseModel;
use App\Model\ClickHouse\Start;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Record;
use App\Model\VersionWhiteList;
use App\Service\Exception\KeyWordFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class HomeLogic
{

    const ELIMINATE_BOUNDARY = 1000; //cache timeout//緩存20分鐘，定時預加載10分鐘
    public static $ttl = 1800; // 过期时间
    public static $datePeriodType = [
        'DAY' => 'day', //按天
        'TODAY' => 'today', // 今天
        'WEEK' => 'week', //按週
        'MONTH' => 'month', //按月
        'LATEST7' => 'latest7', //近七天
        'LATEST30' => 'latest30', // 近30天
        'LATEST_YEAR' => 'latest_year', // 近一年
    ];

    public static $datePeriodKey = [
        'CURRENT' => 'current', //當前週期
        'LATEST' => 'latest', //上一週期
    ];

    /**
     * @param string $cookie
     * @param string $timeType
     * @return array
     * 首页数据——联网设备数
     */
    public function networkNumList(string $cookie, string $timeType): array
    {
        $isEliminate = true;
        return [
            'type' => 'device',
            'list' => $this->pullActiveDeviceNumListV2($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, $isEliminate),
        ];
    }

    /**
     * @param string $cookie
     * @param string $datePeriodType
     * @param string $returnDatePeriodKey
     * @param int $limit
     * @param bool $isEliminate 是否剔除
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/03 17:18
     * memo : 獲取聯網設備數量
     */
    public function pullActiveDeviceNumList(string $cookie, string $datePeriodType, string $returnDatePeriodKey = '', int $limit = 0, bool $isEliminate = false): array
    {
        $cache = $this->pullActiveDeviceNumCacheLogic($datePeriodType, $isEliminate);
        $appList = $this->pullAppList($cookie);
        $format = [];
        foreach ($cache as $datePeriodKey => $finallyActiveList) {
            //filter/fill app detail[START]
            foreach ($appList as $eachApp) {
                if ($isEliminate && !isset($finallyActiveList[$eachApp['app_id']]['num'])) {
                    continue;
                }
                // 设备排行榜剔除 num为0 的app(因为该接口兼容应用列表)
                $format[$datePeriodKey][$eachApp['app_id']] = [
                    'app_id' => $eachApp['app_id'],
                    'app_name' => $eachApp['app_name'],
                    'app_icon' => $eachApp['app_icon'],
                    'unit' => '',
                    'num' => intval($finallyActiveList[$eachApp['app_id']]['num'] ?? 0),
                    'label' => $finallyActiveList[$eachApp['app_id']]['label'] ?? '',
                ];
            }
            $format[$datePeriodKey] = array_column(CommonHelper::order($format[$datePeriodKey] ?? [], 'num'), null, 'app_id');
            //filter/fill app detail[END]
        }
        if ($returnDatePeriodKey) {
            $format = $format[$returnDatePeriodKey];
        }

        if ($returnDatePeriodKey && $limit) {
            $format = CommonHelper::pagination($format, 1, $limit)['list'];
        }

        return $format;
    }

    /**
     * 与pullActiveDeviceNumList 相比，app数据不需要根据用户拥有的app权限获取，直接获取所有的app数据
     * @param string $cookie
     * @param string $datePeriodType
     * @param string $returnDatePeriodKey
     * @param int $limit
     * @param bool $isEliminate
     * @return array
     */
    public function pullActiveDeviceNumListV2(string $cookie, string $datePeriodType, string $returnDatePeriodKey = '', int $limit = 0, bool $isEliminate = false): array
    {
        $cache = $this->pullActiveDeviceNumCacheLogic($datePeriodType, $isEliminate);
        $appList = $this->pullAllAppList($cookie);
        $format = [];
        foreach ($cache as $datePeriodKey => $finallyActiveList) {
            //filter/fill app detail[START]
            foreach ($appList as $eachApp) {
                if ($isEliminate && !isset($finallyActiveList[$eachApp['app_id']]['num'])) {
                    continue;
                }
                // 设备排行榜剔除 num为0 的app(因为该接口兼容应用列表)
                $format[$datePeriodKey][$eachApp['app_id']] = [
                    'app_id' => $eachApp['app_id'],
                    'app_name' => $eachApp['app_name'],
                    'app_icon' => $eachApp['app_icon'],
                    'unit' => '',
                    'num' => intval($finallyActiveList[$eachApp['app_id']]['num'] ?? 0),
                    'label' => $finallyActiveList[$eachApp['app_id']]['label'] ?? '',
                ];
            }
            $format[$datePeriodKey] = array_column(CommonHelper::order($format[$datePeriodKey] ?? [], 'num'), null, 'app_id');
            //filter/fill app detail[END]
        }
        if ($returnDatePeriodKey) {
            $format = $format[$returnDatePeriodKey];
        }

        if ($returnDatePeriodKey && $limit) {
            $format = CommonHelper::pagination($format, 1, $limit)['list'];
        }

        return $format;
    }

    public function pullActiveDeviceNumCacheLogic(string $datePeriodType, bool $isEliminate): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:PullActiveDeviceNumList:'] . md5($datePeriodType);
        return RedisHandler::autoGet($redisKey, function () use ($datePeriodType, $isEliminate) {
            $datePeriod = $this->pullDatePeriod($datePeriodType, 'timestamp');
            $cache = [];
            foreach ($datePeriod as $datePeriodKey => $value) {
                [$startTime, $endTime] = $value;
                //hitbug[START] 获取hitBug联网设备数据——appId分组
                $hitbugActiveList = (new ClickHouse())->getData((new Start())
                        ->selectRaw('`extra_app_id` AS `app_id`, COUNT(DISTINCT `server_dev_str`) AS `num`')
                        ->where('extra_app_id', '!=', 0)
                        ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                        ->groupBy('extra_app_id')
                        ->orderBy('num', 'desc'));
                //hitbug[END]
                //performance[START] 获取线上性能后台联网设备数据——appId分组
                $envi = (env('APP_ENV') == 'production') ? '' : 'test-';
                $domain = "https://{$envi}tool-manager.shiyue.com";
                $api = "/home/<USER>";
                $performanceActiveList = CommonHelper::commonHttpGet("{$domain}{$api}", [
                    'start_date' => date('Y-m-d H:i:s', $startTime),
                    'end_date' => date('Y-m-d H:i:s', $endTime),
                ])['data'];
                // $performanceActiveList = CommonHelper::order($result['data'], 'num');
                //performance[END]
                //push[START] 获取推送后台联网设备数据——appId分组
                $domain = "https://{$envi}push-manager.shiyue.com";
                $api = "/api/analysis/pullActiveDeviceNum";
                $pushActiveList = CommonHelper::commonHttpGet("{$domain}{$api}", [
                    'developer_app_id' => '0',
                    'start_date' => $startTime,
                    'end_date' => $endTime,
                ])['data'];
                // $pushActiveList = CommonHelper::order($result['data'], 'num');
                //push[END]
                //compare[START] 比较联网数据结果，取最大
                // 处理结果，以项目appId作为key 方便比较
                $hitbugActiveList = array_map(function ($item) {
                    $item["label"] = "hitbug";
                    return $item;
                }, array_column($hitbugActiveList, null, 'app_id'));

                $performanceActiveList = array_map(function ($item) {
                    $item["label"] = "performance";
                    return $item;
                }, array_column($performanceActiveList, null, 'app_id'));

                $pushActiveList = array_map(function ($item) {
                    $item["label"] = "push";
                    return $item;
                }, array_column($pushActiveList, null, 'app_id'));
                // 大小比较获取最终结果
                $finallyActiveList = array();
                foreach ([$pushActiveList, $performanceActiveList, $hitbugActiveList] as $array) {
                    foreach ($array as $item) {
                        $app_id = $item['app_id'];
                        $num = $item['num'];
                        $label = $item['label'];
                        if (!isset($finallyActiveList[$app_id]) || $num > $finallyActiveList[$app_id]['num']) {
                            $finallyActiveList[$app_id] = ['num' => $num, 'app_id' => $app_id, 'label' => $label];
                        }
                    }
                }
                // 判斷是否剔除
                if ($isEliminate) {
                    // 结果中，num < 1000的元素去掉
                    $finallyActiveList = array_filter($finallyActiveList, function ($item) {
                        return $item['num'] >= HomeLogic::ELIMINATE_BOUNDARY;
                    });
                    // 对结果按照num排序
                    $finallyActiveList = CommonHelper::order($finallyActiveList, 'num');
                }
                $finallyActiveList = array_column($finallyActiveList, null, 'app_id');
                $cache[$datePeriodKey] = $finallyActiveList;
            }
            return $cache;
        }, self::$ttl);
    }

    /**
     * @param string $type
     * @param string $status 返回樣式，值：date/日期（示例：2023-01-01），timestamp/時間戳（示例：）
     * author : <EMAIL>
     * datetime: 2023/06/30 15:02
     * memo : 獲取時間週期
     */
    public function pullDatePeriod(string $type = 'day', string $status = 'date'): array
    {
        $currentTimestamp = time();
        switch ($type) {
            case 'day':
                $latestPeriodStart = $latestPeriodEnd = date("Y-m-d", strtotime("yesterday"));
                $currentPeriodStart = $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'week':
                $latestPeriodStart = date("Y-m-d", strtotime("last week monday"));
                $latestPeriodEnd = date("Y-m-d", strtotime("last week sunday"));
                $currentPeriodStart = date("Y-m-d", strtotime("this week monday"));
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
            case 'month':
                $latestPeriodStart = date("Y-m-01", strtotime("last month"));
                $latestPeriodEnd = date("Y-m-t", strtotime("last month"));
                $currentPeriodStart = date("Y-m-01", $currentTimestamp);
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
            case 'today': // 今天
                $latestPeriodStart = date("Y-m-d", strtotime("-1 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-1 days"));
                $currentPeriodStart = date("Y-m-d", $currentTimestamp);
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest30': // 近30天 含：今天
                $latestPeriodStart = date("Y-m-d", strtotime("-59 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-30 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-29 days"));
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest_year': // 近一年 含：今天
                $latestPeriodStart = date("Y-m-d", strtotime("-731 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-366 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-365 days"));
                $currentPeriodEnd = date("Y-m-d", $currentTimestamp);
                break;
            case 'latest7': //含：今天
            default:
                $latestPeriodStart = date("Y-m-d", strtotime("-13 days"));
                $latestPeriodEnd = date("Y-m-d", strtotime("-7 days"));
                $currentPeriodStart = date("Y-m-d", strtotime("-6 days"));
                $currentPeriodEnd = date('Y-m-d', $currentTimestamp);
                break;
        }
        return [
            'current' => [
                $status == 'timestamp' ? strtotime("$currentPeriodStart 00:00:00") : $currentPeriodStart,
                $status == 'timestamp' ? strtotime("$currentPeriodEnd 23:59:59") : $currentPeriodEnd,
            ],
            'latest' => [
                $status == 'timestamp' ? strtotime("$latestPeriodStart 00:00:00") : $latestPeriodStart,
                $status == 'timestamp' ? strtotime("$latestPeriodEnd 23:59:59") : $latestPeriodEnd,
            ],
        ];
    }

    /**
     * @param string $cookie
     * @return array
     * author : <EMAIL>
     * datetime: 2023/06/29 21:09
     * memo : 獲取當前用戶的app列表
     */
    public function pullAppList(string $cookie): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:AppList:'] . md5($cookie);
        return RedisHandler::autoGet($redisKey, function () use ($cookie) {
            $envi = (env('APP_ENV') == 'production') ? '' : 'test-';
            $domain = "https://{$envi}developer-manager.shiyue.com";
            $api = "/app/list";
            $result = CommonHelper::commonHttpGet("{$domain}{$api}", [
                'page' => 1,
                'per_page' => 1000,
            ], ['Cookie' => $cookie]);
            $format = [];
            if ($list = ($result['data']['list'] ?? [])) {
                foreach ($list as $value) {
                    $format[$value['id']] = [
                        'app_id' => $value['id'],
                        'app_name' => $value['app_name'],
                        'app_icon' => $value['app_icon'],
                        'is_option' => $value['is_option'],
                        'status' => $value['status'],
                    ];
                }
            }
            return $format;
        }, self::$ttl);
    }

    /**
     * 效能后台权限优化2.0 排行榜改成获取
     * @param string $cookie
     * @return array
     */
    public function pullAllAppList(string $cookie): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:AppList:'] . md5($cookie);
        return RedisHandler::autoGet($redisKey, function () use ($cookie) {
            $envi = (env('APP_ENV') == 'production') ? '' : 'test-';
            $domain = "https://{$envi}developer-manager.shiyue.com";
            $api = "/authV2/getAllAppList";
            $result = CommonHelper::commonHttpGet("{$domain}{$api}", [
                'page' => 1,
                'per_page' => 1000,
            ], ['Cookie' => $cookie]);
            $format = [];
            // if ($list = ($result['data']['list'] ?? [])) {
            if ($list = ($result['data'] ?? [])) {
                foreach ($list as $value) {
                    $format[$value['id']] = [
                        'app_id' => $value['id'],
                        'app_name' => $value['app_name'],
                        'app_icon' => $value['app_icon'],
                    ];
                }
            }
            return $format;
        }, self::$ttl);
    }

    /**
     * @param string $cookie
     * @param string $timeType
     * @return array
     * 首页数据——崩溃率排行
     */
    public function crashRankList(string $cookie, string $timeType): array
    {
        $isEliminate = true;
        return [
            'type' => 'crash',
            'list' => $this->pullExceptionNumList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, ['exception_crash'], true, $isEliminate),
        ];
    }

    /**
     * @param string $cookie
     * @param string $datePeriodType
     * @param string $returnDatePeriodKey
     * @param int $limit
     * @param array|string[] $eventNameList
     * @param bool $rate 是否比率，值：true/比率，false/次數
     * @param bool $isEliminate
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/03 14:13
     * memo : 獲取異常次數
     */
    public function pullExceptionNumList(
        string $cookie,
        string $datePeriodType,
        string $returnDatePeriodKey = '',
        int $limit = 0,
        array $eventNameList = ['exception_crash', 'exception_error'],
        bool $rate = false,
        bool $isEliminate = false
    ): array {
        $cache = $this->pullExceptionNumListCacheLogic($datePeriodType, $eventNameList, $rate);
        $appList = $this->pullAppList($cookie);
        $format = [];
        foreach ($cache as $datePeriodKey => $value) {
            $eachList = $value['eachList']; //分子
            $base = $value['base']; //分母
            foreach ($appList as $eachApp) {
                $eachNum = $eachList[$eachApp['app_id']]['num'] ?? 0;
                if (!$eachNum) {
                    continue;
                }

                $format[$datePeriodKey][$eachApp['app_id']] = [
                    'app_id' => $eachApp['app_id'],
                    'app_name' => $eachApp['app_name'],
                    'app_icon' => $eachApp['app_icon'],
                    'unit' => '',
                    'num' => intval($eachNum),
                ];
                //rate[START]
                if ($rate) {
                    $numerator = $eachList[$eachApp['app_id']]['num'] ?? 0;
                    $denominator = $base[$eachApp['app_id']]['num'] ?? 0;
                    if ($isEliminate && $denominator < HomeLogic::ELIMINATE_BOUNDARY) {
                        unset($format[$datePeriodKey][$eachApp['app_id']]);
                        continue;
                    }
                    $eachRate = (!$numerator || !$denominator) ? 0 : self::customNumberFormat($numerator / $denominator * 100);
                    $format[$datePeriodKey][$eachApp['app_id']]['unit'] = '%';
                    $format[$datePeriodKey][$eachApp['app_id']]['num'] = $eachRate > 100 ? 100 : $eachRate;
                }
                //rate[END]
            }

            $format[$datePeriodKey] = isset($format[$datePeriodKey]) ? array_column(CommonHelper::order($format[$datePeriodKey], 'num'), null, 'app_id') : [];
        }
        if ($returnDatePeriodKey) {
            $format = array_values($format[$returnDatePeriodKey]);
        }

        if ($returnDatePeriodKey && $limit) {
            $format = CommonHelper::pagination($format, 1, $limit)['list'];
        }

        return $format;
    }

    /**
     * 获取所有app游戏数据（pullExceptionNumList是获取有权限的app游戏数据）
     * @param string $cookie
     * @param string $datePeriodType
     * @param string $returnDatePeriodKey
     * @param int $limit
     * @param array|string[] $eventNameList
     * @param bool $rate 是否比率，值：true/比率，false/次數
     * @param bool $isEliminate
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/03 14:13
     * memo : 獲取異常次數
     */
    public function pullExceptionNumAllList(
        string $cookie,
        string $datePeriodType,
        string $returnDatePeriodKey = '',
        int $limit = 0,
        array $eventNameList = ['exception_crash', 'exception_error'],
        bool $rate = false,
        bool $isEliminate = false
    ): array {
        $cache = $this->pullExceptionNumListCacheLogic($datePeriodType, $eventNameList, $rate);
        // $appList = $this->pullAppList($cookie);
        $appList = $this->pullAllAppList($cookie);
        $format = [];
        foreach ($cache as $datePeriodKey => $value) {
            $eachList = $value['eachList']; //分子
            $base = $value['base']; //分母
            foreach ($appList as $eachApp) {
                $eachNum = $eachList[$eachApp['app_id']]['num'] ?? 0;
                if (!$eachNum) {
                    continue;
                }

                $format[$datePeriodKey][$eachApp['app_id']] = [
                    'app_id' => $eachApp['app_id'],
                    'app_name' => $eachApp['app_name'],
                    'app_icon' => $eachApp['app_icon'],
                    'unit' => '',
                    'num' => intval($eachNum),
                ];
                //rate[START]
                if ($rate) {
                    $numerator = $eachList[$eachApp['app_id']]['num'] ?? 0;
                    $denominator = $base[$eachApp['app_id']]['num'] ?? 0;
                    if ($isEliminate && $denominator < HomeLogic::ELIMINATE_BOUNDARY) {
                        unset($format[$datePeriodKey][$eachApp['app_id']]);
                        continue;
                    }
                    $eachRate = (!$numerator || !$denominator) ? 0 : self::customNumberFormat($numerator / $denominator * 100);
                    $format[$datePeriodKey][$eachApp['app_id']]['unit'] = '%';
                    $format[$datePeriodKey][$eachApp['app_id']]['num'] = $eachRate > 100 ? 100 : $eachRate;
                }
                //rate[END]
            }

            $format[$datePeriodKey] = isset($format[$datePeriodKey]) ? array_column(CommonHelper::order($format[$datePeriodKey], 'num'), null, 'app_id') : [];
        }
        if ($returnDatePeriodKey) {
            $format = array_values($format[$returnDatePeriodKey]);
        }

        if ($returnDatePeriodKey && $limit) {
            $format = CommonHelper::pagination($format, 1, $limit)['list'];
        }

        return $format;
    }

    /**
     * @param string $datePeriodType
     * @param array $eventNameList
     * @param bool $rate
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 15:53
     * memo : 異常統計緩存列表
     */
    public function pullExceptionNumListCacheLogic(string $datePeriodType, array $eventNameList, bool $rate): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:PullExceptionNumList:'] . md5("{$datePeriodType}_" . json_encode($eventNameList) . "_" . intval($rate));
        return RedisHandler::autoGet($redisKey, function () use ($datePeriodType, $eventNameList, $rate) {
            $datePeriod = $this->pullDatePeriod($datePeriodType, 'timestamp');
            $ClickHouse = new ClickHouse();
            $cache = [];
            //保存Builders
            $queries = [];
            //组装Builders
            foreach ($datePeriod as $datePeriodKey => $value) {
                [$startTime, $endTime] = $value;
                //hitbug[START]
                $queries["{$datePeriodKey}_eachResult"] = (new Start())
                    ->selectRaw('`extra_app_id` AS `app_id`, COUNT(DISTINCT `server_dev_str`) AS `num`')
                    ->where('extra_app_id', '!=', 0)
                    ->whereIn('event_name', $eventNameList)
                    ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                    ->groupBy('extra_app_id')
                    ->orderBy('num', 'desc');
                //hitbug[END]
                if ($rate) {
                    $queries["{$datePeriodKey}_result"] = (new Start())
                        ->selectRaw('`extra_app_id` AS app_id, COUNT(DISTINCT `server_dev_str`) AS num')
                        ->where('extra_app_id', '!=', 0)
                        ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                        ->groupBy('extra_app_id');
                }
            }
            //并发查询
            $results = $ClickHouse->getMultiSqlData($queries);
            //组装结果
            $keys = array_keys($datePeriod);
            foreach ($keys as $key) {
                $eachResult = $results["{$key}_eachResult"] ?? [];
                if ($rate) {
                    $result = $results["{$key}_result"] ?? [];
                    $base = array_column($result, null, 'app_id'); // 去重后的联网总设备数
                }
                $eachList = array_column($eachResult, null, 'app_id'); // 去重后的 发生崩溃的设备数
                $cache[$key] = [
                    'base' => $base ?? [],
                    'eachList' => $eachList,
                ];
            }
            return $cache;
        }, self::$ttl);
    }

    /**
     * 处理小数点
     *
     * @param $number
     * @return float
     */
    public static function customNumberFormat($number): float
    {
        return (float) (number_format($number, 2, '.', ''));
    }

    /**
     * @param string $cookie
     * @param string $timeType
     * @return array
     * 首页数据——错误率排行
     */
    public function errorRankList(string $cookie, string $timeType): array
    {
        $isEliminate = true;
        return [
            'type' => 'error',
            // 'list' => $this->pullExceptionNumList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, ['exception_error'], true, $isEliminate),
            'list' => $this->pullExceptionNumAllList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, ['exception_error'], true, $isEliminate),
        ];
    }

    /**
     * @param string $cookie
     * @param string $timeType
     * @return array[]
     * author : <EMAIL>
     * datetime: 2023/07/03 16:52
     * memo : 排行數據
     */
    public function rankCommonList(string $cookie, string $timeType): array
    {
        $isEliminate = true;
        return [
            [
                'type' => 'device',
                'list' => $this->pullActiveDeviceNumList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, $isEliminate),
            ], [
                'type' => 'crash',
                'list' => $this->pullExceptionNumList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, ['exception_crash'], true, $isEliminate),
            ], [
                'type' => 'error',
                'list' => $this->pullExceptionNumList($cookie, self::$datePeriodType[$timeType], self::$datePeriodKey['CURRENT'], 10, ['exception_error'], true, $isEliminate),
            ],
        ];
    }

    /**
     * @param string $cookie
     * @param string $datePeriodType
     * @param string $keyword
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/03 14:43
     * memo : 排行搜索
     */
    public function rankSearchList(string $cookie, string $datePeriodType, string $keyword = ''): array
    {
        $activeDeviceNum = $this->pullActiveDeviceNumList($cookie, $datePeriodType);
        $exceptionNum = $this->pullExceptionNumList($cookie, $datePeriodType);
        $performanceNum = $this->pullPerformanceRankList($cookie, $datePeriodType);
        $appList = $this->pullAppList($cookie);
        $format = [];
        foreach ($appList as $appDetail) {
            if ($keyword && strpos($appDetail['app_name'], $keyword) === false) {
                continue;
            }

            $exceptionCurrent = $exceptionNum[self::$datePeriodKey['CURRENT']][$appDetail['app_id']]['num'] ?? 0;
            $exceptionLatest = $exceptionNum[self::$datePeriodKey['LATEST']][$appDetail['app_id']]['num'] ?? 0;
            $performanceCurrent = $performanceNum[self::$datePeriodKey['CURRENT']][$appDetail['app_id']]['num'] ?? 0;
            $performanceLatest = $performanceNum[self::$datePeriodKey['LATEST']][$appDetail['app_id']]['num'] ?? 0;
            $activeDeviceCurrent = $activeDeviceNum[self::$datePeriodKey['CURRENT']][$appDetail['app_id']]['num'] ?? 0;
            $activeDeviceLatest = $activeDeviceNum[self::$datePeriodKey['LATEST']][$appDetail['app_id']]['num'] ?? 0;
            $format[] = [
                'app_id' => $appDetail['app_id'],
                'app_name' => $appDetail['app_name'],
                'app_icon' => $appDetail['app_icon'],
                'app_status' => $appDetail['status'],
                'is_option' => $appDetail['is_option'] ?? 0,
                'performance_current' => $performanceCurrent,
                'performance_latest' => $performanceLatest,
                'device_current' => $activeDeviceCurrent,
                'device_latest' => $activeDeviceLatest,
                'exception_current' => $exceptionCurrent,
                'exception_latest' => $exceptionLatest,
                'diff' => $exceptionLatest ? (float) bcmul(bcdiv($exceptionCurrent - $exceptionLatest, $exceptionLatest, 4), 100, 2) : 0,
                'performance_diff' => $performanceLatest ? (float) bcmul(bcdiv($performanceCurrent - $performanceLatest, $performanceLatest, 4), 100, 2) : 0,
                'device_diff' => $activeDeviceLatest ? (float) bcmul(bcdiv($activeDeviceCurrent - $activeDeviceLatest, $activeDeviceLatest, 4), 100, 2) : 0,
            ];
        }
        return $format;
    }

    /**
     * @param string $cookie
     * @param string $datePeriodType
     * author : <EMAIL>
     * datetime: 2023/06/30 18:07
     * memo : 獲取性能排行列表//cache
     */
    public function pullPerformanceRankList(string $cookie, string $datePeriodType): array
    {
        $result = $this->pullPerformanceRankListCacheLogic($datePeriodType);
        //format[START]
        if ($result) {
            $appList = $this->pullAppList($cookie);
            $format = [];
            foreach ($result as $value) {
                $format['current'][$value['app_id']] = [
                    'app_id' => $value['app_id'],
                    'app_name' => $appList[$value['app_id']]['app_name'] ?? '',
                    'app_icon' => $appList[$value['app_id']]['app_icon'] ?? '',
                    'num' => $value['current'] ?? 0,
                ];
                $format['latest'][$value['app_id']] = [
                    'app_id' => $value['app_id'],
                    'app_name' => $appList[$value['app_id']]['app_name'] ?? '',
                    'app_icon' => $appList[$value['app_id']]['app_icon'] ?? '',
                    'num' => $value['latest'] ?? 0,
                ];
            }
        }
        //format[END]
        return $format ?? [];
    }

    /**
     * 处理性能排行榜
     *
     * @param string $datePeriodType
     * @return array
     */
    public function pullPerformanceRankListCacheLogic(string $datePeriodType): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:PullPerformanceRankList:'] . md5($datePeriodType);
        return RedisHandler::autoGet($redisKey, function () use ($datePeriodType) {
            $envi = (env('APP_ENV') == 'production') ? '' : 'test-';
            $domain = "https://{$envi}tool-manager.shiyue.com";
            $api = "/home/<USER>";
            $result = CommonHelper::commonHttpGet("{$domain}{$api}", [
                'type' => $datePeriodType,
            ]);
            return $result['data'] ?? [];
        }, self::$ttl);
    }

    /**
     * @param int $developerAppId
     * @param int $startTime
     * @param int $endTime
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/03 16:52
     * memo : 圖標（hitbug）
     */
    public function hitbugChart(int $developerAppId, int $startTime, int $endTime): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:HitbugChart:'] . md5("{$developerAppId}_{$startTime}_{$endTime}_new4");
        return RedisHandler::autoGet($redisKey, function () use ($developerAppId, $startTime, $endTime) {
            $ClickHouse = new ClickHouse();
            //过滤白名单
            $keywordFilterService = new KeyWordFilter($developerAppId);
            $keywordFilter = $keywordFilterService->getNotStatKeyword();
            $keywordFilterService->setDate(date('Y-m-d', $startTime));
            $appVersionWhite = VersionWhiteList::getVersionList($developerAppId);
            $exceptionBlockIdWhite = Record::query()
                ->where('developer_app_id', $developerAppId)
                ->where('is_add_white_list', 1)
                ->pluck('exception_block_id')
                ->toArray();
            //整理SQL
            $model = (new UserLogDataAll())
                ->where('extra_app_id', $developerAppId)
                ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                ->when($appVersionWhite, function (Builder $query, $appVersionWhite) {
                    return $query->whereNotIn('app_version', $appVersionWhite);
                })
                ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                    return $query->whereRaw($keywordFilterService->getFilterSql());
                })
                ->when($exceptionBlockIdWhite, function (Builder $query, $exceptionBlockIdWhite) {
                    return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                        return strtoupper($item);
                    }, $exceptionBlockIdWhite));
                })
                ->when(BaseModel::FILTER_PACKAGE_NAME[$developerAppId] ?? false, function ($query) use ($developerAppId) { // 屏蔽包名
                    return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$developerAppId]);
                });
            $abstractModel = (clone $model)
                ->selectRaw("`event_name`, COUNT(*) AS `num`, COUNT(DISTINCT `server_dev_str`) AS `device_num`")
                ->groupBy('event_name');
            $allModel = (new UserLogDataAll())
                ->where('extra_app_id', $developerAppId)
                ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                ->when(BaseModel::FILTER_PACKAGE_NAME[$developerAppId] ?? false, function ($query) use ($developerAppId) { // 屏蔽包名
                    return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$developerAppId]);
                })
                ->selectRaw("COUNT(DISTINCT `server_dev_str`) AS `device_num`, `stream_date` as `create_date`")
                ->groupBy('stream_date');
            $detailModel = (clone $model)
                ->selectRaw("`event_name`, COUNT(*) AS `num`, COUNT(DISTINCT `server_dev_str`) AS `device_num`, `stream_date` as `create_date`")
                ->where('event_name', 'exception_crash')
                ->groupBy('event_name', 'stream_date');
            $detailModel2 = (clone $model)
                ->selectRaw("`event_name`, COUNT(DISTINCT `server_dev_str`) AS `device_num`, `stream_date` as `create_date`")
                ->where('event_name', 'exception_error')
                ->groupBy('event_name', 'stream_date');
            $networkSumModel = (new UserLogDataAll())
                ->where('extra_app_id', $developerAppId)
                ->whereBetween('stream_date', [date('Y-m-d', $startTime), date('Y-m-d', $endTime)])
                ->when(BaseModel::FILTER_PACKAGE_NAME[$developerAppId] ?? false, function ($query) use ($developerAppId) { // 屏蔽包名
                    return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$developerAppId]);
                })
                ->selectRaw("COUNT(*) AS `num`, `stream_date` as `create_date`")
                ->where('event_name', 'exception_start')
                ->groupBy('stream_date');
            $countModel = (new UserLogDataAll())
                ->selectRaw("COUNT(*) AS `num`")
                ->where('extra_app_id', $developerAppId);
            //并发请求
            $results = $ClickHouse->getMultiSqlData([
                'abstract' => $abstractModel,
                'all' => $allModel,
                'detail' => $detailModel,
                'detail2' => $detailModel2,
                'networkSum' => $networkSumModel,
                'count' => $countModel,
            ]);
            $allDeviceNum = array_sum(array_column($results['all'], 'device_num'));
            $allDeviceNum = max($allDeviceNum, 1);
            $crashDeviceNum = array_sum(array_column($results['detail'], 'device_num'));
            $errorDeviceNum = array_sum(array_column($results['detail2'], 'device_num'));
            $startNum = array_sum(array_column($results['networkSum'], 'num'));
            $startNum = max($startNum, 1);
            //处理数据
            $abstractResult = $results['abstract']; // 获取crash、error的总记录数
            $affectIndexList = array_column($abstractResult, 'num', 'event_name');
            $crashRate = $affectIndexList ? round(($affectIndexList['exception_crash'] ?? 0) / $startNum * 100, 2) : 0;
            $errorRate = $affectIndexList ? round(($affectIndexList['exception_error'] ?? 0) / $startNum * 100, 2) : 0;
            $crashDevRate = $crashDeviceNum ? round($crashDeviceNum / $allDeviceNum * 100, 2) : 0;
            $errorDevRate = $errorDeviceNum ? round($errorDeviceNum / $allDeviceNum * 100, 2) : 0;
            $abstract = [
                'start_num' => $allDeviceNum,
                'crash_num' => $crashDeviceNum,
                'error_num' => $errorDeviceNum,
                'crash_rate' => $crashRate,
                'crash_dev_rate' => $crashDevRate,
                'error_rate' => $errorRate,
                'error_dev_rate' => $errorDevRate,
                'unit' => '%',
            ];
            $detailResult = $results['detail'];
            $networkSumResult = $results['networkSum'];
            $list = $detailFormat = $networkSumFormat = [];
            foreach ($detailResult as $value) {
                $eachIndex = "{$value['create_date']}_{$value['event_name']}";
                $detailFormat[$eachIndex] = $value['num'];
            }
            foreach ($networkSumResult as $value) {
                $eachIndex = "{$value['create_date']}";
                $networkSumFormat[$eachIndex] = $value['num'];
            }
            $currentPoint = $startTime;
            while ($currentPoint < $endTime) {
                $eachDate = date('Y-m-d', $currentPoint);
                $eachCrashIndex = "{$eachDate}_exception_crash";
                $eachNetWorkSumIndex = "{$eachDate}";
                $eachCrashRate = (($detailFormat[$eachCrashIndex] ?? 0) && ($networkSumFormat[$eachNetWorkSumIndex] ?? 0)) ? round($detailFormat[$eachCrashIndex] / $networkSumFormat[$eachNetWorkSumIndex] * 100, 2) : 0;
                $list[] = [
                    "timestamp" => $eachDate,
                    "score" => $eachCrashRate,
                    'unit' => '%',
                ];
                $currentPoint = $currentPoint + 86400;
            }
            return [
                'abstract' => $abstract,
                'list' => $list,
                'is_use' => ($results['count'][0]['num'] ?? 0) > 0,
            ];
        }, self::$ttl);
    }

    /**
     * author : <EMAIL>
     * datetime: 2023/07/11 15:43
     * memo : 預加載首頁數據
     */
    public function loadHomeDataCache($list = [])
    {
        @ini_set('memory_limit', '2G');
        @ini_set('max_execution_time', '0');
        $eventNameListCase = [
            ['exception_crash'],
            ['exception_error'],
            ['exception_crash', 'exception_error'],
        ];
        $rateCase = [
            true,
            false,
        ];
        if (empty($list)) {
            $list = self::$datePeriodType;
        }
        // foreach ($list as $eachDatePeriodType) {
        //     foreach ($eventNameListCase as $eachEventNameList) {
        //         foreach ($rateCase as $eachRate) {
        //             try {
        //                 $eachRedisKey = RedisKeyEnum::STRING['STRING:PullExceptionNumList:'] . md5("{$eachDatePeriodType}_" . json_encode($eachEventNameList) . "_" . intval($eachRate));
        //                 RedisHandler::del($eachRedisKey);
        //                 $this->pullExceptionNumListCacheLogic($eachDatePeriodType, $eachEventNameList, $eachRate);
        //                 $log = "loadHomeDataCache : {$eachDatePeriodType} / " . json_encode($eachEventNameList) . " / " . intval($eachRate) . " finished";
        //                 \Log::info($log);
        //             } catch (\Throwable $e) {
        //                 \Log::info("Throwable{$log}");
        //             }
        //         }
        //     }
        // }
        foreach ($list as $eachDatePeriodType) {
            try {
                //-----
                $eachRedisKey = RedisKeyEnum::STRING['STRING:PullActiveDeviceNumList:'] . md5($eachDatePeriodType);
                RedisHandler::del($eachRedisKey);
                $this->pullActiveDeviceNumCacheLogic($eachDatePeriodType, false);
                //-----
                $eachRedisKey = RedisKeyEnum::STRING['STRING:PullPerformanceRankList:'] . md5($eachDatePeriodType);
                RedisHandler::del($eachRedisKey);
                $this->pullPerformanceRankListCacheLogic($eachDatePeriodType);
                $log = "loadHomeDataCache : {$eachDatePeriodType} /  finished";
                \Log::info($log);
            } catch (\Throwable $e) {
                \Log::info("Throwable{$log}");
            }
        }
        RedisHandler::close();
    }
}
