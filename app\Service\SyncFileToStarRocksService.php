<?php

/**
 * 文件内容到starRocks
 * @desc 文件内容到starRocks
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use Illuminate\Support\Facades\Log;

class SyncFileToStarRocksService
{
    /**
     * starRocks配置
     *
     * @var array
     */
    protected $starRocksConfig;

    /**
     * 文件路径
     *
     * @var string
     */
    protected $path;

    /**
     * 文件名称
     *
     * @var string
     */
    protected $fileName;

    /**
     * 同步的字段
     *
     * @var array
     */
    protected $columns;

    /**
     * 数据库名称
     *
     * @var string
     */
    protected $database;

    /**
     * 构造函数
     *
     * @param $path
     * @param $columns
     * @param $database
     */
    public function __construct($path, $columns, $database)
    {
        $this->starRocksConfig = config('starRocks');
        $this->path = $path;
        $this->fileName = pathinfo($path)['filename'];
        $this->columns = $columns;
        $this->database = $database;
    }

    /**
     *
     * 同步文件内容到starRocks
     *
     * @return void
     */
    public function sync()
    {
        $curl = 'curl --location-trusted -u root:' . $this->starRocksConfig['starRocks_password'] . ' -H "label:' . $this->fileName . '" -H "column_separator:|$|$|" -H "columns: ' . implode(', ', $this->columns) . '" -H "Expect:100-continue" -T ' . $this->path . ' -XPUT ' . $this->starRocksConfig["starRocks_{$this->database}_url"];
        Log::info('文件同步到StarRocks，开始执行Stream Load同步，curl，' . $curl);
        exec($curl, $log, $pyres);
        Log::info('文件同步到StarRocks，Stream Load同步结果：' . json_encode($log));
        if ($pyres != 0) {
            Log::error("文件同步到StarRocks，Stream Load同步失败" . json_encode($log));
        }
        //删除文件
        unlink($this->path);
    }
}
