<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>jia Email: <EMAIL>
 * Date: 2022/06/01 11:54
 * CURL
 */
namespace App\Components\Helper;

use Illuminate\Support\Facades\Log;

class Curl
{
    private static $timeout = 300;    //300s
    private static $connectTimeout = 3;    //3s

    /**
     * 访问http地址，返回http的response数据
     * @param string $url http地址，需要带上http://头
     * @param array|string $params 参数
     * @return false | string false表示访问地址失败
     */
    public static function post($url, $params = [], $setting = [])
    {
        $qs = self::buildQueryString($params);
        $curl = curl_init($url);
        if (isset($setting)) {
            foreach ($setting as $option => $value) {
                curl_setopt($curl, $option, $value);          //设置其他配置信息
            }
        }
        curl_setopt($curl, CURLOPT_HEADER, false);          // 过滤HTTP头
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);   // 显示输出结果
        curl_setopt($curl, CURLOPT_USERAGENT, isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '');
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, self::$connectTimeout);
        curl_setopt($curl, CURLOPT_TIMEOUT, self::$timeout);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $qs);

        $data = curl_exec($curl);
        $errno = curl_errno($curl);
        $errmsg = curl_error($curl);

        curl_close($curl);

        if ($errno) {
            Log::error("访问 $url 时发生了错误[$errno]: $errmsg");
            return false;
        }

        return $data;
    }

    /**
     * 将传入的参数构建成query string添加到url后面，然后访问该地址，获取该地址的响应结果
     * @param string $url http地址，需要带上http://头
     * @param array $params 参数
     * @return false | string false表示访问失败
     */
    public static function get($url, $params = [])
    {
        $qs = self::buildQueryString($params);
        if (!empty($qs)) {
            if (strpos($url, '?') !== false) {
                $url = $url . '&' . $qs;
            } else {
                $url = $url . '?' . $qs;
            }
        }

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_HEADER, false);          // 过滤HTTP头
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);   // 显示输出结果
        curl_setopt($curl, CURLOPT_USERAGENT, isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '');
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, self::$connectTimeout);
        curl_setopt($curl, CURLOPT_TIMEOUT, self::$timeout);

        $data = curl_exec($curl);
        $errno = curl_errno($curl);
        $errmsg = curl_error($curl);

        curl_close($curl);

        if ($errno) {
            log::error("访问 $url 时发生了错误[$errno]: $errmsg");
            return false;
        }

        return $data;
    }

    /**
     * 发送带cookies的get请求
     * @param $url
     * @param $params
     * @param $cookies
     * @return bool|string
     */
    public static function getWithCookies($url, $params = [], $cookies = [])
    {
        $qs = self::buildQueryString($params);
        if (!empty($qs)) {
            if (strpos($url, '?') !== false) {
                $url = $url . '&' . $qs;
            } else {
                $url = $url . '?' . $qs;
            }
        }

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_HEADER, false);          // 过滤HTTP头
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);   // 显示输出结果
        curl_setopt($curl, CURLOPT_USERAGENT, isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '');
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, self::$connectTimeout);
        curl_setopt($curl, CURLOPT_TIMEOUT, self::$timeout);
        // 如果提供了 cookies，则设置它们
        if (!empty($cookies)) {
            $cookieString = self::buildCookieString($cookies);
            curl_setopt($curl, CURLOPT_COOKIE, $cookieString);
        }

        $data = curl_exec($curl);
        $errno = curl_errno($curl);
        $errmsg = curl_error($curl);

        curl_close($curl);


        if ($errno) {
            log::error("访问 $url 时发生了错误[$errno]: $errmsg");
            return false;
        }
        return $data;
    }

    /**
     * 构造cookies字符串
     * @param $cookies
     * @return string
     */
    private static function buildCookieString($cookies)
    {
        if (!is_array($cookies)) {
            return $cookies;
        }
        $cookieParts = [];
        foreach ($cookies as $name => $value) {
            $cookieParts[] = $name . '=' . urlencode($value);
        }
        return implode('; ', $cookieParts);
    }

    /**
     * json 请求
     * @param $url
     * @param $params
     * @param array $setting
     * @return bool|mixed
     */
    public static function json($url, $params, $setting = [])
    {
        $data = is_array($params) ? json_encode($params) : $params;
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, self::$connectTimeout);
        curl_setopt($curl, CURLOPT_TIMEOUT, self::$timeout);
        curl_setopt($curl, CURLOPT_HTTPHEADER, isset($setting['headers']) ? array_merge($setting['headers'], array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        )) : array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ));

        $result = curl_exec($curl);
        $errno = curl_errno($curl);
        $errmsg = curl_error($curl);
        curl_close($curl);
        if ($errno) {
            Log::error("访问 $url 时发生了错误[$errno]: $errmsg");
            return false;
        }

        return $result;
    }


    // 构建QueryString
    private static function buildQueryString($params)
    {
        return is_array($params) ? http_build_query($params) : $params;
    }


    //并发请求 post
    public static function multiRequest($urls)
    {
        $mh = curl_multi_init();
        $urlHandlers = [];
        $urlData = [];

        // 初始化多个请求句柄为一个
        foreach ($urls as $index => $value) {
            $ch = curl_init();
            $url = $value['url'];
            $params = $value['params'];
            $qs = self::buildQueryString($params);
            curl_setopt($ch, CURLOPT_URL, $url);
            // 设置数据通过字符串返回，而不是直接输出
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, self::$connectTimeout);
            curl_setopt($ch, CURLOPT_TIMEOUT, self::$timeout);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $qs);

            $urlHandlers[$index] = $ch;
            curl_multi_add_handle($mh, $ch);
        }

        $active = null;
        // 检测操作的初始状态是否OK，CURLM_CALL_MULTI_PERFORM为常量值-1
        do {
            // 返回的$active是活跃连接的数量，$mrc是返回值，正常为0，异常为-1
            $mrc = curl_multi_exec($mh, $active);
        } while ($mrc == CURLM_CALL_MULTI_PERFORM);
        // 如果还有活动的请求，同时操作状态OK，CURLM_OK为常量值0
        while ($active && $mrc == CURLM_OK) {
            // 持续查询状态并不利于处理任务，每50ms检查一次，此时释放CPU，降低机器负载
            usleep(50000);
            // 如果批处理句柄OK，重复检查操作状态直至OK。select返回值异常时为-1，正常为1(因为只有1个批处理句柄)
            if (curl_multi_select($mh) != -1) {
                do {
                    $mrc = curl_multi_exec($mh, $active);
                } while ($mrc == CURLM_CALL_MULTI_PERFORM);

            }
        }

        // 获取返回结果
        foreach ($urlHandlers as $index => $ch) {
            $urlData[$index] = curl_multi_getcontent($ch);
            // 移除单个curl句柄
            curl_multi_remove_handle($mh, $ch);
        }
        curl_multi_close($mh);
        return $urlData;
    }
}
