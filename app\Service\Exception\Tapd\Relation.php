<?php

/**
 * Tapd关联服务基础类
 * @desc Tapd关联服务基础类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/11/27
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Tapd;

use App\Components\ApiResponse\StatusCode;
use App\Http\Logic\TapdLogic;
use App\Model\TapdBug;
use App\Service\TapdService;
use Illuminate\Support\Facades\Log;

class Relation extends Base
{
    /**
     * 请求参数
     *
     * @var array
     */
    private $params;

    /**
     * 效能后台ID
     *
     * @var int
     */
    private $developerAppId;

    /**
     *  异常ID
     *
     * @var string
     */
    private $exceptionBlockId;

    /**
     * TAPD 配置信息
     *
     * @var array
     */
    private $tapdConfig;

    /**
     * 初始化
     *
     * @param array $params
     */
    public function __construct(array $params)
    {
        $this->params = $params;
        $this->developerAppId = $this->params['developer_app_id'];
        $this->exceptionBlockId = $this->params['exception_block_id'];
        $this->tapdConfig = $this->getAccountDetail($this->developerAppId);
    }

    /**
     * 创建缺陷
     *
     * @return array
     */
    public function save(): array
    {
        // 根据tapd_account_id获取绑定配置表
        if (empty($this->tapdConfig)) {
            Log::error("关联失败，tapdConfig为空");
            return [false, StatusCode::C_NOT_BOUND_TAPD_PROJECT, null];
        }
        //处理bugId
        $itemId = str_pad($this->tapdConfig['item_id'], 8, '0', STR_PAD_LEFT);
        $bugId = str_pad($this->params['bug_id'], 9, '0', STR_PAD_LEFT);
        $this->params['bug_id'] = "11{$itemId}{$bugId}";
        //判断是否存在
        if ($this->isExist()) {
            Log::error("关联失败，{$this->exceptionBlockId} 异常已绑定");
            return [false, StatusCode::C_RELATION_TAPD_BUG_FAIL, null];
        }
        // 调用tapd服务api获取缺陷信息
        $data = $this->getTapdBug();
        if ($data['status'] != 1 || count($data['data']) == 0) {
            Log::error("关联失败，获取缺陷报错，返回结果：" . json_encode($data));
            return [false, StatusCode::C_RELATION_TAPD_BUG_FAIL, null];
        }
        $bugInfo = $data['data'][0]['Bug'];
        // 创建TAPD
        $this->createTapdBug($bugInfo);
        // 返回结果
        return [true, 0, $bugInfo['id']];
    }

    /**
     * 判断Tapd是否存在
     *
     * @return bool
     */
    public function isExist(): bool
    {
        return TapdBug::query()
            ->where('exception_block_id', $this->exceptionBlockId)
            ->where('developer_app_id', $this->developerAppId)
            ->where('tapd_bug_id', $this->params['bug_id'])
            ->where('bind_status', '!=', 0)
            ->exists();
    }

    /**
     * 提交缺陷
     *
     * @return mixed
     */
    private function getTapdBug()
    {
        // 调用tapd服务api获取缺陷，获取到对应的参数
        return TapdService::get(TapdService::TAPD_CREATE_BUG_URL, $this->getPostTapdBugParams());
    }

    /**
     * 获取提交缺陷请求参数
     *
     * @return array
     */
    private function getPostTapdBugParams(): array
    {
        $params = $this->params;
        //移除不需要的参数
        unset($params['developer_app_id'], $params['reporter_id'], $params['exception_block_id'], $params['type']);
        //添加项目参数
        $params['workspace_id'] = $this->tapdConfig['item_id'];
        $params['id'] = $params['bug_id'];
        //打印日志
        Log::info("关联TAPD的请求参数：" . json_encode($params));
        //返回参数
        return $params;
    }

    /**
     * 创建TAPD
     *
     * @param array $data
     * @return void
     */
    private function createTapdBug(array $data): void
    {
        // 通过模型类创建缺陷表记录
        $tapdBug = new TapdBug();
        $tapdBug->developer_app_id = $this->params['developer_app_id'];
        $tapdBug->exception_block_id = $this->params['exception_block_id'];
        $tapdBug->tapd_account_id = $this->tapdConfig['id'];
        $tapdBug->tapd_bug_id = $data['id'];
        $tapdBug->type = $this->params['type'];
        $tapdBug->detail = json_encode($this->getPostTapdBugParams(), JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $tapdBug->tapd_link = $this->structTapdLink($this->tapdConfig['item_id'], $data['id']);
        $tapdBug->create_type = 2;
        $tapdBug->creator_id = $this->params['reporter_id'];
        $tapdBug->bind_status = 1; // 默认为已绑定
        $tapdBug->status = $data['status'];
        $tapdBug->save();
    }

    /**
     * 生成跳转到tapd的链接
     *
     * @param int $workspaceId 项目id
     * @param int $bugId 缺陷id
     * @return string
     */
    public function structTapdLink(int $workspaceId, int $bugId): string
    {
        return TapdLogic::TAP_BUG_VIEW_DOMAIN . $workspaceId . TapdLogic::TAP_BUG_VIEW_URI . '?bug_id=' . $bugId;
    }
}
