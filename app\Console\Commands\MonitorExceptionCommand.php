<?php

/**
 * 监控数据脚本
 * @desc 监控数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Model\AppModel;
use App\Model\BaseModel;
use App\Model\StarRocks\ExceptionStreamAll;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorExceptionCommand extends Command
{
    /**
     * 机器人webhook地址
     *
     * @var string
     */
    const HOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cffbb57a-3a02-407b-b320-fdaecafd20a2';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:exception {operation? : 操作} ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //打印日志
            Log::info("执行监控数据脚本开始");
            // 获取操作类型
            $operation = $this->argument('operation');
            // 获取昨天时间
            $yesterday = Carbon::yesterday()->toDateString();
            // 获取APP信息
            $apps = AppModel::query()->pluck('app_name', 'id');
            // 判断是否发送数据
            if (empty($operation) || $operation == 'data') {
                $this->sendData($yesterday, $apps);
            }
            // 判断是否发送异常ID
            if (empty($operation) || $operation == 'exception') {
                $this->sendExceptionId($yesterday, $apps);
            }
            //打印日志
            Log::info("执行监控数据脚本完成");
        } catch (\Exception $e) {
            Log::error("执行监控数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    /**
     * 发送昨日数据
     *
     * @param string $yesterday 昨天日期字符串
     * @param array $apps App信息数组，key为App ID，value为App名称
     * @return void
     */
    private function sendData($yesterday, $apps)
    {
        // 组装msg信息
        $content = "HitBug昨日异常数据推送【{$yesterday}】：\n";
        // 获取数据
        $list = ExceptionStreamAll::query()
            ->selectRaw('extra_app_id, event_name, os_type, count(*) as num, count(distinct server_dev_str) as dev_num')
            ->where('stream_date', $yesterday)
            ->where('event_name', '!=', 'exception_start')
            ->groupBy('extra_app_id', 'event_name', 'os_type')
            ->getFromSR();
        // 判断数据是否为空
        if (!empty($list)) {
            // 处理数据
            $list = $this->handleData($list, 'extra_app_id');
            // 循环处理数据
            foreach ($list as $item) {
                // App名称
                $appName = $apps[$item['extra_app_id']] ?? '未知';
                // 判断是否是错误还是崩溃
                $eventName = $item['event_name'] == 'exception_error' ? '错误' : '崩溃';
                // 文字内容
                $text = '';
                // 循环处理数据
                foreach ($item['list'] as $val) {
                    // 系统
                    $osType = BaseModel::OS_TYPE_TEXT[$val['os_type']] ?? '未知';
                    // 文字内容
                    $text .= "【{$osType}】：{$val['num']} 条，{$val['dev_num']} 设备 | ";
                }
                // 把最后的, 去掉
                $text = rtrim($text, " | ");
                // 拼接字符串
                $content .= "{$appName} （{$eventName}）：{$item['total']} 条，{$item['dev']} 设备 | {$text}\n";
            }
            // 把最后的换行符去掉
            $content = rtrim($content, "\n");
            // 发送请求
            HttpAgent::getInstance()->request("POST", self::HOOK_URL, [
                'json' => [
                    'msgtype' => 'text',
                    'text' => [
                        'content' => $content,
                    ],
                ],
            ]);
        }
    }

    /**
     * 发送异常ID信息到企业微信
     *
     * @param string $yesterday 昨天日期字符串
     * @param array $apps App信息数组，key为App ID，value为App名称
     * @return void
     */
    private function sendExceptionId($yesterday, $apps)
    {
        // 重置文本
        $content = "HitBug昨日异常问题推送【{$yesterday}】：\n";
        // 错误错误比较多的问题数据
        $list = ExceptionStreamAll::query()
            ->selectRaw('extra_app_id, event_name, exception_block_id, count(*) as num')
            ->where('stream_date', $yesterday)
            ->where('event_name', '!=', 'exception_start')
            ->groupBy('extra_app_id', 'event_name', 'exception_block_id')
            ->orderBy('num', 'desc')
            ->limit(30)
            ->getFromSR();
        // 判断数据是否为空
        if (!empty($list)) {
            // 循环处理数据
            foreach ($list as $item) {
                // App名称
                $appName = $apps[$item['extra_app_id']] ?? '未知';
                // 判断是否是错误还是崩溃
                $eventName = $item['event_name'] == 'exception_error' ? '错误' : '崩溃';
                // 组装msg信息
                $content .= "{$appName} （{$eventName}）： {$item['exception_block_id']}，涉及 {$item['num']} 条\n";
            }
            // 把最后的换行符去掉
            $content = rtrim($content, "\n");
            // 发送请求
            HttpAgent::getInstance()->request("POST", self::HOOK_URL, [
                'json' => [
                    'msgtype' => 'text',
                    'text' => [
                        'content' => $content,
                    ],
                ],
            ]);
        }
    }

    /**
     * 处理数据，并返回结果。
     * @param array $data 数据列表。
     * @param string $field 数据列表中的字段名。
     */
    private function handleData($data, $field)
    {
        // 首先，按app分组整合数据
        $list = [];
        foreach ($data as $item) {
            $list[$item[$field] . $item['event_name']][$field] = $item[$field];
            $list[$item[$field] . $item['event_name']]['event_name'] = $item['event_name'];
            $list[$item[$field] . $item['event_name']]['list'][] = $item;
            $list[$item[$field] . $item['event_name']]['total'] = $item['num'] + ($list[$item[$field] . $item['event_name']]['total'] ?? 0);
            $list[$item[$field] . $item['event_name']]['dev'] = $item['dev_num'] + ($list[$item[$field] . $item['event_name']]['dev'] ?? 0);
        }
        // 根据二维数组的total字段进行排序
        uasort($list, function ($a, $b) {
            return $b['total'] <=> $a['total'];
        });
        // 只要前20条数据，并返回结果
        return array_slice($list, 0, 20);
    }
}
