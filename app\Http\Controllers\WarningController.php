<?php

namespace App\Http\Controllers;

use App\Components\Scope\WarningScope;
use App\Http\Validation\WarningValidation;
use App\Model\Warning;
use App\Model\WarningRule;
use App\Service\Exception\Warning\Records;
use App\Service\Exception\Warning\TimeRange;
use App\Service\Exception\Warning\Version;
use App\Service\MonitorConfigChangeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WarningController extends BaseController
{
    protected $scope;
    private $warning;

    public function __construct(Request $request, Warning $warning)
    {
        parent::__construct($request);
        $this->warning = $warning;
        $this->scope = new WarningScope($request->toArray());
    }

    /**
     * 预警新增、编辑接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2685
     * @param Request $request
     * @param null $warningId
     * @return JsonResponse
     * @throws Exception
     */
    public function store(Request $request, $warningId = null): JsonResponse
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'developer_app_id' => 'required|int',
            'name' => 'required|string|max:128',
            'warning_rule' => 'array',
            'stream_time_filter_hours' => 'max:72',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }

        try {
            $service = null;
            DB::connection('exception')->beginTransaction();
            if ($warningId) {
                // 如果 warningId 存在则是编辑
                $warning = $this->warning::query()->where('warning_id', $warningId)->first();
                if ($warning === null) {
                    return $this->response(5310);
                }
                //不需要检查是否存在相同的名称
                $check = false;
                // 创建监控服务类
                $service = new MonitorConfigChangeService($warning->toArray(), "HitBug修改预警配置");
            } else {
                // 如果 warningId 不存在则是新增 需要检查是否存在相同的名称
                //大盤通知僅支持一條配置[END]
                $check = true;
                $warning = $this->warning;
                // 创建监控服务类
                $service = new MonitorConfigChangeService(null, "HitBug创建预警配置");
            }
            //组装数据
            //状态 默认为开启状态
            $input['status'] = Warning::START;
            //平台类型 默认为0:全选
            !isset($input['os_type']) && $input['os_type'] = 0;
            //app_version 默认为全选
            if (!isset($input['app_version'])) {
                $input = array_merge($input, ['app_version' => []]);
            }
            //生效时间默认给个空数组
            if (!isset($input['effective_time'])) {
                $input = array_merge($input, ['effective_time' => []]);
            }
            //监控状态默认给个空数组
            if (!isset($input['monitor_status'])) {
                $input = array_merge($input, ['monitor_status' => []]);
            }
            //exception_keywords默认给个空数组
            if (!isset($input['exception_keywords'])) {
                $input = array_merge($input, ['exception_keywords' => []]);
            }
            //判断如果monitor_type等于2并且monitor_range的值等于7，则exception_keywords不能为空
            if ($input['monitor_type'] == Warning::ERROR_MONITOR && ($input['monitor_range'] ?? 0) == 7 && empty($input['exception_keywords'])) {
                return $this->response(5311, [], '关键词不能为空');
            }

            if ($input['stream_time_filter_hours'] == null) {
                $input['stream_time_filter_hours'] = 0;
            }
            $input['schedule_time'] = $input['schedule_time'] ?? 86400;
            $warning->store($input, true, $check);

            //存储异常预警触发条件
            if (isset($input['warning_rule'])) {
                $warningRule = new WarningRule();
                $keys = array_keys($input['warning_rule']);
                foreach ($keys as $key) {
                    $input['warning_rule'][$key]['schedule_time'] = $input['schedule_time'] ?? 3600;
                }
                $warningRule->batchStore($warning->warning_id, $input['warning_rule'], true);
            }

            DB::connection('exception')->commit();
            // 监控
            $service->monitor($warning->toArray());
            // 返回
            return $this->response(0, $warning->warning_id);
        } catch (Exception $e) {
            Log::error('保存预警接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            DB::connection('exception')->rollBack();
            $code = empty($e->getCode()) ? 1005 : intval($e->getCode());
            return $this->response($code, [], $e->getMessage());
        }
    }

    /**
     * 预警列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2686
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $input = $this->request;
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        try {
            $list = $this->warning::with('warningRule')->tap(function ($query) {
                $this->scope->getBuilder($query);
            })->orderBy('created_at', 'desc')
                ->paginate($input['per_page'] ?? 15);
            return $this->response(0, ['list' => $list->items(), 'total' => $list->total()]);
        } catch (Exception $e) {
            Log::error('预警列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 获取预警详情
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2688
     * @param $warningId
     * @return JsonResponse
     */
    public function getContent($warningId): JsonResponse
    {
        try {
            $content = $this->warning::with('warningRule')->findOrFail($warningId);
            //监控时间颗粒度
            if ($content['monitor_type'] != Warning::REPORT_MONITOR) {
                $content['schedule_time'] = $content['warningRule'][0]['schedule_time'] ?? 3600;
            }
            return $this->response(0, $content);
        } catch (Exception $e) {
            Log::error('获取预警详情接口报错-warningId:' . $warningId . ',原因:' . $e->getMessage() .
                ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 删除预警接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2687
     * @param $warningId
     * @return JsonResponse
     * @throws Exception
     */
    public function delete($warningId): JsonResponse
    {
        try {
            $warning = $this->warning::query()->find($warningId);
            // 创建监控服务类
            $service = new MonitorConfigChangeService($warning ? $warning->toArray() : null, "HitBug删除预警配置");
            DB::connection('exception')->beginTransaction();
            $this->warning::query()->where('warning_id', $warningId)->delete();
            //删除异常预警触发条件
            WarningRule::whereWarningId($warningId)->delete();
            DB::connection('exception')->commit();
            // 判断是否能找到
            if ($warning) {
                // 监控
                $service->monitor([]);
            }
            return $this->response();
        } catch (Exception $e) {
            Log::error('删除预警接口报错-warningId:' . $warningId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            DB::connection('exception')->rollBack();
            return $this->response(1005);
        }
    }

    /**
     * 修改预警状态
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2689
     * @param null $warning_id
     * @return JsonResponse
     */
    public function updateStatus($warning_id = null): JsonResponse
    {
        try {
            $warning = $this->warning::query()->find($warning_id);
            // 创建监控服务类
            $service = new MonitorConfigChangeService($warning ? $warning->toArray() : null, "HitBug修改预警状态");
            // 修改状态
            if ($warning->status === Warning::CLOSE) {
                $warning->status = Warning::START;
            } else {
                $warning->status = Warning::CLOSE;
            }
            $warning->save();
            // 判断是否能找到
            if ($warning) {
                // 监控
                $service->monitor($warning->toArray());
            }
            return $this->response();
        } catch (Exception $e) {
            Log::error('修改预警状态接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 筛选下拉列表(app版本)
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2725
     * @return JsonResponse
     */
    public function search(): JsonResponse
    {
        $input = $this->request;
        $validator = Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        try {
            $list = $this->warning::query()->tap(function ($query) {
                $this->scope->getBuilder($query);
            })->select('app_version')->groupBy('app_version')->get();
            $appVersion = [];
            foreach ($list as $key => $value) {
                if (!empty($value['app_version'])) {
                    $appVersion[] = $value['app_version'];
                }
            }
            //将数据合并成一个数组
            $array = Arr::collapse($appVersion);
            //去重
            $collection = collect($array);
            $duplicates = $collection->duplicates()->all();
            $duplicateKeys = array_keys($duplicates);
            $array = $collection->forget($duplicateKeys)->values()->all();
            //格式化
            $list = [];
            foreach ($array as $key => $value) {
                $list['app_version'][$key]['label'] = $value;
                $list['app_version'][$key]['value'] = $value;
            }
            return $this->response(0, $list);
        } catch (Exception $e) {
            Log::error('预警列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 触发预警的崩溃记录列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5810
     * @return JsonResponse
     */
    public function exceptionList(): JsonResponse
    {
        $params = WarningValidation::build()->developerAppId()->warningId()
            ->warningRecordId()->page()->perPage()
            ->validate();
        try {
            $result = (new Records($params))->warningRecords();
            return $this->response(0, $result);
        } catch (Exception $e) {
            Log::error('获取触发预警的崩溃记录列表接口失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 获取指定时间维度内触发的预警时间段
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5805
     * @return JsonResponse
     */
    public function warningTimeRange(): JsonResponse
    {
        $params = WarningValidation::build()->developerAppId()->warningId()->notWarningRecordId()->validate();
        try {
            [$list, $type] = (new TimeRange($params))->warningTimeRange();
            return $this->response(0, compact('list', 'type'));
        } catch (Exception $e) {
            Log::error('获取触发预警的崩溃记录列表接口失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 获取版本列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=6242
     * @return JsonResponse
     */
    public function version(): JsonResponse
    {
        $params = WarningValidation::build()->developerAppId()->validate();
        try {
            $list = (new Version($params))->getList();
            return $this->response(0, $list);
        } catch (Exception $e) {
            Log::error('获取预警版本列表' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
