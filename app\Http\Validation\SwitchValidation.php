<?php

/**
 * 开关校验类
 * @desc 开关校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

use App\Model\BaseModel;
use App\Model\SwitchConfig;
use Illuminate\Database\Query\Builder;
use Illuminate\Validation\Rule;

/**
 * @method static SwitchValidation build()
 */
class SwitchValidation extends BaseValidation
{
    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'os_type.unique' => '该系统平台配置已存在',
        ];
    }

    public function developerAppId(): SwitchValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    public function switchId(): SwitchValidation
    {
        $this->rules['switch_id'] = 'required|integer|min:0';
        return $this;
    }

    public function status(): SwitchValidation
    {
        $this->rules['status'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::STATUS_ON, SwitchConfig::STATUS_OFF]),
        ];
        return $this;
    }

    public function limit(): SwitchValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:100';
        return $this;
    }

    public function osType(): SwitchValidation
    {
        $this->rules['os_type'] = [
            'required',
            'integer',
            Rule::in([BaseModel::ANDROID, BaseModel::IOS, BaseModel::PC, BaseModel::MINI, BaseModel::HARMONY]),
            Rule::unique('exception.switch_config')->where(function (Builder $query) {
                $request = request();
                return $query->when($request->has('developer_app_id'), function (Builder $query) use ($request) {
                    return $query->where('developer_app_id', $request->input('developer_app_id'));
                })
                    ->when($request->has('switch_id'), function (Builder $query) use ($request) {
                        return $query->where('id', '<>', $request->input('switch_id'));
                    });
            }),
        ];
        return $this;
    }

    public function timeInterval(): SwitchValidation
    {
        $this->rules['time_interval'] = 'required|integer|min:1';
        return $this;
    }

    public function cacheSize(): SwitchValidation
    {
        $this->rules['cache_size'] = 'required|integer|min:1';
        return $this;
    }

    public function isAllowErrorReport(): SwitchValidation
    {
        $this->rules['is_allow_error_report'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::ALLOW_ERROR_REPORT, SwitchConfig::NOT_ALLOW_ERROR_REPORT]),
        ];
        return $this;
    }

    public function isAllowCrashReport(): SwitchValidation
    {
        $this->rules['is_allow_crash_report'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::ALLOW_CRASH_REPORT, SwitchConfig::NOT_ALLOW_CRASH_REPORT]),
        ];
        return $this;
    }

    public function isAllowAnrReport(): SwitchValidation
    {
        $this->rules['is_allow_anr_report'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::ALLOW_ANR_REPORT, SwitchConfig::NOT_ALLOW_ANR_REPORT]),
        ];
        return $this;
    }

    /**
     * 是否允许截图
     *
     * @return SwitchValidation
     */
    public function isAllowScreenshot(): SwitchValidation
    {
        $this->rules['is_allow_screenshot'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::ALLOW_SCREENSHOT, SwitchConfig::NOT_ALLOW_SCREENSHOT]),
        ];
        return $this;
    }

    /**
     * 截图数量
     *
     * @return SwitchValidation
     */
    public function screenshotNum(): SwitchValidation
    {
        $this->rules['screenshot_num'] = [
            'required_if:is_allow_screenshot,1',
            function ($attribute, $value, $fail) {
                if (request('is_allow_screenshot') == 1 && (!is_numeric($value) || $value < 1)) {
                    $fail('当允许截图时，截图数量必须是大于等于1的整数。');
                }
            },
        ];
        return $this;
    }

    /**
     * 截图间隔
     *
     * @return SwitchValidation
     */
    public function screenshotCd(): SwitchValidation
    {
        $this->rules['screenshot_cd'] = [
            'required_if:is_allow_screenshot,1',
            function ($attribute, $value, $fail) {
                if (request('is_allow_screenshot') == 1 && (!is_numeric($value) || $value < 1)) {
                    $fail('当允许截图时，截图间隔必须是大于等于1的整数。');
                }
            },
        ];
        return $this;
    }

    /**
     * 是否允许oom上报
     *
     * @return SwitchValidation
     */
    public function isAllowOomReport(): SwitchValidation
    {
        $this->rules['is_allow_oom_report'] = [
            'required',
            'integer',
            Rule::in([SwitchConfig::ALLOW_OOM_REPORT, SwitchConfig::NOT_ALLOW_OOM_REPORT]),
        ];
        return $this;
    }
}
