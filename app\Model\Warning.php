<?php

namespace App\Model;

use App\Components\ApiResponse\ErrorHelper;

/**
 * App\Model\Warning
 *
 * @property int $warning_id 预警id
 * @property int $developer_app_id 研发效能APP项目id
 * @property bool $os_type 平台类型;1为安卓,2为iOS
 * @property bool $status 状态
 * @property string $name 预警计划名称
 * @property array $receiving_group 接收群地址
 * @property array $app_version app版本
 * @property array $notification_method 通知方式
 * @property array $receiving_person 接收人员
 * @property string|null $last_warning_time 最近预警时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\WarningRule[] $warningRule
 * @property-read int|null $warning_rule_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereAppVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereDeveloperAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereLastWarningTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereNotificationMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereOsType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereReceivingGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereReceivingPerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Warning whereWarningId($value)
 * @mixin \Eloquent
 */
class Warning extends BaseModel
{
    use ModelTrait;

    public const START = 1;
    public const CLOSE = 0;
    const NOTICE_WX = 1;
    const REPORT_MONITOR = 1;
    const ERROR_MONITOR = 2;
    /**
     * 异常类型，全部
     *
     * @var string
     */
    const EXCEPTION_TYPE_ALL = 'all';
    /**
     * 异常类型，崩溃
     *
     * @var string
     */
    const EXCEPTION_TYPE_CRASH = 'crash';
    /**
     * 异常类型，错误
     *
     * @var string
     */
    const EXCEPTION_TYPE_ERROR = 'error';
    /**
     * 异常类型
     */
    const EXCEPTION_TYPE = [
        1 => self::EXCEPTION_TYPE_CRASH,
        2 => self::EXCEPTION_TYPE_ERROR,
    ];
    /**
     * 异常类型文本
     */
    const EXCEPTION_TYPE_TEXT = [
        1 => '崩溃',
        2 => '错误',
    ];
    /**
     * 条件模式，任意
     *
     * @var int
     */
    const CONDITION_MODE_ANY = 1;
    /**
     * 条件模式，全部
     *
     * @var int
     */
    const CONDITION_MODE_ALL = 2;

    //状态 是否开启
    public $validateRule = [
        'developer_app_id' => 'required|integer',
        'os_type' => 'required|integer',
        'monitor_type' => 'required|integer',
        'status' => 'required|integer',
        'name' => 'required|string',
        'receiving_group' => 'array',
        'app_version' => 'array',
        'exception_type' => 'array',
        'effective_time' => 'array',
        'monitor_range' => 'integer|in:0,1,2,3,4,5,6,7',
        'monitor_status' => 'array',
        'condition_mode' => 'integer|in:1,2',
        'is_create_tapd' => 'integer|in:0,1',
        'notification_method' => 'required|array',
        'receiving_phone' => 'array',
        'receiving_person' => 'array',
        'receiving_group.*.url' => 'required_with:receiving_group.*|url',
        'receiving_group.*.is_mentioned' => 'required_with:receiving_group.*|int',
        'exception_keywords' => 'array',
    ];
    public $uniqueKey = ['developer_app_id', 'name'];

    //通知方式 1为企业微信
    protected $table = 'warning';

    //监控类型 1：大盘监控 2：问题监控
    protected $primaryKey = 'warning_id';
    protected $fillable = [
        'developer_app_id', 'os_type', 'status', 'name', 'receiving_group', 'app_version', 'notification_method',
        'receiving_person', 'last_warning_time', 'monitor_type', 'receiving_phone', 'exception_type', 'effective_time', 'monitor_range', 'monitor_status', 'condition_mode', 'is_create_tapd', 'exception_keywords', 'stream_time_filter', 'stream_time_filter_hours', 'schedule_time',
    ];
    protected $casts = [
        'app_version' => 'array',
        'notification_method' => 'array',
        'receiving_person' => 'array',
        'receiving_phone' => 'array',
        'receiving_group' => 'array',
        'exception_type' => 'array',
        'effective_time' => 'array',
        'monitor_status' => 'array',
        'exception_keywords' => 'array',
    ];

    /**
     * 关联预警规则
     */
    public function warningRule()
    {
        return $this->hasMany(WarningRule::class, 'warning_id', 'warning_id');
    }

    /**
     * 获取平台类型
     * @return bool|string
     */
    public function getOsType()
    {
        return self::OS_TYPE[$this->os_type] ?? '全部平台';
    }

    /**
     * 获取app版本
     * @return string
     */
    public function getAppVersion(): string
    {
        if (empty($this->app_version)) {
            return '全部版本';
        }
        return implode('、', $this->app_version);
    }

    /**
     * 获取异常类型
     *
     * @return string
     */
    public function getExceptionType(): string
    {
        if (empty($this->exception_type) || count($this->exception_type) == 2) {
            return self::EXCEPTION_TYPE_ALL;
        }
        return self::EXCEPTION_TYPE[$this->exception_type[0]] ?? self::EXCEPTION_TYPE_ALL;
    }

    /**
     * 获取监控范围
     *
     * @return string
     */
    public function getMonitorRange(): string
    {
        switch ($this->monitor_range) {
            case 1:
                return '最近一小时TOP 5 问题';
            case 2:
                return '最近一小时TOP 10 问题';
            case 3:
                return '最近一小时TOP 20 问题';
            case 4:
                return '最近一小时TOP 30 问题';
            case 5:
                return '最近一小时TOP 50 问题';
            case 6:
                return '最近一小时TOP 100 问题';
            case 7:
                return '指定问题关键词';
            default:
                return '全部';
        }
    }

    /**
     * 检查是否已存在相同数据
     * model需定义uniqueKey字段
     * @example public $uniqueKey = ['username'];代表用户名唯一
     * @param array $params
     * @throws InfoException
     */
    private function checkExists(array $params)
    {
        //组装唯一值判断条件
        $unique = [];
        foreach ($this->uniqueKey as $value) {
            $unique[$value] = $params[$value] ?? '';
        }

        (new static())->where($unique)->exists() && ErrorHelper::callException(1012, '已存在相同标题的预警');
    }
}
