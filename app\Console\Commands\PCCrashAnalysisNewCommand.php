<?php

/**
 * PC崩溃解析脚本(新)
 * @desc PC崩溃解析脚本(新)
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/01/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Service\AnalysisService;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class PCCrashAnalysisNewCommand extends Command
{
    /**
     * 执行间隔时间
     *
     * @var int
     */
    const TIME = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pc:crash:analysis:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PC崩溃解析脚本(新)';

    /**
     *  PC崩溃解析脚本
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        Log::info('PC崩溃解析脚本开始执行(新)');

        $redis = Redis::connection('api');
        $time = time() + (self::TIME * 60);
        $result = '';
        while (time() < $time) {
            try {
                $result = $redis->lIndex('hitbug_pc_crash_parse_list_new', -1);
                // 判断是否为空
                if (empty($result)) {
                    sleep(10); // 休眠10秒
                    continue;
                }
                // 解析数据
                $data = json_decode($result, true);
                // 获取解析的数据
                $data = $this->getReportDataJson($data);
                // 解析数据
                $this->parseData($data);
                // 移除数据
                $redis->rPop('hitbug_pc_crash_parse_list_new');
            } catch (\Exception $e) {
                if ($e->getCode() == 10010) {
                    // 移除数据
                    $redis->rPop('hitbug_pc_crash_parse_list_new');
                }
                Log::error('PC崩溃解析脚本(新)执行失败，错误信息：' . $e->getMessage() . '，堆栈：' . $e->getTraceAsString() . "result：" . $result);
                sleep(10); // 每次执行失败后等待10秒
            }
        }
        Log::info('PC崩溃解析脚本(新)执行结束');
    }

    /**
     * 获取报告数据
     *
     * @return array
     */
    private function getReportDataJson(array $data)
    {
        // 获取 data 里面 exception_file 字段
        $exceptionFile = $data['exception_file'];
        // f341238fbbf515a6e7d85acb396e66a7/1737683615_1/SYKit.log,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/Player.log,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/upload_file_data,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/0c1908b3-8c34-47c8-bda2-d0dbacf9ce7f.dmp，在这个字符串中找到dmp文件名
        $exceptionFiles = explode(',', $exceptionFile);
        // 定义文件名变量
        $fileName = '';
        // 遍历数组
        foreach ($exceptionFiles as $item) {
            // 判断是否包含dmp文件
            if (stripos($item, '.dmp') === false) {
                continue;
            }
            // 获取文件名
            $fileName = $item;
            break;
        }
        // 判断是否找到文件名
        if (empty($fileName)) {
            throw new Exception('没有上传dmp文件', 10010);
        }

        $response = HttpAgent::getInstance()->request('GET', env('COS_BASE_URL') . str_replace('.dmp', '_sym.json', $fileName));

        $message = json_decode($response['message'], true);

        if (empty($message)) {
            // 调用接口进行解析
            HttpAgent::getInstance()->request('POST', 'https://crashpad.shiyue.com/dump/decode', [
                'json' => [
                    'dump_file_name' => $fileName,
                ],
            ]);
            // 判断是否存在
            if (!HttpAgent::getInstance()->requestSuccess('GET', env('COS_BASE_URL') . $fileName)) {
                throw new Exception('dmp文件在cos找不到', 10010);
            }
            //判断当前数据的时间是否超过30分钟，如果是，则丢弃
            if ($data['stream_time'] < (time() - 1800)) {
                throw new Exception('还没生成解析数据', 10010);
            }
            throw new Exception('还没生成解析数据');
        }
        // 判断是否存在crashing_thread
        if (empty($message['crashing_thread'])) {
            throw new Exception('解析数据crashing_thread为空', 10010);
        }
        // 赋值
        $data['origin_stacks_json'] = $message['crashing_thread']['frames'];
        $data['explain_desc'] = (new AnalysisService(null))->getThreadStackText($message['crashing_thread']['frames'][0]);
        $data['search_content'] = $data['explain_desc'];
        $data['content'] = $this->prettyJsonEncode($message['threads']);
        $data['subject_name'] = $message['crash_info']['type'];
        $data['exception_file2'] = $data['exception_file'];
        $data['exception_file'] = $fileName;
        // 处理uuid_info
        $data['game_info_json'] = [];
        $uuidInfo = array_column($message['crashing_thread']['frames'], 'module');
        $modules = $message['modules'] ?? [];
        foreach ($modules as $value) {
            if (in_array($value['filename'], $uuidInfo)) {
                $data['game_info_json'][] = $value;
            }
        }
        return $data;
    }

    /**
     * 解析数据
     *
     * @param array $data
     * @return void
     */
    private function parseData(array $data)
    {
        $data['exception_block_id'] = $this->createExceptionBlockId($data, 1);
        // 对 data 所有数据进行处理
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->prettyJsonEncode($value);
            }
        }
        // 保存数据
        $this->saveData($data);
    }

    /**
     * 创建问题ID
     *
     * @param array $data
     * @param $type
     * @return string
     */
    private function createExceptionBlockId(array $data, $type): string
    {
        $explain = $data['explain_desc'];
        $length = stripos($data['explain_desc'], '- stack1');
        if ($length) {
            $explain = substr($data['explain_desc'], 0, $length);
        }

        return strtoupper(md5($data['extra_app_id'] . $type . $data['os_type'] . $data['type'] . $data['subject_name'] . $explain));
    }

    /**
     * 创建异常合并ID
     *
     * @param array $data
     * @return string
     */
    private function createExceptionMergeId(array $data): string
    {
        ksort($data);
        return strtoupper(md5(json_encode($data)));
    }

    /**
     * 生成json格式
     *
     * @param $object
     * @param int|null $flag
     * @return string
     */
    private function prettyJsonEncode($object, ?int $flag = JSON_PRETTY_PRINT): string
    {
        //JSON_PRETTY_PRINT//易讀格式（即：自動換行）
        $flagCounter = JSON_UNESCAPED_SLASHES/*不轉義反斜杠*/ | JSON_UNESCAPED_UNICODE/*unicode轉至中文*/;
        if (!$flag) {
            $flagCounter |= $flag;
        }
        return json_encode($object, $flagCounter);
    }

    /**
     * 保存数据
     *
     * @param array $data
     * @return void
     */
    private function saveData(array $data)
    {
        //公用参数
        $common = [
            'game_id' => intval($data['project_id'] ?? ''), //项目id
            'scene_id' => 0, //場景ID
            'referrer_scene_id' => 0, //[進入頁面前]場景標識
            'system_source' => 3, //系統來源，值：1瀏覽器，2公眾號，3客戶端，4服務端
            'project_id' => 9, //項目ID，值：1社區，2積分商城，3遊戲客戶端，4用戶體系，5活動通用，6WEB支付，9效能系統
            'account_id' => (string)($data['account_id'] ?? 0),
        ];
        $data = array_merge($common, $data);
        $data['exception_merge_id'] = $this->createExceptionMergeId($data);
        //字段
        $columns = [
            'extra_app_id',
            'event_name',
            'exception_block_id',
            'exception_merge_id',
            'stream_date',
            'stream_time',
            'timezone',
            'event_time',
            'dev_create_time',
            'server_dev_str',
            'type',
            'os_type',
            'version',
            'os_version',
            'app_version',
            'is_success',
            'operate_status',
            'release_store',
            'manufacturer',
            'device_model',
            'current_page_title',
            'app_id',
            'game_id',
            'scene_id',
            'referrer_scene_id',
            'project_id',
            'system_source',
            'account_id',
            'role_id',
            'role_name',
            'ip',
            'subject_name',
            'sdk_package_name',
            'extra',
            'explain_desc',
            'content',
            'search_content',
            'page_info_json',
            'game_info_json',
            'basic_info_json',
            'memory_info_json',
            'console_info_json',
            'origin_stacks_json',
            'duration',
            'is_emulator',
            'inner_version',
            'exception_image',
            'exception_file',
            'exception_file2',
        ];
        //写入csv
        $path = (new WriteCsvFileService("app/exception-stream-all/" . Str::random() . ".csv", $columns, [$data]))->write();
        //同步到starRocks
        (new SyncFileToStarRocksService($path, $columns, 'exception_stream_all'))->sync();
    }
}
