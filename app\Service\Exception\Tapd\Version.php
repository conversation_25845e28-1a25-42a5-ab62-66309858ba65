<?php

/**
 * Tapd版本服务基础类
 * @desc Tapd版本服务基础类
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/04
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Tapd;

use App\Service\TapdService;

class Version extends Base
{
    /**
     * 项目ID
     *
     * @var string
     */
    private $itemId;

    /**
     * 初始化
     *
     * @param $itemId
     */
    public function __construct($itemId)
    {
        $this->itemId = $itemId;
    }

    /**
     * 获取版本信息
     *
     * @return array
     */
    public function get(): array
    {
        $data = TapdService::get(TapdService::TAPD_GET_VERSIONS_URL, ['workspace_id' => $this->itemId]);
        //没有数据
        if (empty($data['data'])) return [];
        //格式化数据
        return $this->format($data);
    }

    /**
     * 格式化
     *
     * @param array $data
     * @return array
     */
    private function format(array $data): array
    {
        $versions = [];
        foreach ($data['data'] as $item) {
            $version = ['version_id' => $item['Version']['id'] ?? 0, 'version_name' => $item['Version']['name'] ?? ''];
            $versions[] = $version;
        }
        return $versions;
    }
}
