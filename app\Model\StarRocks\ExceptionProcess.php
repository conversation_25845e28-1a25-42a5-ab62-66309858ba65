<?php

/**
 * 对应starRocks数仓中exception_process表
 * @desc 对应starRocks数仓中exception_process表
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/03/25
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\StarRocks;

class ExceptionProcess extends BaseModel
{
    public const TABLE_NAME = 'exception_process';

    protected $table = self::TABLE_NAME;

    const STATE_CHANGE = 1;  // 状态变更
    const ADD_COMMENT = 0; // 添加评论
}
