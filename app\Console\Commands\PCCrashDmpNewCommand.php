<?php

/**
 * PC崩溃dmp解析脚本(新)
 * @desc PC崩溃dmp解析脚本(新)
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/01/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class PCCrashDmpNewCommand extends Command
{
    /**
     * 执行间隔时间
     *
     * @var int
     */
    const TIME = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pc:crash:dmp:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PC崩溃dmp解析脚本(新)';

    /**
     *  PC崩溃dmp解析脚本
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        Log::info('PC崩溃dmp解析脚本开始执行(新)');
        // 获取redis连接
        $redis = Redis::connection('api');
        // 获取要执行的时长
        $time = time() + (self::TIME * 60);
        // 循环执行
        while (time() < $time) {
            try {
                $result = $redis->lIndex('hitbug_pc_crash_list_new', -1);
                // 判断是否为空
                if (empty($result)) {
                    sleep(10); // 休眠10秒
                    continue;
                }
                // 解析数据
                $data = json_decode($result, true);
                // 解析dmp文件
                $isSuccess = $this->parseDmp($data);
                // 判断是否解析成功
                if ($isSuccess) {
                    // 添加到解析队列
                    $redis->lPush('hitbug_pc_crash_parse_list_new', $result);
                    // 移除数据
                    $redis->rPop('hitbug_pc_crash_list_new');
                }
            } catch (\Exception $e) {
                Log::error('PC崩溃dmp解析脚本(新)执行失败，错误信息：：' . $e->getMessage() . '，堆栈：' . $e->getTraceAsString());
                sleep(10); // 每次执行失败后等待10秒
            }
        }
    }

    /**
     * 解析dmp文件
     *
     * @return void
     * @throws \Exception
     * @var array $data
     */
    private function parseDmp($data)
    {
        // 获取 data 里面 exception_file 字段
        $exceptionFile = $data['exception_file'];
        // f341238fbbf515a6e7d85acb396e66a7/1737683615_1/SYKit.log,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/Player.log,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/upload_file_data,f341238fbbf515a6e7d85acb396e66a7/1737683615_1/0c1908b3-8c34-47c8-bda2-d0dbacf9ce7f.dmp，在这个字符串中找到dmp文件名
        $exceptionFiles = explode(',', $exceptionFile);
        // 定义文件名变量
        $fileName = '';
        // 遍历数组
        foreach ($exceptionFiles as $item) {
            // 判断是否包含dmp文件
            if (stripos($item, '.dmp') === false) {
                continue;
            }
            // 获取文件名
            $fileName = $item;
            break;
        }
        // 判断是否找到文件名
        if (empty($fileName)) {
            Redis::connection('api')->rPop('hitbug_pc_crash_list_new');
            // throw new \Exception('未找到dmp文件名，数据：' . json_encode($data));
            return false;
        }
        // 打印日志
        Log::info('开始解析dmp文件(新)，文件名：' . $fileName);
        // 调用接口进行解析
        $response = HttpAgent::getInstance()->request('POST', 'https://crashpad.shiyue.com/dump/decode', [
            'json' => [
                'dump_file_name' => $fileName,
            ],
        ]);
        if (!$response['success']) {
            throw new \Exception('请求接口失败：' . $response['message']);
        }
        return true;
    }
}
