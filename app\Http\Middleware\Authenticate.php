<?php

namespace App\Http\Middleware;


use App\User;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Authenticate
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (env('APP_ENV') == 'local') Auth::setUser(User::query()->find(2));
        if (!Auth::guard($guard)->check() && !(in_array($request->ip(), ['**************', '*************', '*************', '*************', '*************']) && $request->getPathInfo() == '/common/symbol/common/store')) {
            Log::info("账号未登录，ip：{$request->ip()}，访问的path：{$request->getPathInfo()}");
            return response()->json(['message' => '账号未登录', 'code' => 1002, 'data' => []], 401);
        }
        //执行逻辑
        return $next($request);
    }
}
