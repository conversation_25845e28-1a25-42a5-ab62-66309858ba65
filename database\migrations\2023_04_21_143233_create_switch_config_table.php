<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('switch_config', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台，1:android、2:ios、3:pc');
            $table->unsignedInteger('time_interval')->default(0)->comment('时间间隔（单位秒）');
            $table->unsignedInteger('cache_size')->default(0)->comment('缓存大小（单位KB）');
            $table->unsignedTinyInteger('is_allow_error_report')->default(0)->comment('是否允许错误上报，1:允许、0:不允许');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态，1:开启、0:关闭');
            $table->timestamps();

            $table->index('developer_app_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `switch_config` comment '开关配置表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('switch_config');
    }
}
