<?php

/**
 * 同步TapdBug数据脚本
 * @desc 同步TapdBug数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/28
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionTapdBug;
use App\Model\TapdBug;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncTapdBugCommand extends Command
{
    /**
     * 同步数据条数
     *
     * @var int
     */
    const LIMIT = 1000;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:tapd:bug {id? : 要开始同步的ID} ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步TapdBug数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            $page = 0;
            // 获取最大ID
            $maxId = $this->argument('id');
            //打印日志
            Log::info("执行同步TapdBug数据脚本开始，最大ID为：{$maxId}");
            // 获取数据
            $builder = TapdBug::query()->orderBy('updated_at', 'asc')->limit(self::LIMIT);
            // 判断$maxId是否大于0
            if (!empty($maxId) && $maxId >= 0) {
                $builder->where('id', '>', $maxId);
            } else {
                $builder->where('updated_at', '>=', Carbon::now()->subMinutes(5)->toDateTimeString());
            }
            //字段
            $columns = [
                'id',
                'developer_app_id',
                'exception_block_id',
                'tapd_account_id',
                'type',
                'bind_status',
                'status',
                'created_at',
                'updated_at',
            ];
            // 循环获取数据
            while (true) {
                // 获取数据
                $newData = $builder->offset($page * self::LIMIT)->get($columns)->toArray();
                // 判断是否有数据
                if (empty($newData)) {
                    break;
                }
                Log::info("执行同步TapdBug数据脚本执行中，当前页为：{$page}");
                // 分页
                $page++;
                //写入csv
                $path = (new WriteCsvFileService("app/exception-tapd-bug/" . Str::random() . ".csv", $columns, $newData))->write();
                //同步到starRocks
                (new SyncFileToStarRocksService($path, $columns, ExceptionTapdBug::TABLE_NAME))->sync();
            }
            //打印日志
            Log::info("执行同步TapdBug数据脚本完成");
        } catch (\Exception $e) {
            Log::error("执行同步TapdBug数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
