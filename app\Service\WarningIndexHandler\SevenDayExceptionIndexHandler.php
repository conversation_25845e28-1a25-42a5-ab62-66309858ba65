<?php

/**
 * 7天（崩溃/错误）维度预警
 * @desc 7天（崩溃/错误）维度预警
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/23
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\WarningIndexHandler;

use App\Model\Warning;
use Carbon\Carbon;

class SevenDayExceptionIndexHandler extends ExceptionIndexHandler
{
    /**
     * 初始化
     */
    public function __construct(int $exceptionType, Warning $warning)
    {
        parent::__construct($exceptionType, $warning);
        //获取当前的时间
        $now = Carbon::now();
        //$this->startTime减7天的时间
        $this->startTime = (clone $now)->subDays(7)->startOfHour()->timestamp;
        //$this->endTime减一天的时间
        $this->endTime = (clone $now)->subDay()->endOfHour()->timestamp;
    }
}
