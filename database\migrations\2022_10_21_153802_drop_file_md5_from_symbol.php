<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropFileMd5FromSymbol extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->dropColumn('file_md5');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->string('file_md5', 128)->comment('md5');
        });
    }
}
