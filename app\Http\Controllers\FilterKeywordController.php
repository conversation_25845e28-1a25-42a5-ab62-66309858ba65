<?php

/**
 * 过滤关键词控制器
 * @desc 过滤关键词控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/10/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Http\Validation\FilterKeywordValidation;
use App\Jobs\ExceptionKeywordJob;
use App\Model\StarRocks\FilterKeyword;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class FilterKeywordController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    public const PER_PAGE = 10;

    /**
     * 关键词列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=6703
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        //请求参数校验
        $params = FilterKeywordValidation::build()
            ->developerAppId()->page()->perPage()->validate();
        try {
            $page = ($params['page'] ?? 1) - 1;
            $perPage = $params['per_page'] ?? self::PER_PAGE;
            //获取总记录数
            $total = FilterKeyword::query()
                ->selectRaw("count(*) as num")
                ->where('developer_app_id', $params['developer_app_id'])
                ->firstFromSR();
            //获取数据
            $list = FilterKeyword::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->latest()
                ->offset($page * $perPage)
                ->limit($perPage)
                ->getFromSR();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, "total" => (int)($total['num'] ?? 0)]);
        } catch (Exception $e) {
            \Log::error('获取过滤关键词列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加关键词
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=6705
     * @return JsonResponse
     */
    public function add(): JsonResponse
    {
        //请求参数校验
        $params = FilterKeywordValidation::build()
            ->developerAppId()->keyword()->isStat()->validate();
        try {
            //判断关键词是否存在
            $res = FilterKeyword::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->where('keyword', $params['keyword'])
                ->firstFromSR();
            //如果不为空，返回添加失败
            if (!empty($res)) {
                return $this->response(StatusCode::C_PARAM_ERROR, [], '关键词已存在');
            }
            //添加关键词
            FilterKeyword::query()->createFromSR([
                'uuid' => Str::uuid(),
                'developer_app_id' => $params['developer_app_id'],
                'keyword' => $params['keyword'],
                'is_stat' => $params['is_stat'] ?? 1,
            ]);
            //执行队列
            $now = Carbon::now();
            dispatch(new ExceptionKeywordJob($params['developer_app_id'], $now->startOfDay()->timestamp, $now->endOfDay()->timestamp, [$params['keyword']]))->onQueue('exception_keyword_queue');
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('添加过滤关键词接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 修改关键词
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=6705
     * @return JsonResponse
     */
    public function update(): JsonResponse
    {
        //请求参数校验
        $params = FilterKeywordValidation::build()
            ->uuid()->keyword()->isStat()->validate();
        try {
            //获取关键词
            $res = FilterKeyword::query()
                ->where('uuid', $params['uuid'])
                ->firstFromSR();
            //如果不为空，返回添加失败
            if (empty($res)) {
                return $this->response(StatusCode::C_PARAM_ERROR, [], '关键词不存在');
            }
            //修改关键词
            $res['keyword'] = $params['keyword'];
            $res['is_stat'] = $params['is_stat'] ?? $res['is_stat'];
            FilterKeyword::query()->createFromSR($res);
            //执行队列
            $now = Carbon::now();
            dispatch(new ExceptionKeywordJob($res['developer_app_id'], $now->startOfDay()->timestamp, $now->endOfDay()->timestamp, [$res['keyword']]))->onQueue('exception_keyword_queue');
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('修改过滤关键词接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除开关键词
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=6706
     * @return JsonResponse
     */
    public function del(): JsonResponse
    {
        //请求参数校验
        $params = FilterKeywordValidation::build()->uuid()->validate();
        try {
            //删除关键词
            FilterKeyword::query()->where('uuid', $params['uuid'])->deleteFromSR();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('删除过滤关键词接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
