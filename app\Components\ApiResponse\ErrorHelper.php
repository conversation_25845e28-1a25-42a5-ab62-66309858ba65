<?php
/**
 * ErrorHelper.php
 *
 * User: Dican
 * Date: 2022/8/16
 * Email: <<EMAIL>>
 */

namespace App\Components\ApiResponse;


use App\Exceptions\InfoException;

/**
 * 统一异常处理类
 * Class ErrorHelper
 * @package App\Components\ApiResponse
 */
class ErrorHelper
{
    /**
     * 统一抛出返回
     * @param int $code
     * @param null|string $message
     * @throws InfoException
     */
    public static function callException(int $code, $message = null)
    {
        throw new InfoException($message, $code);
    }

    /**
     * 表单字段验证，验证失败直接抛出返回
     * @param array $data 待验证数据
     * @param array $rules 验证规则
     * @return array
     * @throws InfoException
     */
    public static function validate(array $data, array $rules): array
    {
        $validator = \Validator::make($data, $rules);
        if ($validator->fails()) {
            static::callException(1000);
        }
        return $data;
    }
}
