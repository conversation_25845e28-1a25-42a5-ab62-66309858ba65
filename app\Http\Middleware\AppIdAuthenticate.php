<?php
/**
 * 游戏应用权限校验
 * @desc 游戏应用权限校验
 * <AUTHOR> <EMAIL>
 * @date 2024/4/23
 * @todo 这里是后续需要跟进的功能说明
 */
namespace App\Http\Middleware;

use App\Components\Helper\Curl;
use Closure;

class AppIdAuthenticate
{
    /**
     * appId权限校验
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next, $appId)
    {
        if (empty($appId)) {
            return response()->json(['message' => '效能后台应用id为空', 'code' => 1002, 'data' => []]);
        }

        // 校验权限（发送校验请求）
        $url = "https://test-developer-manager.shiyue.com/authV2/checkAppId";
        if (env('APP_ENV') == 'production') {
            $url = "https://developer-manager.shiyue.com/authV2/checkAppId";
        }

        $cookie = $request->header('Cookie');
        $response = Curl::getWithCookies($url, ["developer_app_id" => $appId], $cookie);
        $ret = json_decode($response, true);
        if ($ret["code"] != 0) {
            return response()->json(['message' => '获取权限失败', 'code' => 1006, 'data' => []], 401);
        }

        return $next($request);
    }
}
