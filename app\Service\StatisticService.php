<?php
/**
 * StatisticService.php
 * <AUTHOR>
 * @date 2022/9/20
 * @email <EMAIL>
 */

namespace App\Service;

use App\Components\Helper\DataHelper;
use App\Components\Scope\CrashScope;
use App\Components\Scope\Scope;
use App\Components\Scope\StartScope;
use App\Model\ClickHouse\Start;
use App\Model\ClickHouse\UserLogDataAll;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;
use Carbon\Carbon;
use Illuminate\Http\Request;

class StatisticService
{
    /**
     * @var Scope
     */
    public $scope;
    /**
     * @var Request
     */
    public $request;
    public $query;
    public $class;

    public function __construct(Request $request, $scope = null)
    {
        $this->request = $request;
        empty($scope) && $scope = new Scope();
        $this->scope = $scope;
        $this->class = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type);
        $this->query = $this->class::query();
    }

    /**
     * 获取崩溃汇总统计
     * 崩溃次数、崩溃人数、启动次数、启动人数、崩溃率、崩溃用户比率
     * 崩溃次数环比、崩溃人数环比、崩溃率环比、崩溃用户比率环比
     * 最新上报时间、最新发生时间
     * @return array
     */
    public function getCrashSummaryStatistic(): array
    {
        //获取最新上报时间、最新发生时间
        $lastReportTime = $this->getLastReportTime(clone ($this->query));
        //获取崩溃次数、崩溃人数、启动次数、启动人数、崩溃率、崩溃用户比率
        $summaryStatistic = $this->getCrashReportSummary();

        //获取对比崩溃次数、崩溃人数、启动次数、启动人数、崩溃率、崩溃用户比率
        $lastSummaryStatistic = $this->getCrashReportSummary(true);

        //计算环比数 崩溃次数环比、崩溃人数环比、崩溃率环比、崩溃用户比率环比、启动用户数环比
        $summaryStatistic['crash_count_proportion'] = DataHelper::linkRatio($summaryStatistic['crash_count'], $lastSummaryStatistic['crash_count']);
        $summaryStatistic['crash_user_count_proportion'] = DataHelper::linkRatio($summaryStatistic['crash_user_count'], $lastSummaryStatistic['crash_user_count']);
        $summaryStatistic['crash_percent_proportion'] = DataHelper::linkRatio($summaryStatistic['crash_percent'], $lastSummaryStatistic['crash_percent']);
        $summaryStatistic['crash_user_percent_proportion'] = DataHelper::linkRatio($summaryStatistic['crash_user_percent'], $lastSummaryStatistic['crash_user_percent']);
        $summaryStatistic['start_user_proportion'] = DataHelper::linkRatio($summaryStatistic['start_user_count'], $lastSummaryStatistic['start_user_count']);
        return $summaryStatistic + $lastReportTime;
    }

    /**
     * 获取最新上报时间、最新发生时间
     * @param object $object 查询事件模型
     * @return array
     */
    public function getLastReportTime(object $object): array
    {
        $lastTime = $this->getFirstSelect($object, ['event_time', 'dev_create_time']);
        return ['last_happen_time' => $lastTime['dev_create_time'] ?? null, 'last_event_time' => $lastTime['event_time'] ?? null];
    }

    /**
     * 获取首个查询结果 默认按事件时间倒序排序
     * @param object $object
     * @param array $select 查询
     * @param string $order 排序
     * @return mixed
     */
    public function getFirstSelect(object $object, array $select, string $order = 'event_time desc')
    {
        return $object->where('extra_app_id', $this->scope->developerAppId)
            ->tap(function ($query) {
                $this->scope->getBuilder($query);
            })
            ->orderByRaw($order)
            ->select($select)
            ->FirstFromCk();
    }

    /**
     * 获取崩溃上报汇总统计
     * 崩溃次数、崩溃人数、启动次数、启动人数、崩溃率、崩溃用户比率
     * @param bool $isLast 是否查询上一次的
     * @return array
     */
    public function getCrashReportSummary($isLast = false): array
    {
        //获取异常崩溃上报次数、人数
        $this->scope = new CrashScope($this->request->toArray());
        if ($this->request->toArray()['type'] == 2/*錯誤時*/) {
            $this->scope->useDuration = 0; //reset//或：#錯誤類型#時前端禁止傳入
        }
        $this->query = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query();
        //设置对比筛选的开始时间、结束时间 对比结束时间=初始开始时间 对比开始时间=初始开始时间-初始区间间隔
        if ($isLast) {
            $this->scope->endTime = $this->scope->startTime;
            $this->scope->startTime -= $this->scope->duration;
        }
        $crashStatistic = $this->getCount(clone ($this->query), "COUNT(1) as crash_count,COUNT(distinct server_dev_str) as crash_user_count");
        //获取异常启动上报次数、人数
        $this->scope = new StartScope($this->request->toArray());
        //设置对比筛选的开始时间、结束时间 对比结束时间=初始开始时间 对比开始时间=初始开始时间-初始区间间隔
        if ($isLast) {
            $this->scope->endTime = $this->scope->startTime;
            $this->scope->startTime -= $this->scope->duration;
        }
        $this->scope->useDuration = 0; //reset
        $startStatistic = $this->getCount(Start::query(), "COUNT(1) as start_count"); //分母：聯網次數
        $crashDeviceNum = $this->getCount(UserLogDataAll::query(), "COUNT(distinct server_dev_str) as start_user_count"); //分母：0聯網1崩潰2錯誤三者相加
        $crash_count = $crashStatistic['crash_count'] ?? 0; //崩潰次數
        $crash_user_count = $crashStatistic['crash_user_count'] ?? 0; //崩潰設備
        $start_count = $startStatistic['start_count'] ?? 0; //聯網次數
        $start_user_count = $crashDeviceNum['start_user_count'] ?? 0; //聯網設備
        $crash_percent = (float) number_format(DataHelper::divide($crash_count, $start_count, 4) * 100, 2);
        $crash_user_percent = (float) number_format(DataHelper::divide($crash_user_count, $start_user_count, 4) * 100, 2);
        return [
            'crash_count' => $crash_count,
            'crash_user_count' => $crash_user_count,
            'start_count' => $start_count,
            'start_user_count' => $start_user_count,
            'crash_percent' => $crash_percent,
            'crash_user_percent' => $crash_user_percent,
        ];
    }

    /**
     * 获取总数
     * @param object $object 查询事件模型
     * @param string $select 查询
     * @param string $groupBy 分组 默认为空
     * @return mixed
     */
    public function getCount(object $object, string $select = 'Count(1) AS count', string $groupBy = 'null')
    {
        return $object->where('extra_app_id', $this->scope->developerAppId)
            ->tap(function ($query) {
                $this->scope->getBuilder($query);
            })
            ->groupByRaw($groupBy)
            ->selectRaw($select)
            ->FirstFromCk();
    }

    /**
     * 获取崩溃分布 number_mode 0为用户量、1为上报量
     * @param $type
     * @return array
     */
    public function getCrashDistribution($type): array
    {
        if (isset($this->request->number_mode) && $this->request->number_mode == 1) {
            //number_mode = 1 为上报量 是统计所有的数据
            $select = $type . " as name, COUNT(1) as num";
        } else {
            //number_mode = 0（默认） 为用户量 是统计所有的设备id去重
            $select = $type . " as name, COUNT(distinct server_dev_str) as num";
        }
        $list = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query()
            ->where('extra_app_id', $this->scope->developerAppId)
            ->where($type, '!=', "")
            ->tap(function ($query) {
                $this->scope->getBuilder($query);
            })
            ->groupByRaw($type)
            ->orderBy('num', 'desc')
            ->selectRaw($select)
            ->limit(10)
            ->getFromCk();
        $sum = 0;
        foreach ($list as $item) {
            $sum += $item['num'];
        }
        foreach ($list as $key => $item) {
            $list[$key]['percent'] = (float) number_format(DataHelper::divide($item['num'] ?? 0, $sum ?? 0, 4) * 100, 2);
        }
        //按照比例排序
        $column = array_column($list, 'percent');
        array_multisort($column, SORT_DESC, $list);
        return $list;
    }

    /**
     * 分组获取数据 (按照时间分组)
     * group 分组 默认不分组 1为APP版本、2为SDK版本
     * @return array
     */
    public function getSummaryDataByGroup(): array
    {
        //group 分组 1为APP版本、2为SDK版本
        switch ($this->request->group) {
            case 1:
                $group = "app_version";
                $orderBy = $groupBy = $group . ', date ';
                break;
            case 2:
                $group = "version";
                $orderBy = $groupBy = $group . ', date';
                break;
            default:
                $group = null;
                $orderBy = $groupBy = 'date';
                break;
        }
        return $this->getSummaryByDateAndGroup($group, $groupBy, $orderBy);
    }

    /**
     * 通过日期和分组(app版本、sdk版本)获取数据
     * @param string|null $group
     * @param string $groupBy
     * @param string $orderBy
     * @return array
     */
    private function getSummaryByDateAndGroup(?string $group, string $groupBy, string $orderBy): array
    {
        // 获取筛选时间 timeDimension 按天/小时维度 days 按发生时间/上报时间维度
        [$timeDimension, $days] = $this->getSelectTime();
        [$data, $list] = $this->getValueByStatisticsType($timeDimension, $days, $group, $groupBy, $orderBy);
        $category = [];
        if (!empty($data)) {
            if ($group) {
                foreach ($list as $item) {
                    $category[] = $item['name'];
                }
            } else {
                $category[] = $list;
                foreach ($data as $key => $datum) {
                    $data[$key]['name'] = $list;
                }
            }
        }
        return [
            "series" => $data,
            "category" => $category,
        ];
    }

    /**
     * 处理筛选日期
     * @return array
     */
    private function getSelectTime(): array
    {
        $timeDimension = 'hour';
        //time_type    1为发生时间（默认）、2为上报时间
        if (isset($this->request->time_type) && $this->request->time_type == CrashScope::TIME_TYPE_UPLOAD) {
            //time_dimension 按时间维度 1为按小时维度(默认)、2为按天维度
            if (isset($this->request->time_dimension) && $this->request->time_dimension == 2) {
                $timeDimension = 'day';
                $days = UserLogDataAll::FORMAT_EVENT_TIME . " as date, ";
            } else {
                $days = UserLogDataAll::FORMAT_EVENT_HOUR . " as date, ";
            }
        } else if (isset($this->request->time_dimension) && $this->request->time_dimension == 2) {
            $timeDimension = 'day';
            $days = UserLogDataAll::FORMAT_DEV_CREATE_TIME . " as date, ";
        } else {
            $days = UserLogDataAll::FORMAT_DEV_CREATE_HOUR . " as date, ";
        }
        return [$timeDimension, $days];
    }

    /**
     * 根据统计数据类型返回对应的数据
     * @param $timeDimension
     * @param $days
     * @param $group
     * @param $groupBy
     * @param $orderBy
     * @return array
     */
    private function getValueByStatisticsType($timeDimension, $days, $group, $groupBy, $orderBy): array
    {
        $selectCount = $days . 'COUNT(1) as value';
        $selectUserCount = $days . 'COUNT(distinct server_dev_str) as value';
        $crashUserCountList = $crashCountList = $startCountList = $startUserCountList = null;
        switch ($this->request->statistics_type) {
            case 1:
                if ($group) {
                    [$crashCountList, $crashCount] = $this->getCrashByGroup($selectCount, $group, $groupBy, $orderBy);
                    array_unshift($crashCountList, ['name' => '全版本']);
                    $list = $crashCountList;
                } else {
                    // 次数
                    $list = $this->class::COUNT;
                    $crashCount = $this->getCKValue(clone ($this->query), $selectCount, $groupBy, $orderBy);
                }
                $appVersion = $this->scope->appVersion;
                $this->scope->appVersion = null;
                $allList = (clone ($this->query))->tap(function ($query) {
                    $this->scope->getBuilder($query);
                })->selectRaw($selectCount . ', "全版本" as name')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->getFromCK();
                $this->scope->appVersion = $appVersion;
                $data = $this->format(array_merge($allList, $crashCount), $timeDimension, $crashCountList);
                break;
            case 2:
                $allList = $crashAllList = [];
                if ($group) {
                    [$crashCountList, $crashCount] = $this->getCrashByGroup($selectCount, $group, $groupBy, $orderBy);
                    $appVersion = $this->scope->appVersion;
                    $this->scope->appVersion = null;
                    $crashAllList = (clone ($this->query))->tap(function ($query) {
                        $this->scope->getBuilder($query);
                    })->selectRaw($selectCount . ', "全版本" as name')
                        ->groupBy('date')
                        ->orderBy('date')
                        ->getFromCK();
                    $this->scope->appVersion = $appVersion;
                    [$startCountList, $startCount] = $this->getStartByGroup($selectCount, $group, $groupBy, $orderBy, $crashCountList);
                    $appVersion = $this->scope->appVersion;
                    $this->scope->appVersion = null;
                    $allList = Start::query()->tap(function ($query) {
                        $this->scope->getBuilder($query);
                    })->selectRaw($selectCount . ', "全版本" as name')
                        ->groupBy('date')
                        ->orderBy('date')
                        ->getFromCK();
                    $this->scope->appVersion = $appVersion;
                    array_unshift($crashCountList, ['name' => '全版本']);
                    array_unshift($startCountList, ['name' => '全版本']);
                    $list = $crashCountList;
                } else {
                    $list = $this->class::RATE;
                    // 获取次数
                    $crashCount = $this->getCKValue(clone ($this->query), $selectCount, $groupBy, $orderBy);
                    // 获取总启动次数
                    $startCount = $this->getStart($selectCount, $groupBy, $orderBy);
                }
                $crashCount = $this->format(array_merge($crashAllList, $crashCount), $timeDimension, $crashCountList);
                $startCount = $this->format(array_merge($allList, $startCount), $timeDimension, $startCountList);

                $data = $this->getPercentFormat($crashCount, $startCount);
                break;
            case 3:
                if ($group) {
                    [$crashUserCountList, $crashUserCount] = $this->getCrashByGroup($selectUserCount, $group, $groupBy, $orderBy);
                    array_unshift($crashUserCountList, ['name' => '全版本']);
                    $list = $crashUserCountList;
                } else {
                    // 人数（影响人数）
                    $list = $this->class::USER_COUNT;
                    $crashUserCount = $this->getCKValue(clone ($this->query), $selectUserCount, $groupBy, $orderBy);
                }
                $appVersion = $this->scope->appVersion;
                $this->scope->appVersion = null;
                $allList = (clone ($this->query))->tap(function ($query) {
                    $this->scope->getBuilder($query);
                })->selectRaw($selectUserCount . ', "全版本" as name')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->getFromCK();
                $this->scope->appVersion = $appVersion;
                $data = $this->format(array_merge($allList, $crashUserCount), $timeDimension, $crashUserCountList);
                break;
            case 4:
                $allList = $crashAllList = [];
                if ($group) {
                    [$crashUserCountList, $crashUserCount] = $this->getCrashByGroup($selectUserCount, $group, $groupBy, $orderBy);
                    $appVersion = $this->scope->appVersion;
                    $this->scope->appVersion = null;
                    $crashAllList = (clone ($this->query))->tap(function ($query) {
                        $this->scope->getBuilder($query);
                    })->selectRaw($selectUserCount . ', "全版本" as name')
                        ->groupBy('date')
                        ->orderBy('date')
                        ->getFromCK();
                    $this->scope->appVersion = $appVersion;
                    [$startUserCountList, $startUserCount] = $this->getStartByGroup($selectUserCount, $group, $groupBy, $orderBy, $crashCountList);
                    $appVersion = $this->scope->appVersion;
                    $this->scope->appVersion = null;
                    $allList = Start::query()->tap(function ($query) {
                        $this->scope->getBuilder($query);
                    })->selectRaw($selectUserCount . ', "全版本" as name')
                        ->groupBy('date')
                        ->orderBy('date')
                        ->getFromCK();
                    $this->scope->appVersion = $appVersion;
                    array_unshift($crashUserCountList, ['name' => '全版本']);
                    array_unshift($startUserCountList, ['name' => '全版本']);
                    $list = $crashUserCountList;
                } else {
                    // 用户比例
                    $list = $this->class::USER_RATE;
                    // 获取人数
                    $crashUserCount = $this->getCKValue(clone ($this->query), $selectUserCount, $groupBy, $orderBy);
                    // 获取用户数
                    $startUserCount = $this->getStart($selectUserCount, $groupBy, $orderBy);
                }
                $crashUserCount = $this->format(array_merge($crashAllList, $crashUserCount), $timeDimension, $crashUserCountList);
                $startUserCount = $this->format(array_merge($allList, $startUserCount), $timeDimension, $startUserCountList);
                // 用户比例 = 人数/用户数
                $data = $this->getPercentFormat($crashUserCount, $startUserCount);
                break;
            case 5:
                if ($group) {
                    [$startUserCountList, $startUserCount] = $this->getStartByGroup($selectUserCount, $group, $groupBy, $orderBy);
                    array_unshift($startUserCountList, ['name' => '全版本']);
                    $list = $startUserCountList;
                } else {
                    // 用户数
                    $list = Start::USER_COUNT;
                    // 获取用户数
                    $startUserCount = $this->getCKValue(Start::query(), $selectUserCount, $groupBy, $orderBy);
                }
                $appVersion = $this->scope->appVersion;
                $this->scope->appVersion = null;
                $allList = Start::query()->tap(function ($query) {
                    $this->scope->getBuilder($query);
                })->selectRaw($selectUserCount . ', "全版本" as name')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->getFromCK();
                $this->scope->appVersion = $appVersion;
                $data = $this->format(array_merge($allList, $startUserCount), $timeDimension, $startUserCountList);
                break;
            default:
                //默认为[]
                $list = [];
                $data = [];
                break;
        }
        return [$data, $list];
    }

    /**
     * 根据分组（app版本、sdk版本）获取异常上报的数据
     * @param string $select
     * @param string $group
     * @param string $groupBy
     * @param string $orderBy
     * @return array
     */
    private function getCrashByGroup(string $select, string $group, string $groupBy, string $orderBy): array
    {
        $selectType = $group . ' as name';
        $select .= ', ' . $selectType;
        // 获取异常上报事件的最新的20个版本号
        $crashList = $this->getCKValue(clone ($this->query), $selectType, $group, $group, $group, 20);
        // 根据最新的20个版本号 获取崩溃人数
        $crash = $this->getLatestDataByType(clone ($this->query), $select, $group, $crashList, $groupBy, $orderBy);
        return [$crashList, $crash];
    }

    /**
     * 查询ck数据
     * @param object $object 查询事件模型
     * @param string $select
     * @param string $groupBy
     * @param string $orderBy
     * @param string|null $type
     * @param int|null $limit
     * @return array
     */
    public function getCKValue(object $object, string $select, string $groupBy, string $orderBy, ?string $type = null, ?int $limit = null): array
    {
        return $object->when($type, function ($query, $type) {
            $query->where($type, '!=', "");
        })
            ->tap(function ($query) {
                $this->scope->getBuilder($query);
            })
            ->groupByRaw($groupBy)
            ->selectRaw($select)
            ->orderByRaw($orderBy)
            ->when($limit, function ($query, $limit) {
                $query->limit($limit);
            })
            ->getFromCk();
    }

    /**
     * 获取最新的分组数据
     * @param object $object
     * @param string $select
     * @param string $type
     * @param $list
     * @param string $groupBy
     * @param string $orderBy
     * @return mixed
     */
    private function getLatestDataByType(object $object, string $select, string $type, $list, string $groupBy, string $orderBy)
    {
        return $object->tap(function ($query) {
            $this->scope->getBuilder($query);
        })->whereIn($type, $list)->selectRaw($select)
            ->groupByRaw($groupBy)->orderByRaw($orderBy)->getFromCK();
    }

    /**
     * 格式化数据
     * @param $data
     * @param string $timeDimension
     * @param $names
     * @return array
     */
    private function format($data, string $timeDimension, $names): array
    {
        if (empty($data)) {
            return [];
        }
        // 添加从开始到结束时间的日期间隔
        $list = $this->getDateData();
        if ($timeDimension == 'hour') {
            // 添加从开始到结束时间的小时时间间隔
            $list = $this->getHourData($list);
        }
        // 添加名称
        if ($names) {
            $list = $this->getNameData($list, $names);
        }
        $newData = [];
        foreach ($data as $datum) {
            $newData["{$datum['date']}:00|||{$datum['name']}"] = $datum['value'];
            $newData["{$datum['date']}|||{$datum['name']}"] = $datum['value'];
        }
        foreach ($list as $key => $item) {
            $value = null;
            if ($timeDimension == 'hour') {
                $value = $newData["{$item['date']}:00|||{$item['name']}"] ?? null;
            }
            if (empty($value)) {
                $value = $newData["{$item['date']}|||{$item['name']}"] ?? null;
            }
            if (!empty($value)) {
                //转为数字，保留两位小数
                $list[$key]['value'] = round((float) $value, 2);
            }
        }
        return $list;
    }

    /**
     * 添加从开始到结束时间的日期间隔
     * @return array
     */
    private function getDateData(): array
    {
        $list = [];

        $endDate = Carbon::parse($this->scope->endDate);
        $startDate = Carbon::parse($this->scope->startDate);
        $duration = $endDate->diffInDays($startDate);

        for ($i = 0; $i <= $duration; $i++) {
            $arr['date'] = $startDate->format('Y-m-d');
            $arr['value'] = 0;
            $list[] = $arr;
            $startDate->addDay();
        }
        return $list;
    }

    /**
     * 添加从开始到结束时间的小时时间间隔
     * @param array $data
     * @return array
     */
    private function getHourData(array $data): array
    {
        $list = [];
        foreach ($data as $item) {
            for ($i = 0; $i < 24; $i++) {
                $arr['date'] = Carbon::parse($item['date'])->addHours($i)->toDateTimeString('minute');
                $arr['value'] = 0;
                $list[] = $arr;
            }
        }
        return $list;
    }

    /**
     * 添加名称
     * @param array $data
     * @param array $lists
     * @return array
     */
    private function getNameData(array $data, array $lists): array
    {
        $list = [];
        foreach ($lists as $value) {
            foreach ($data as $key => $item) {
                $data[$key]['name'] = $value['name'];
                $list[] = $data[$key];
            }
        }
        return $list;
    }

    /**
     * 根据分组（app版本、sdk版本）获取启动上报的数据
     * @param string $select
     * @param string $group
     * @param string $groupBy
     * @param string $orderBy
     * @return array
     */
    private function getStartByGroup(string $select, string $group, string $groupBy, string $orderBy, $list = []): array
    {
        $selectType = $group . ' as name';
        $select .= ', ' . $selectType;
        //实例化startScope 启动上报查询筛选按startScope
        $this->scope = new StartScope($this->request->toArray());
        // 判断是否有传递 list
        if (!empty($list)) {
            $startList = $list;
        } else {
            // 获取启动上报事件的最新的20个版本号
            $startList = $this->getCKValue(Start::query(), $selectType, $group, $group, $group, 20);
        }
        // 根据最新的20个版本号 获取用户数
        $start = $this->getLatestDataByType(Start::query(), $select, $group, $startList, $groupBy, $orderBy);
        return [$startList, $start];
    }

    /**
     * 获取启动上报的数据
     * @param string $select
     * @param string $groupBy
     * @param string $orderBy
     * @return array
     */
    private function getStart(string $select, string $groupBy, string $orderBy): array
    {
        // 实例化startScope 启动上报查询筛选按startScope
        $this->scope = new StartScope($this->request->toArray());
        return $this->getCKValue(Start::query(), $select, $groupBy, $orderBy);
    }

    /**
     * 获取崩溃率、崩溃用户比例并格式化数据
     * @param array $arr1
     * @param array $arr2
     * @return array
     */
    private function getPercentFormat(array $arr1, array $arr2): array
    {
        if (empty($arr1) || empty($arr2)) {
            return [];
        }
        $list = [];
        $count = count($arr1);
        for ($i = 0; $i < $count; $i++) {
            $arr['date'] = $arr1[$i]['date'];
            isset($arr1[$i]['name']) && $arr['name'] = $arr1[$i]['name'];
            $arr['value'] = (float) number_format(DataHelper::divide($arr1[$i]['value'] ?? 0, $arr2[$i]['value'] ?? 0, 4) * 100, 2);
            $list[] = $arr;
        }
        return $list;
    }

}
