<?php

namespace App\Http\Controllers;

use App\Service\VersionWhiteListService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VersionWhiteListController extends BaseController
{
    /**
     * @var array 请求参数
     */
    protected $params;

    /**
     * @var VersionWhiteListService 版本白名单服务类
     */
    protected $versionWhiteListService;

    /**
     * 白名单列表类型
     */
    const DISPLAY_VERSION_LIST = 0; // 显示列表
    const HIDDEN_VERSION_LIST = 1; // 隐藏列表

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->params = $request->toArray();
        $this->versionWhiteListService = new VersionWhiteListService($this->params);
    }

    /**
     * 版本白名单管理列表
     * 支持版本搜索condition、显示/隐藏筛选项type、分页page,limit
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3949
     * @return JsonResponse
     */
    public function list()
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required|integer'
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }

            // 隐藏表名单列表返回
            if ($this->params['type'] == self::HIDDEN_VERSION_LIST) {
                return $this->response(0, $this->versionWhiteListService->hiddenWhiteList());
            } else if($this->params['type'] == self::DISPLAY_VERSION_LIST) {
                return $this->response(0, $result = $this->versionWhiteListService->displayWhiteList());
            }
        } catch (Exception $e) {
            \Log::error('获取版本白名单列表失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 在白名单中添加/删除版本
     * 支持批量隐藏/显示
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3950
     * @return JsonResponse
     */
    public function operate()
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'operate_type' => 'required|integer',
                'versions' => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }

            // 操作版本白名单数据库
            $this->versionWhiteListService->operateWhiteList();

            return $this->response();
        } catch (Exception $e) {
            \Log::error('版本白名单列表操作失败' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
    }
    }
}
