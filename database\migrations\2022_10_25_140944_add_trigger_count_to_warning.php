<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTriggerCountToWarning extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            //
            $table->unsignedInteger('trigger_count')->default(0)->comment('触发次数');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            //
            $table->dropColumn('trigger_count');
        });
    }
}
