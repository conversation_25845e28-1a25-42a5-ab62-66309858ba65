<?php

/**
 * tapd自动提单机器人推送
 * @desc tapd自动提单机器人推送
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\AppModel;
use App\Model\TapdAutoConfig;
use App\Service\WXGroupNoticeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class TapdAutoPushCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tapd:auto:push';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tapd自动提单机器人推送';

    /**
     * 处理
     *
     * @return void
     */
    public function handle(): void
    {
        // 打印日志
        Log::info('tapd自动提单机器人推送开始执行');
        // 获取tapd自动提单列表
        $list = TapdAutoConfig::query()
            ->where('status', 1)
            ->get();
        // 判断提单时间是否符合
        foreach ($list as $item) {
            // 判断key是否存在
            $key = date('Ymd') . '_tapd_auto_bug_' . $item['developer_app_id'];
            // 获取所有数据
            $data = Redis::hGetAll($key);
            // 判断
            if (empty($data) || $data['hit_num'] == 0) {
                continue;
            }
            $tapdConfig = json_decode($data['tapd_auto_config'], true);
            $maxNum = $tapdConfig['max_num'];
            if ($maxNum > $data['hit_num']) {
                $maxNum = $data['hit_num'];
            }
            // 判断 success_num 是否大于 num
            if ($data['success_num'] >= $maxNum && ($data['success_num'] % $maxNum) == 0) {
                $startTime = date('Y年m月d日 H时', $data['start_time']);
                $endTime = date('Y年m月d日 H时', $data['end_time']);
                // 打印日志
                Log::info('tapd自动提单机器人推送开始推送：' . json_encode($data));
                // 查询APP的name
                $appName = AppModel::query()->where('id', $tapdConfig['developer_app_id'])->value('app_name');
                // 删除缓存
                Redis::del($key);
                // 获取
                $filterKeys = ['start_time', 'end_time', 'num', 'white_num', 'config', 'tapd_auto_config', 'hit_num', 'success_num'];
                // 过滤特殊的key
                $newData = array_filter($data, function ($k) use ($filterKeys) {
                    return !in_array($k, $filterKeys);
                }, ARRAY_FILTER_USE_KEY);
                $extendText = '';
                if ($newData) {
                    $extendText = "\r\nTapd单具体分配情况：\r\n";
                    arsort($newData);
                    foreach ($newData as $k => $v) {
                        $extendText .= "{$k}：{$v}条\r\n";
                    }
                }
                // 推送
                foreach ($tapdConfig['webhook_url'] as $url) {
                    $text = <<<TEXT
# {$appName}提单通知
\r\n
{$startTime}  -  {$endTime}
\r\n
区间日志总数量：{$data['num']} 条
期望提单数量：{$data['hit_num']} 条
实际提单数量：{$data['success_num']} 条
白名单过滤命中数量：{$data['white_num']} 条
{$extendText}
TEXT;
                    $service = new WXGroupNoticeService($url, true);
                    $service->wxGroupNotify($text, 'markdown');
                }
            }
        }
        // 打印日志
        Log::info('tapd自动提单机器人推送结束执行');
    }
}
