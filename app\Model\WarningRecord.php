<?php

namespace App\Model;

class WarningRecord extends BaseModel
{
    use ModelTrait;

    protected $table = 'warning_record';
    protected $primaryKey = 'id';
    protected $fillable = [
        'developer_app_id', 'warning_id', 'rule_id', 'app_name', 'os_type', 'app_version', 'start_date', 'end_date'
    ];

    protected $casts = [
        'app_version' => 'array',
    ];

    /**
     * 关联预警表
     */
    public function warning()
    {
        return $this->belongsTo(Warning::class, 'warning_id', 'warning_id');
    }
}
