<?php
/**
 * 公共控制器
 *
 * User: Dican
 * Date: 2022/9/19
 * Email: <<EMAIL>>
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Components\Helper\CommonHelper;
use App\Http\Logic\SymbolLogic;
use App\Model\BaseModel;
use App\Model\Record;
use App\Model\Symbol;
use App\Model\TapdBug;
use App\Service\AnalysisService;
use App\Service\Exception\Analysis\WarningWeekData;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CommonController extends Controller
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request->all();
    }

    /**
     * 符号表列表
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2614
     * @return JsonResponse
     */
    public function symbolList(): JsonResponse
    {//
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'app_version' => 'required|string',
            'os_type' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $data = Symbol::symbolList($this->request['developer_app_id'], $this->request['app_version'], $this->request['os_type'], [], true);
        return $this->response(0, ['total' => $data->total(), 'list' => $data->items()]);
    }

    /**
     * 符號表列表（新）
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3959
     * @return JsonResponse
     */
    public function symbolIndex(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'app_version' => 'string',
            'uuid' => 'string',
            'file_name' => 'string',
            'page' => 'integer',
            'per_page' => 'integer',
            'os_type' => 'integer',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        $paginate = Symbol::symbolIndex(
            $this->request['developer_app_id'],
            $this->request['app_version'] ?? 0,
            $this->request['uuid'] ?? 0,
            $this->request['file_name'] ?? '',
            $this->request['per_page'] ?? 15,
            $this->request['os_type'] ?? 0,
        );
        return $this->response(0, ['total' => $paginate->total(), 'list' => $paginate->items()]);
    }

    /**
     * 上傳符號（新）
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5366
     * @return JsonResponse
     */
    public function symbolCommonStore(): JsonResponse
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'file_url' => 'required|string',
                'app_version' => 'required|string',
                'file_name' => 'required|string',
                'os_type' => 'nullable|integer',
            ]);
            if ($validator->fails()) {
                return $this->response(StatusCode::C_PARAM_INVAILD);
            }
            if (($this->request['os_type'] ?? 0) == 3) {
                $record = (new AnalysisService($this->request))->pcUUid($this->request['developer_app_id'], $this->request['app_version'], $this->request['file_url'], $this->request['file_name']);
            } else {
                $record = (new SymbolLogic())->symbolCommonStore($this->request['developer_app_id'], $this->request['app_version'], $this->request['file_url'], $this->request['file_name'], ($this->request['os_type'] ?? 0));
            }
            if (!$record) {
                throw new \Exception('符号解释超时');
            }

            return $this->response(0, $record);
        } catch (\Throwable $e) {
            return $this->response(1005, [], $e->getMessage());
        }
    }

    /**
     * 上传、重新上传符号表
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2615
     * @param Symbol|null $symbol
     * @return JsonResponse
     */
    public function symbolStore(Symbol $symbol = null): JsonResponse
    {
        try {
            if (empty($symbol)) {
                //默认为崩溃异常
                $this->request['type'] = Symbol::TYPE_CRASH;
                $symbol = new Symbol();
                $symbol->store($this->request, true, true);
            } else {
                //编辑时不可更改appId、异常类型、app版本、uuid、os_type
                $this->request['developer_app_id'] = $symbol->developer_app_id;
                $this->request['type'] = $symbol->type;
                $this->request['app_version'] = $symbol->app_version;
                $this->request['uuid'] = $symbol->uuid;
                $this->request['os_type'] = $symbol->os_type;
                $symbol->store($this->request, true);
            }
            return $this->response();
        } catch (\Exception $e) {
            \Log::error('保存符号表接口报错,id:' . $symbol->id . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 符号表解析
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2647
     * @return JsonResponse
     * @throws \Throwable
     */
    public function symbolAnalysis(): JsonResponse
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'exception_block_id' => 'required|string',
                'app_version' => 'required|string',
                'content' => 'nullable|string',
                'origin_stacks' => 'nullable|array',
                'server_dev_str' => 'required|string',
                'client_ts' => 'required|integer',
                'ts' => 'required|integer',
                'uuid_info' => 'required|array',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            $where = [
                'developer_app_id' => $this->request['developer_app_id'],
                'exception_block_id' => $this->request['exception_block_id'],
                //                'name' => $this->request['name'],
                //                'explain' => $this->request['explain'],
                //                'category' => $this->request['category'],
                //                'type' => $this->request['type']
            ];
            $record = Record::where($where)->firstOrFail();
            $exceptionMergeId = $this->request['exception_merge_id'] ?? '';
            // 判断系统版本，如果是PC并且缺少merge_id，则抛出异常
            if ($record['os_type'] == 3) {
                if (empty($exceptionMergeId)) {
                    return $this->response(1011);
                }
                $data = (new AnalysisService($this->request))->pcAnalysis($this->request['exception_block_id'], $exceptionMergeId);
                return $this->response(0, $data);
            }

            //非可解析异常类型
            if (!in_array($record->category, $record::$analysisCategory)) {
                return $this->response(1011);
            }
            //获取指定版本和uuid相关符号表文件链接和uuid
            $temp = \Arr::pluck($this->request['uuid_info'], 'uuid');
            $symbol = Symbol::symbolList($this->request['developer_app_id'], $this->request['app_version'], $record->os_type,
                \Arr::pluck($this->request['uuid_info'], 'uuid'), false, $record->type);
            //将组content、origin_stacks、singular_type组装成JSON写入文件
            $dir = storage_path('app/public/');
            $fileName = sprintf("%d-%d-%s-%d-symbolAnalysis.txt",
                $this->request['developer_app_id'], $record->type, $this->request['app_version'], time()
            );
            //$localFile = $dir . $fileName;//DEBUG_LABEL
            $content = json_encode([
                'content' => $this->request['content'],
                'origin_stacks' => $this->request['origin_stacks'],
                'singular_type' => $record->category,
                'os_type' => $record->os_type,
            ]);
            //FileHelper::write($localFile, $content);//DEBUG_LABEL
            //上传文件到打包机、执行脚本
            //$sshService = new SshService(config('ssh.packer_host'), config('ssh.packer_port'), config('ssh.packer_user'), config('ssh.packer_pwd'));//DEBUG_LABEL
            $remoteFile = '/Users/<USER>/exception/symbolAnalysis/' . config('app.env') . '/' . $fileName;
            //$uploadResult = $sshService->scpUploadFile($localFile, $remoteFile);//DEBUG_LABEL
            //if (!$uploadResult) return $this->response(1005, [], '上传文件到解析服务器失败');//DEBUG_LABEL
            //上传成功后删除本地文件
            //unlink($localFile);//DEBUG_LABEL
            //组装symbol-url，按#截取为字符串
            $fileUrls = $symbol->pluck('file_url')->implode('#');
            $fileNames = $symbol->pluck('file_name')->implode('#');
            $cmd = 'cd /Users/<USER>/awesome-tools;git pull;source venv/bin/activate;
                inv crash.prase-crash.parse --params-json-path=' . $remoteFile . ' --symbol-url=' . $fileUrls;
            \Log::info('cmd:' . $cmd);
            $type = SymbolLogic::$SYMBOL_SERVICE_TYPE['ANALYSIS'];
            // 判断是否鸿蒙系统
            if ($record->os_type == BaseModel::HARMONY) {
                $type = SymbolLogic::$SYMBOL_SERVICE_TYPE['HARMONY_PARSE_STACK'];
            }
            //解析异常
            $symbolDetail = (new SymbolLogic())->pullSymbolDetail($type, $fileUrls, $content, $fileNames);
            if ($symbolDetail) {
                //$sshService->run($cmd, $output);//DEBUG_LABEL
                //$runResult = $sshService->formatOut($output);//DEBUG_LABEL
                //if ($runResult['code'] != 0) return $this->response(1005, [], '解析报错');//DEBUG_LABEL
                //将异常类型、异常错误名称、符号表文件uuid_info放进request里
                $this->request['category'] = $record->category ?? 0;
                $this->request['name'] = $record->name ?? '';
                $this->request['uuid_info'] = $symbol->pluck('uuid')->toJson();
                //解析成功 上报解析的最新原始堆栈
                // $analysisService = new AnalysisService($this->request);
                // $analysisService->analysisUpload($symbolDetail);//原始堆棧
                return $this->response(0, $symbolDetail);
            }
        } catch (\Exception $e) {
            CommonHelper::xdebug($e);
            \Log::error('解析符号表接口报错原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005, $e->getMessage() . ' file: ' . $e->getFile() . ' line: ' . $e->getLine());
        }

    }

    /**
     * 符号表删除
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3959
     * @return JsonResponse
     * @throws \Exception
     */
    public function symbolDel(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        // 删除符号表
        Symbol::query()->where('id', $this->request['id'])->delete();
        // 返回结果
        return $this->response();
    }

    /**
     * 分享
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12022
     * @return JsonResponse
     */
    public function share(): JsonResponse
    {
        try {
            //判断参数是否为空
            if (empty($this->request)) {
                return $this->response(StatusCode::C_PARAM_INVAILD);
            }
            //缓存key的前缀
            $cacheKeyPrefix = 'hitbug_share_';
            //判断请求参数中是否有分享ID
            if (!empty($this->request['share_id'])) {
                //从缓存中获取分享信息
                $share = Cache::get("{$cacheKeyPrefix}{$this->request['share_id']}");
                //判断是否有内容
                if (empty($share)) {
                    return $this->response(StatusCode::C_PARAM_INVAILD);
                }
                //返回数据
                return $this->response(StatusCode::C_SUCCESS, json_decode($share, true));
            }
            //对参数进行json化
            $params = json_encode($this->request, JSON_UNESCAPED_UNICODE);
            //生成分享ID
            $shareId = md5($params);
            //保存到缓存中，保存7天
            Cache::add("{$cacheKeyPrefix}{$shareId}", $params, 7 * 24 * 60 * 60);
            //返回结果
            return $this->response(StatusCode::C_SUCCESS, ['share_id' => $shareId]);
        } catch (\Exception $e) {
            \Log::error('分享接口报错原因:' . $e->getMessage() . 'in: ' . $e->getFile() . 'line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取预警周数据
     *
     * @return JsonResponse
     */
    public function getWarningWeekData()
    {
        $params = request()->all();

        // 验证key
        if (empty($params['key']) || $params['key'] != 'yHBYEucdk3729v5KZASmCOixrDIGfRT60hM1gLaNVoQs8zUXje4pqFltnwPb') {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        return $this->response(StatusCode::C_SUCCESS, (new WarningWeekData($params))->getData());
    }

    /**
     * tapd解绑
     *
     * @return JsonResponse
     */
    public function unbind()
    {
        // 获取 event的数据
        if (is_string($this->request['event'])) {
            $this->request['event'] = json_decode($this->request['event'], true);
        }
        // 获取 workspace_id 和 id
        $workspaceId = $this->request['event']['workspace_id'];
        $id = $this->request['event']['id'];
        // 判断是否望月项目
        if ($workspaceId == 52787908) {
            TapdBug::untieTapd([
                'developer_app_id' => 28,
                'bug_id' => $id,
            ]);
        }
        // 打印日志
        Log::info("tapd解绑成功,workspace_id:{$workspaceId},id:{$id}");
        // 返回
        return $this->response();
    }
}
