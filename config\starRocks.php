<?php

/**
 * starRocks数据仓库相关配置
 * @desc starRocks数据仓库相关配置
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/21
 * @todo 这里是后续需要跟进的功能说明
 */

return [
    //数据仓地址
    'starRocks_host' => env('STAR_ROCKS_HOST', ''),
    //数据仓的用户名
    'starRocks_username' => env('STAR_ROCKS_USERNAME', ''),
    //数据仓的密码
    'starRocks_password' => env('STAR_ROCKS_PASSWORD', ''),
    //数据仓的数据库
    'starRocks_database' => env('STAR_ROCKS_DATABASE', 'sdk_log'),
    //数据仓的端口
    'starRocks_port' => env('STAR_ROCKS_PORT', 9030),
    //数据仓的exception_stream_all库的url
    'starRocks_exception_stream_all_url' => env('STAR_ROCKS_EXCEPTION_STREAM_ALL_URL', ''),
    //数据仓的exception_stream_keyword库的url
    'starRocks_exception_stream_keyword_url' => env('STAR_ROCKS_EXCEPTION_STREAM_KEYWORD_URL', ''),
    //数据仓的exception_stat_all_v2库的url
    'starRocks_exception_stat_all_v2_url' => env('STAR_ROCKS_EXCEPTION_STAT_ALL_URL', ''),
    //数据仓的exception_stat_version_v3库的url
    'starRocks_exception_stat_version_v3_url' => env('STAR_ROCKS_EXCEPTION_STAT_VERSION_URL', ''),
    //数据仓的exception_record_perf库的url
    'starRocks_exception_record_perf_url' => env('STAR_ROCKS_EXCEPTION_RECORD_PERF_URL', ''),
    //数据仓的exception_analysis库的url
    'starRocks_exception_analysis_url' => env('STAR_ROCKS_EXCEPTION_ANALYSIS_URL', ''),
    //数据仓的exception_tapd_bug库的url
    'starRocks_exception_tapd_bug_url' => env('STAR_ROCKS_EXCEPTION_TAPD_BUG_URL', ''),
    //数据仓的exception_record_new库的url
    'starRocks_exception_record_new_url' => env('STAR_ROCKS_EXCEPTION_RECORD_NEW_URL', ''),
    //数据仓的exception_handler库的url
    'starRocks_exception_handler_url' => env('STAR_ROCKS_EXCEPTION_HANDLER_URL', ''),
    //数据仓的exception_process库的url
    'starRocks_exception_process_url' => env('STAR_ROCKS_EXCEPTION_PROCESS_URL', ''),
];
