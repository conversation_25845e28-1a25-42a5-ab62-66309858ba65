<?php

/**
 * 监控数据字段是否正常入库脚本
 * @desc 监控数据字段是否正常入库脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Model\AppModel;
use App\Model\BaseModel;
use App\Model\StarRocks\ExceptionStreamAll;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorDataCommand extends Command
{
    /**
     * 机器人webhook地址
     *
     * @var string
     */
    const HOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cffbb57a-3a02-407b-b320-fdaecafd20a2';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控数据字段是否正常入库脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //打印日志
            Log::info("执行监控数据字段是否正常入库开始");
            // 获取昨天时间
            $yesterday = Carbon::yesterday()->toDateString();
            // 查询字段
            $selectRaw = <<<STR
extra_app_id,
os_type,
event_name,
sum(case when NULL_OR_EMPTY(server_dev_str) then 1 else 0 end)     as server_dev_str_count,
sum(case when NULL_OR_EMPTY(subject_name) and event_name != 'exception_start' then 1 else 0 end)       as subject_name_count,
sum(case when NULL_OR_EMPTY(explain_desc) and event_name != 'exception_start' then 1 else 0 end)       as explain_desc_count,
sum(case when NULL_OR_EMPTY(basic_info_json) and event_name != 'exception_start' then 1 else 0 end)    as basic_info_json_count
STR;
            // 查询某些字段数据为空的数据
            $data = ExceptionStreamAll::query()
                ->selectRaw($selectRaw)
                ->where('stream_date', $yesterday)
                ->whereRaw('(NULL_OR_EMPTY(server_dev_str) OR NULL_OR_EMPTY(subject_name) OR NULL_OR_EMPTY(explain_desc) OR NULL_OR_EMPTY(basic_info_json))')
                ->groupBy('extra_app_id', 'os_type', 'event_name')
                ->getFromSR();
            // 如何数据为空，则结束
            if (empty($data)) {
                return;
            }
            // 判断是有有数据为0的，则移除
            $newData = [];
            foreach ($data as $item) {
                // 获取数据长度
                $length = count($item);
                $keys = array_keys($item);
                // 检测关键数据是否为0
                for ($i = 3; $i < $length; $i++) {
                    // 判断数据是否为0
                    if (((int) $item[$keys[$i]]) > 0) {
                        $newData[] = $item;
                        break;
                    }
                }
            }
            // 判断是否有数据
            if (empty($newData)) {
                return;
            }
            // 获取APP信息
            $apps = AppModel::query()->pluck('app_name', 'id');
            // 组装msg信息
            $content = "HitBug字段为空数据【{$yesterday}】：\n";
            // 循环处理数据
            foreach ($newData as $item) {
                // App名称
                $appName = $apps[$item['extra_app_id']] ?? '未知';
                // 判断是否是错误还是崩溃
                $eventName = '未知';
                if ($item['event_name'] == 'exception_error') {
                    $eventName = '错误';
                } elseif ($item['event_name'] == 'exception_start') {
                    $eventName = '初始化';
                } elseif ($item['event_name'] == 'exception_crash') {
                    $eventName = '崩溃';
                }
                // 系统
                $osType = BaseModel::OS_TYPE_TEXT[$item['os_type']] ?? '未知';
                // 文字内容
                $text = '';
                // 判断有哪些字段为空
                if ($item['server_dev_str_count'] > 0) {
                    $text .= "server_dev_str:{$item['server_dev_str_count']}, ";
                }
                if ($item['subject_name_count'] > 0) {
                    $text .= "subject_name:{$item['subject_name_count']}, ";
                }
                if ($item['explain_desc_count'] > 0) {
                    $text .= "explain_desc:{$item['explain_desc_count']}, ";
                }
                if ($item['basic_info_json_count'] > 0) {
                    $text .= "basic_info_json:{$item['basic_info_json_count']}, ";
                }
                // 把最后的, 去掉
                $text = rtrim($text, ", ");
                // 组装msg信息
                $content .= "{$appName}【{$osType}】（{$eventName}）为空的数据：{$text}\n";

            }
            // 把最后的换行符去掉
            $content = rtrim($content, "\n");
            // 发送请求
            HttpAgent::getInstance()->request("POST", self::HOOK_URL, [
                'json' => [
                    'msgtype' => 'text',
                    'text' => [
                        'content' => $content,
                    ],
                ],
            ]);
            //打印日志
            Log::info("执行监控数据字段是否正常入库完成");
        } catch (\Exception $e) {
            Log::error("执行监控数据字段是否正常入库脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
