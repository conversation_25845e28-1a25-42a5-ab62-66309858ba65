<?php

/**
 * 全局配置迁移文件
 * @desc 全局配置迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/27
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGlobalConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('global_config', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->mediumText('config')->nullable()->comment('配置信息');
            $table->timestamps();
            $table->index('developer_app_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `global_config` comment 'global_config'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('global_config');
    }
}
