<?php

/**
 * 统计版本
 * @desc 统计版本
 * <AUTHOR> <EMAIL>
 * @date 2024/06/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\ClickHouse;

use Arr;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Eloquent
 * @mixin Builder
 * @method static \Illuminate\Database\Eloquent\Builder getFromCK()
 * @method static \Illuminate\Database\Eloquent\Builder firstFromCK()
 */
class ExceptionVersion extends Model
{
    protected $table = 'exception_stat_version_v3';
    protected $casts = [];

    /**
     * 查询构建
     * @return ExceptionVersion
     */
    public static function query()
    {
        return (new static());
    }

    /**
     * 查询执行
     * @param $query
     * @return array
     */
    public function scopeGetFromCK($query)
    {
        $data = \ClickHouse::getData($query);
        if (empty($this->casts)) return $data;

        //格式化需要转移的字段
        foreach ($data as $key => $datum) {
            foreach ($datum as $column => $value) {
                $formatType = $this->casts[$column] ?? null;
                $data[$key][$column] = $this->getFormatValue($formatType, $value);
            }
        }
        return $data;
    }

    /**
     * 格式化返回的数据
     * @param $type
     * @param $value
     * @return mixed
     */
    public function getFormatValue($type, $value)
    {
        switch ($type) {
            case 'array':
            case 'json':
                $value = json_decode($value, true) ?? [];
                break;
        }
        return $value;
    }

    /**
     * 执行first操作
     * @param $query
     * @return mixed|null
     */
    public function scopeFirstFromCk($query)
    {
        $query = $query->limit(1);
        $data = \ClickHouse::getData($query);
        $ret = is_array($data) ? Arr::first($data) : [];
        $ret = empty($ret) ? [] : $ret;

        if (empty($this->casts)) return $ret;

        //格式化需要转移的字段
        foreach ($ret as $column => $value) {
            $formatType = $this->casts[$column] ?? null;
            $ret[$column] = $this->getFormatValue($formatType, $value);
        }
        return $ret;
    }
}
