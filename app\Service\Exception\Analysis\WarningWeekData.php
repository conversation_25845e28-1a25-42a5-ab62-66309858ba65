<?php

/**
 * 获取预警周数据
 * @desc 获取预警周数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/10/29
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Model\BaseModel;
use App\Model\Record;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\VersionWhiteList;
use App\Service\Exception\KeyWordFilter;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WarningWeekData
{
    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        $current = [Carbon::now()->subWeek()->startOfWeek()->toDateTimeString(), Carbon::now()->subWeek()->endOfWeek()->toDateTimeString()];
        $old = [Carbon::now()->subWeeks(2)->startOfWeek()->toDateTimeString(), Carbon::now()->subWeeks(2)->endOfWeek()->toDateTimeString()];
        return $this->calcRateData([
            'current' => $this->getDbData($current),
            'old' => $this->getDbData($old),
            'current_curve' => $this->getDayDbData($current),
            'date' => $current,
        ]);
    }

    /**
     * 计算环比
     *
     * @param $dates
     * @return array
     */
    private function calcRateData(array $data)
    {
        $current = $data['current'];
        $old = $data['old'];

        $current['crash_rate'] = bcadd(round(bcdiv(bcsub($current['crash'], $old['crash'], 6), empty($old['crash']) ? 1 : $old['crash'], 6) * 100, 2), 0, 2);
        $current['error_rate'] = bcadd(round(bcdiv(bcsub($current['error'], $old['error'], 6), empty($old['error']) ? 1 : $old['error'], 6) * 100, 2), 0, 2);
        $current['crash_dev_rate'] = bcadd(round(bcdiv(bcsub($current['crash_dev'], $old['crash_dev'], 6), empty($old['crash_dev']) ? 1 : $old['crash_dev'], 6) * 100, 2), 0, 2);
        $current['error_dev_rate'] = bcadd(round(bcdiv(bcsub($current['error_dev'], $old['error_dev'], 6), empty($old['error_dev']) ? 1 : $old['error_dev'], 6) * 100, 2), 0, 2);
        $current['crash_dev_rate'] = bcadd(round(bcdiv(bcsub($current['crash_dev'], $old['crash_dev'], 6), empty($old['crash_dev']) ? 1 : $old['crash_dev'], 6) * 100, 2), 0, 2);
        $current['dev_num_rate'] = bcadd(round(bcdiv(bcsub($current['dev_num'], $old['dev_num'], 6), empty($old['dev_num']) ? 1 : $old['dev_num'], 6) * 100, 2), 0, 2);

        return [
            'date' => date('Y.m.d', strtotime($data['date'][0])) . '~' . date('m.d', strtotime($data['date'][1])),
            'current' => $current,
            'old' => $old,
            'current_curve' => $data['current_curve'],
        ];
    }

    /**
     * 获取数据库数据
     *
     * @param $dates
     * @return array
     */
    private function getDbData(array $dates)
    {
        // 获取数据
        $list = ExceptionStreamAll::query()
            ->selectRaw('event_name, count(1) as num, count(distinct server_dev_str) as dev_num')
            ->whereBetween("stream_date", $dates) //过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("os_type", $this->params['os_type'])
            ->groupByRaw('event_name')
            ->orderByRaw('event_name')
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']]);
            })
            ->getFromSR();
        $allDev = ExceptionStreamAll::query()
            ->selectRaw("stream_date, count(distinct server_dev_str) as all_num")
            ->whereBetween("stream_date", $dates) //过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("os_type", $this->params['os_type'])
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']]);
            })
            ->groupBy('stream_date')
            ->getFromSR();
        //过滤白名单
        $keywordFilterService = new KeyWordFilter($this->params['developer_app_id']);
        $keywordFilter = $keywordFilterService->getNotStatKeyword();
        !empty($dates[0]) && $keywordFilterService->setDate(date('Y-m-d', strtotime($dates[0])));
        $appVersionWhite = VersionWhiteList::getVersionList($this->params['developer_app_id']);
        $crashExceptionBlockIdWhite = Record::getExceptionBlockIds($this->params['developer_app_id'], 1);
        $errorExceptionBlockIdWhite = Record::getExceptionBlockIds($this->params['developer_app_id'], 2);
        $crashDev = ExceptionStreamAll::query()
            ->whereBetween("stream_date", $dates) //过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("os_type", $this->params['os_type'])
            ->selectRaw("stream_date, count(distinct server_dev_str) as crash_num")
            ->when($appVersionWhite, function ($query, $appVersionWhite) {
                return $query->whereNotIn('app_version', $appVersionWhite);
            })
            ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                return $query->whereRaw($keywordFilterService->getFilterSql());
            })
            ->when($crashExceptionBlockIdWhite, function ($query, $exceptionBlockIdWhite) {
                return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                    return strtoupper($item);
                }, $exceptionBlockIdWhite));
            })
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']]);
            })
            ->where('event_name', 'exception_crash')
            ->groupBy('stream_date')
            ->getFromSR();
        $errorDev = ExceptionStreamAll::query()
            ->whereBetween("stream_date", $dates) //过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("os_type", $this->params['os_type'])
            ->selectRaw("stream_date, count(distinct server_dev_str) as error_num")
            ->when($appVersionWhite, function ($query, $appVersionWhite) {
                return $query->whereNotIn('app_version', $appVersionWhite);
            })
            ->when($keywordFilter, function ($query) use ($keywordFilterService) {
                return $query->whereRaw($keywordFilterService->getFilterSql());
            })
            ->when($errorExceptionBlockIdWhite, function ($query, $exceptionBlockIdWhite) {
                return $query->whereNotIn(DB::raw('upper(exception_block_id)'), array_map(function ($item) {
                    return strtoupper($item);
                }, $exceptionBlockIdWhite));
            })
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->params['developer_app_id']]);
            })
            ->where('event_name', 'exception_error')
            ->groupBy('stream_date')
            ->getFromSR();
        // 新的数组
        $list = array_column($list, null, 'event_name');
        // 数据处理
        $data = [
            'dev_num' => $list['exception_start']['dev_num'] ?? 0,
        ];
        $crashNum = empty($list['exception_crash']['num']) ? 0 : $list['exception_crash']['num'];
        $errorNum = empty($list['exception_error']['num']) ? 0 : $list['exception_error']['num'];
        $startNum = empty($list['exception_start']['num']) ? 1 : $list['exception_start']['num'];
        $startDevNum = 0;
        $crashDevNum = 0;
        $errorDevNum = 0;
        foreach ($allDev as $item) {
            $startDevNum += $item['all_num'];
        }
        foreach ($crashDev as $item) {
            $crashDevNum += $item['crash_num'];
        }
        foreach ($errorDev as $item) {
            $errorDevNum += $item['error_num'];
        }
        $data['crash'] = bcadd(round(bcdiv($crashNum, $startNum, 6) * 100, 2), 0, 2);
        $data['crash_dev'] = bcadd(round(bcdiv($crashDevNum, $startDevNum, 6) * 100, 2), 0, 2);
        $data['error'] = bcadd(round(bcdiv($errorNum, $startNum, 6) * 100, 2), 0, 2);
        $data['error_dev'] = bcadd(round(bcdiv($errorDevNum, $startDevNum, 6) * 100, 2), 0, 2);
        // 返回数据
        return $data;
    }

    /**
     * 获取周数据
     *
     * @param array $dates
     * @return array
     */
    private function getDayDbData(array $dates)
    {
        $list = ExceptionStreamAll::query()
            ->selectRaw('event_name, stream_date, count(1) as num, count(distinct server_dev_str) as dev_num')
            ->whereBetween("stream_date", $dates) //过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("os_type", $this->params['os_type'])
            ->groupByRaw('event_name, stream_date')
            ->orderByRaw('stream_date, event_name')
            ->getFromSR();
        // 新的数组
        $newList = [];
        // 处理数据，按日期分组
        foreach ($list as $item) {
            $newList[$item['stream_date']][$item['event_name']] = $item;
        }
        // 没有日期的数据填充0
        $startDate = date('Y-m-d', strtotime($dates[0]));
        $endDate = date('Y-m-d', strtotime($dates[1]));
        $list = $newList;
        $newList = [];
        while ($startDate <= $endDate) {
            if (!isset($list[$startDate])) {
                $newList[$startDate] = [
                    'exception_crash' => [
                        'num' => 0,
                        'dev_num' => 0,
                    ],
                    'exception_error' => [
                        'num' => 0,
                        'dev_num' => 0,
                    ],
                    'exception_start' => [
                        'num' => 0,
                        'dev_num' => 0,
                    ],
                ];
            } else {
                $newList[$startDate] = $list[$startDate];
            }
            $startDate = date('Y-m-d', strtotime("+1 day", strtotime($startDate)));
        }
        // 处理数据
        return $this->handleDayData(array_values($newList));
    }

    /**
     * 处理数据
     *
     * @param array $data
     * @return array
     */
    private function handleDayData(array $data)
    {
        $newData = [];
        // 把日期转为 周一 周二 周三 周四 周五 周六 周日
        $times = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        // 计算崩溃率和崩溃设备率、错误率和错误设备率
        foreach ($data as $key => $item) {
            $newItem = [];
            $crashNum = empty($item['exception_crash']['num']) ? 0 : $item['exception_crash']['num'];
            $crashDevNum = empty($item['exception_crash']['dev_num']) ? 0 : $item['exception_crash']['dev_num'];
            $errorNum = empty($item['exception_error']['num']) ? 0 : $item['exception_error']['num'];
            $errorDevNum = empty($item['exception_error']['dev_num']) ? 0 : $item['exception_error']['dev_num'];
            $startNum = empty($item['exception_start']['num']) ? 1 : $item['exception_start']['num'];
            $startDevNum = empty($item['exception_start']['dev_num']) ? 1 : $item['exception_start']['dev_num'];
            $newItem['stream_date'] = $times[$key];
            $newItem['crash'] = bcadd(round(bcdiv($crashNum, $startNum, 6) * 100, 6), 0, 2);
            $newItem['crash_dev'] = bcadd(round(bcdiv($crashDevNum, $startDevNum, 6) * 100, 6), 0, 2);
            $newItem['error'] = bcadd(round(bcdiv($errorNum, $startNum, 6) * 100, 6), 0, 2);
            $newItem['error_dev'] = bcadd(round(bcdiv($errorDevNum, $startDevNum, 6) * 100, 6), 0, 2);
            $newData[] = $newItem;
        }
        return $newData;
    }
}
