<?php

/**
 * BaseModel.php
 *
 * User: Dican
 * Date: 2022/7/27
 * Email: <<EMAIL>>
 */

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\BaseModel
 *
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel query()
 * @mixin \Eloquent
 */
class BaseModel extends Model
{
    public $connection = "exception";
    protected $guarded = [];
    //新增、编辑验证规则
    public $validateRule = [];
    //唯一值验证条件
    public $uniqueKey = [];

    //异常类型 1为崩溃异常
    const TYPE_CRASH = 1;
    public static $type = [
        self::TYPE_CRASH,
    ];

    //平台类型 1为安卓 2为iOS，3为PC
    const ANDROID = 1;
    const IOS = 2;
    const PC = 3;
    const MINI = 4;
    const HARMONY = 5;

    const OS_TYPE = [
        self::ANDROID => 'android',
        self::IOS => 'ios',
        self::PC => 'pc',
        self::MINI => 'mini',
        self::HARMONY => 'harmony',
    ];

    /**
     * 操作系统中文
     *
     * @var array
     */
    const OS_TYPE_TEXT = [
        self::ANDROID => '安卓',
        self::IOS => '苹果',
        self::PC => 'PC',
        self::MINI => '小程序',
        self::HARMONY => '鸿蒙',
    ];

    /**
     * 屏蔽包名
     *
     * @var array
     */
    const FILTER_PACKAGE_NAME = [
        6 => [
            'com.cahx.dream',
            'com.cahx.dream_mini',
            'com.cahx.chengfeng',
            'com.test.cahx',
            'com.cahx.lqcahx',
            'com.cahx.chengfenxx',
            'com.cahx.cahx_local2',
            'com.cahx.gw2',
        ],
    ];
}
