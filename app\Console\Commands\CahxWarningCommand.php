<?php

/**
 * 长安幻想预警脚本
 * @desc 长安幻想预警脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/15
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\BaseModel;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\WXGroupNoticeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CahxWarningCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cahx:warning';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '长安幻想预警脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        $list = ExceptionStreamAll::query()
            ->selectRaw("os_type, event_name, count(1) as num, count(distinct server_dev_str) as dev_num")
            ->where('extra_app_id', 6)
            ->whereIn('os_type', [1, 2])
            ->where('stream_date', date('Y-m-d'))
            ->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[6])
            ->groupBy('os_type', 'event_name')
            ->orderByRaw('os_type, event_name')
            ->getFromSR();

        $newList = [];
        foreach ($list as $item) {
            $newList[$item['os_type']][$item['event_name']] = $item;
        }

        // 推送
        foreach ($newList as $osType => $data) {
            // 判断启动设备是否大于5000
            if (($data['exception_start']['dev_num'] ?? 0) < 5000) {
                continue;
            }
            $keys = ['exception_crash', 'exception_error'];
            foreach ($keys as $key) {
                // 判断是否已经推送
                if (Cache::has($this->getKey($osType, $key))) {
                    continue;
                }
                $this->sendMessage($osType, $key, $data);
            }
        }
    }

    /**
     * 推送消息
     *
     * @param $osType
     * @param $data
     * @return void
     */
    private function sendMessage($osType, $key, $data)
    {
        $text = '';
        // 计算比例
        $crashPercent = round(($data[$key]['num'] ?? 0) / ($data['exception_start']['num'] ?? 1) * 100, 2);
        // 获取操作系统名称
        $osTypeText = $osType == 1 ? 'Android' : 'iOS';
        $date = date('Y年m月d H:i');
        // 崩溃次数率：2%，分系统推送。前置条件，联网设备数大于5000
        // 错误次数率：500%，分系统推送。前置条件，联网设备数大于5000
        if ($key == 'exception_crash' && $crashPercent >= 2) {
            $text = "长安性能预警 请各单位注意 \r\n\r\n<font color='red'>崩溃次数率高达{$crashPercent}%，请立即关注！行业标准仅为1%。</font>\r\n<font color='red'>{$osTypeText}系统。共统计设备{$data['exception_start']['dev_num']}台</font>\r\n\r\n报警时间：{$date}\r\n* 详情请查看效能后台。该报警同类型问题，一天只触发一次。";
        } elseif ($key == 'exception_error' && $crashPercent >= 500) {
            $text = "长安性能预警 请各单位注意 \r\n\r\n<font color='red'>错误次数率高达{$crashPercent}%（建议值500%）。{$osTypeText}系统。共统计设备{$data['exception_start']['dev_num']}台</font>\r\n\r\n报警时间：{$date}\r\n* 详情请查看效能后台。该报警同类型问题，一天只触发一次。";
        }

        if ($text) {
            $urls = [
                'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=552bd469-008e-48de-b232-4a5cb01a7943',
                'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d8ab1d6b-303c-45e8-8b21-8fddde2f8f15',
            ];
            foreach ($urls as $url) {
                $service = new WXGroupNoticeService($url, true);
                $service->wxGroupNotify($text, 'markdown');
            }
            Cache::put($this->getKey($osType, $key), 1, 86400);
        }
    }

    /**
     * 获取key
     *
     * @param $osType
     * @param $eventName
     * @return string
     */
    private function getKey($osType, $eventName)
    {
        return 'hitbug:cahx:warning:' . date('Ymd') . ":" . $osType . ":" . $eventName;
    }
}
