<?php

/**
 * 定时删除PDB脚本
 * @desc 定时删除PDB脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/30
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\Symbol;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ScheduledDelPdbCommand extends Command
{
    /**
     * 应用ID列表
     *
     * @var array
     */
    const IDS = [28];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scheduled:del:pdb';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '定时删除PDB脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        foreach (self::IDS as $id) {
            //获取要排除的版本
            $appVersion = Symbol::query()
                ->selectRaw('app_version, min(created_at) as created_at')
                ->where('developer_app_id', $id)
                ->where('os_type', 3)
                ->groupBy('app_version')
                ->orderByDesc('created_at')
                ->limit(100)
                ->get()
                ->toArray();
            //判断是否为空，为空则跳过此次循环
            if (empty($appVersion)) {
                continue;
            }
            //获取要删除的版本
            $list = Symbol::query()
                ->whereNotIn('app_version', array_column($appVersion, 'app_version'))
                ->where('developer_app_id', $id)
                ->where('os_type', 3)
                ->orderBy('created_at')
                ->get(['id', 'file_url', 'created_at']);
            //循环删除
            foreach ($list as $item) {
                //组装文件路径
                $filePath = '/data/www/developer/efficacy-manager/storage/app/public/files/pdb/' . basename($item['file_url']);
                //删除记录
                try {
                    $item->delete();
                } catch (\Exception $e) {
                }
                //打印日志
                Log::info("删除文件，应用ID：{$id}，ID：{$item['id']}，创建时间：{$item['created_at']}，路径：{$filePath}");
                $this->info("删除文件，应用ID：{$id}，ID：{$item['id']}，创建时间：{$item['created_at']}，路径：{$filePath}");
                //删除文件
                @unlink($filePath);
            }
        }
    }
}
