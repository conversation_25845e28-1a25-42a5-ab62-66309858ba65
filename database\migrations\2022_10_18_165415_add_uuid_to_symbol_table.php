<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUuidToSymbolTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->string('uuid', 255)->default('')->after('file_size')->comment('uuid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
    }
}
