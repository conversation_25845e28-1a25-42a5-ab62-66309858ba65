<?php

namespace App\Http\Controllers;

use App\Service\ExceptionHandleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ExceptionHandleController extends Controller
{
    /**
     * 异常记录状态变更
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3979
     * @param Request $request
     * @return JsonResponse
     */
    public function operate(Request $request)
    {
        try {
            // 参数校验
            // 传入developer_app_id、record_id、status
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'is_status_change' => 'required|integer',
                'record_id' => 'required|int',
                'exception_block_id' => 'required|string',
                'type' => 'required|int',
                // 'handlers' => 'required|array',
                // 'handlers.*.handler_id' => 'required|integer',
                // 'handlers.*.handler_name' => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            (new ExceptionHandleService($request->toArray()))->updateExceptionState();
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('异常记录状态变更接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * 获取最新异常记录处理状态
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3983
     * @param Request $request
     * @return JsonResponse
     */
    public function show(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'exception_block_id' => 'required|string'
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            $list = (new ExceptionHandleService($request->toArray()))->getExceptionState();
            return $this->response(0, compact('list'));
        } catch (\Exception $e) {
            Log::error('获取异常记录处理状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * 获取异常记录处理流程列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3985
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'exception_block_id' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            $list = (new ExceptionHandleService($request->toArray()))->getExceptionProcessList();
            return $this->response(0, compact('list'));
        } catch (\Exception $e) {
            Log::error('获取异常记录处理流程列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }
}
