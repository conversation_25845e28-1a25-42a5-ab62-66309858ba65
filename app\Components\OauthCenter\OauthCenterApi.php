<?php

namespace App\Components\OauthCenter;


use App\Components\Helper\CommonHelper;
use App\Components\HttpAgent;
use GuzzleHttp\Exception\GuzzleException;

class OauthCenterApi
{
    //一般key
    const KEY = 'oU0lD8GRVpvYfYUq6ensuQtHUkwtE0o3';

    /**
     * 微信 信息推送接口
     * @param string|array $username 工号 支持多个 |分割 sy0001|sy0002
     * @param string $message 消息内容 支持a标签和换行
     * @return array
     * @throws GuzzleException
     */
    public function sendWXMsg($username, string $message): array
    {
        is_array($username) && $username = join('|', $username);
        if (config('app.env') == 'production') {
            return $this->sendWXMsgFact($username, $message);
        } elseif (config('app.env') == 'local') {
            \Log::info("本地发送企业微信通知: username: {$username}, message: {$message}");
            return $this->sendWXMsgFact($username, $message);
        } else {
//            $message = "[测试数据: 抄送给: " . $username . "]" . $message;
//            $username = config('message.wx_test_username');
            return $this->sendWXMsgFact($username, $message);
        }
    }

    /**
     * 真正企微微信推送信息接口
     * @param $username
     * @param $message
     * @return array
     * @throws GuzzleException
     */
    public function sendWXMsgFact($username, $message): array
    {
        $time = time();
        $query = [
            'userid' => $username,
            'message' => $message,
            'dateTime' => $time,
            'sign' => md5($username . self::KEY . $time),
            'id' => 103,
        ];
        $url = $this->getUrl('push');
        //微信通知推送消息接口
        $ret = HttpAgent::getInstance()->request('GET', $url, [
            'query' => $query,
        ]);
        if (!$ret['success']) {
            \Log::error("发送企业微信请求失败: username: {$username}, message: {$message}");
            return $this->setResult(false, $ret['message']);
        }

        $code = json_decode($ret['message'], true);
        if (!$code['success']) {
            \Log::error("发送企业微信通知失败: username: {$username}, message: {$message}");
            return $this->setResult(false, $code['message']);
        }
        \Log::info("发送企业微信通知成功: username: {$username}, message: {$message}");
        return $this->setResult(true, $code['message']);
    }

    /**
     * 获取地址
     * @param string $api
     * @return string
     */
    private function getUrl(string $api): string
    {
        return 'https://oauthcenter.shiyuegame.com/' . $api;
    }

    /**
     * 格式话返回
     * @param bool $success
     * @param $message
     * @return array
     */
    private function setResult(bool $success, $message): array
    {
        return compact('success', 'message');
    }
}
