<?php

/**
 * 关键词过滤
 * @desc 关键词过滤
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/23
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception;

use App\Model\StarRocks\FilterKeyword;

class KeyWordFilter
{
    /**
     * 应用ID
     *
     * @var int
     */
    private $developerAppId;

    /**
     * 关键词
     *
     * @var array
     */
    private $keywords;

    /**
     * 时间
     *
     * @var string
     */
    private $date;

    /**
     * 初始化
     *
     * @param $developerAppId
     */
    public function __construct($developerAppId)
    {
        $this->developerAppId = $developerAppId;
    }

    /**
     * 获取不需要统计的关键词
     *
     * @return array
     */
    public function getNotStatKeyword(): array
    {
        $result = FilterKeyword::query()
            ->selectRaw('ARRAY_AGG(keyword) as keywords')
            ->where('developer_app_id', $this->developerAppId)
            ->where('is_stat', 0)
            ->firstFromSr();
        $this->keywords = json_decode($result['keywords'] ?? '[]', true);
        return $this->keywords;
    }

    /**
     * 设置日期
     *
     * @param $date
     * @return void
     */
    public function setDate($date)
    {
        $this->date = $date;
    }

    /**
     * 获取过滤的SQL
     *
     * @return string
     */
    public function getFilterSql(): string
    {
        // $keywords = json_encode($this->keywords, JSON_UNESCAPED_UNICODE);
        $where = '';
        if ($this->date) {
            $where = "and stream_date >= '{$this->date}'";
        }
        return "(upper(exception_block_id) not in (select upper(a.exception_block_id) as exception_block_id from (select extra_app_id, exception_block_id, unnest from exception_stream_keyword, unnest(keywords) AS unnest where array_length(keywords) > 0 and extra_app_id = {$this->developerAppId} {$where}) as a inner join (select developer_app_id, keyword from exception_filter_keyword where is_stat = 0 and developer_app_id = {$this->developerAppId}) as b on a.extra_app_id = b.developer_app_id and a.unnest = b.keyword group by a.exception_block_id))";
        // return "(upper(exception_block_id) not in (select upper(exception_block_id) from exception_stream_keyword where array_contains_all({$keywords}, keywords) and extra_app_id = {$this->developerAppId} {$where}))";
    }
}
