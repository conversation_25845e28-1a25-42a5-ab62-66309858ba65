<?php
/**
 * Created by PhpStorm.
 * User: zhanghaijia Email: <EMAIL>
 * Date: 2022/06/01 11:54
 */

namespace App\Components\ClickHouse;

use App\Components\Helper\Curl;
use App\Components\Helper\Rsa;
use App\Components\Redis\RedisHandler;
use App\Components\Redis\RedisKeyEnum;

class ClickHouse
{

    /**
     * 是否调试模式
     * @var bool
     */
    private $isDebug;

    public function __construct()
    {
        $this->isDebug = env("APP_ENV") != "production";
    }

    /**
     * 解析sql
     */
    public function getSqlBindings($query): string
    {
        $queryBindings = $query->getBindings();
        foreach ($queryBindings as $key => $val) {
            if (!is_int($val) && !is_float($val)) {
                $val = "'" . trim($val) . "'";
            }
            $queryBindings[$key] = $val;
        }
        // 因为%使用vsprintf会有问题, 所以需要转义
        $sql = str_replace('%', '%%', $query->toSql());
        // 替换?为%s
        $tmp = str_replace('?', '%s', $sql);
        //$tmp = str_replace('where', 'prewhere', $tmp);
        return vsprintf($tmp, $queryBindings);
    }

    /**
     * 获取clickhouse的数据的办法
     * @param string $sqlQuery
     * @return array
     */
    private function getClickHouseData(string $sqlQuery = '')
    {
        if (is_null($sqlQuery) || !$sqlQuery) return [];
        $redisKey = RedisKeyEnum::STRING['STRING:GetClickHouseData:'] . md5($sqlQuery);
        $ttl = RedisHandler::INIT['hotDataTimeout'];
        return RedisHandler::autoGet($redisKey, function()use($sqlQuery, &$ttl){
            if ($this->isDebug) \Log::info("clickhouse sql print : {$sqlQuery}");
            $result = Curl::post(config('clickHouse.get_data_warehouse_url'), json_encode([
                'query_sql' => Rsa::publicEncryptPart($sqlQuery, config('clickHouse.book_mark_public_key')),
                'sql' => base64_encode($sqlQuery),
                'query_date' => date('Y-m-d H:i:s', time())
            ]));
            if ($result !== false) {
                $result = json_decode($result, true);
                if (($result['code'] ?? -1) !== 0) {
                    \Log::error("sql execute failed : {$sqlQuery}");
                    $ttl = 1;//錯誤時則清除緩存
                }
            }
            return is_array($result) ? ($result['data'] ?? []) : [];
        }, $ttl);
    }

    /**
     * 对外的方法
     * 直接调用解析sql和获取数据
     * @param $query
     * @return array
     */
    public function getData($query)
    {
        return $this->getClickHouseData($this->getSqlBindings($query));
    }

    /**
     * 对外直接原生sql
     * @param $sql
     * @return array
     */
    public function getSqlData($sql)
    {
        return $this->getClickHouseData($sql);
    }

    /**
     * 并发查询执行
     * @param array $queries
     * @return array
     */
    public function getMultiSqlData(array $queries): array
    {
        return $this->multiGetData($queries);
    }

    /**
     * 并发查询
     * @param array $sqlQueries 子查询集合
     * @return array
     */
    public function multiGetData(array $sqlQueries): array
    {
        if (empty($sqlQueries)) return [];

        $key = config('clickHouse.book_mark_public_key');
        $url = config('clickHouse.get_data_warehouse_url');

        //组装并发请求内容
        $requests = [];
        foreach ($sqlQueries as $index => $sqlQuery) {
            $sqlQuery = $this->getSqlBindings($sqlQuery);
            if ($this->isDebug) \Log::info('clickhouse sql print: ' . $sqlQuery);
            $requests[$index] = [
                'url' => $url,
                'params' => json_encode([
                    'query_sql' => Rsa::publicEncryptPart($sqlQuery, $key),
                    'sql' => base64_encode($sqlQuery),
                    'query_date' => date('Y-m-d H:i:s', time())
                ])
            ];
        }

        $data = [];
        $result = Curl::multiRequest($requests);
        foreach ($result as $index => $value) {
            $value = json_decode($value, true);
            $data[$index] = isset($value['code']) && $value['code'] == 0 ? ($value['data'] ?? []) : [];
        }
        return $data;
    }
}
