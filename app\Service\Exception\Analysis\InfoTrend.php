<?php

/**
 * 详情趋势
 * @desc 详情趋势
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/09/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\Exception\BaseService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class InfoTrend extends BaseService
{
    /**
     * 获取趋势数据
     *
     * @param int $extraAppId
     * @param string $eventName
     * @param string $blockId
     * @return array
     */
    public function getTrend(int $extraAppId, string $eventName, string $blockId): array
    {
        $results = $this->getRelationData($extraAppId, $eventName, $blockId);
        return [
            '14_day_trend' => $this->handleDayTrend($blockId, $results['14_day_trend']),
            '24_hour_trend' => $this->handleHourTrend($blockId, $results['24_hour_trend']),
        ];
    }

    /**
     * 获取关联数据
     *
     * @param array $blockIds
     * @return array
     */
    private function getRelationData(int $extraAppId, string $eventName, string $blockId): array
    {
        // 14天趋势
        $queries['14_day_trend'] = ExceptionStreamAll::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, stream_date, count(1) as num')
            ->where('extra_app_id', $extraAppId)
            ->where('event_name', $eventName)
            ->where(DB::raw('upper(exception_block_id)'), $blockId)
            ->where('stream_date', '>=', Carbon::now()->subDays(13)->toDateString())
            ->where('stream_date', '<=', Carbon::now()->toDateString())
            ->groupBy(DB::raw('upper(exception_block_id)'), 'stream_date')
            ->orderBy(DB::raw('upper(exception_block_id)'))
            ->orderBy('stream_date');
        // 24小时趋势
        $queries['24_hour_trend'] = ExceptionStreamAll::query()
            ->selectRaw("upper(exception_block_id) as exception_block_id, from_unixtime(stream_time, '%Y-%m-%d %H') as stream_date, count(1) as num")
            ->where('extra_app_id', $extraAppId)
            ->where('event_name', $eventName)
            ->where(DB::raw('upper(exception_block_id)'), $blockId)
            ->where('stream_date', '>=', Carbon::now()->subHours(24)->toDateString())
            ->where('stream_time', '>=', Carbon::now()->subHours(24)->timestamp)
            ->groupBy(DB::raw('upper(exception_block_id)'), DB::raw("from_unixtime(stream_time, '%Y-%m-%d %H')"))
            ->orderBy(DB::raw('upper(exception_block_id)'))
            ->orderBy('stream_date');
        //多条并行查询
        return (new ClickHouse())->getMultiSqlData($queries);
    }

    /**
     * 处理趋势数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleDayTrend($blockId, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = [];
        foreach ($data as $value) {
            $dataIndexedById[$value['exception_block_id']][] = $value;
        }
        $statData = isset($dataIndexedById[$blockId]) ? $dataIndexedById[$blockId] : [];
        // 用日期作为key
        $statData = array_column($statData, null, 'stream_date');
        // 判断是否存在没有日期的数据
        $startDate = Carbon::now()->subDays(13)->toDateString();
        $endDate = Carbon::now()->toDateString();
        $newStatData = [];
        while ($startDate <= $endDate) {
            // 判断是否存在
            if (!isset($statData[$startDate])) {
                $newStatData[$startDate] = [
                    'exception_block_id' => $blockId,
                    'stream_date' => $startDate,
                    'num' => '0',
                ];
            } else {
                $newStatData[$startDate] = $statData[$startDate];
            }
            $startDate = Carbon::parse($startDate)->addDay()->toDateString();
        }
        return array_values($newStatData);
    }

    /**
     * 处理趋势数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleHourTrend($blockId, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = [];
        foreach ($data as $value) {
            $dataIndexedById[$value['exception_block_id']][] = $value;
        }

        $statData = isset($dataIndexedById[$blockId]) ? $dataIndexedById[$blockId] : [];
        // 用日期作为key
        $statData = array_column($statData, null, 'stream_date');
        // 判断是否存在没有日期的数据
        $startDate = Carbon::now()->subHours(24)->timestamp;
        $endDate = Carbon::now()->timestamp;
        $newStatData = [];
        while ($startDate <= $endDate) {
            $date = date('Y-m-d H', $startDate);
            // 判断是否存在
            if (!isset($statData[$date])) {
                $newStatData[$date] = [
                    'exception_block_id' => $blockId,
                    'stream_date' => $date,
                    'num' => '0',
                ];
            } else {
                $newStatData[$date] = $statData[$date];
            }
            // 处理时间把 2024-09-24 11 转为 2024-09-24 11:00~11:59
            $newStatData[$date]['stream_date'] = $newStatData[$date]['stream_date'] . ':00~' . date('H:i', strtotime($newStatData[$date]['stream_date'] . ':59'));
            $startDate = Carbon::parse($startDate)->addHours()->timestamp;
        }
        return array_values($newStatData);
    }
}
