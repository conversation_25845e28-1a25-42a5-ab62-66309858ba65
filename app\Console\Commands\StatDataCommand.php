<?php

/**
 * 统计数据脚本
 * @desc 统计数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionStatAllV2;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\StarRocksService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StatDataCommand extends Command
{
    /**
     * 统计时间间隔
     *
     * @var int
     */
    public const TIME = 5;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stat:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '统计数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            // 获取
            $maxStreamTime = ExceptionStatAllV2::query()->selectRaw('max(max_stream_time) as max_stream_time')->firstFromSR();
            // 上次同步时间
            $lastTime = ($maxStreamTime['max_stream_time'] ?? 600) - 600;
            // 日期时间
            $dateTime = date('Y-m-d', $lastTime);
            //打印日志
            Log::info("执行统计数据脚本开始, dateTime: {$dateTime}, lastTime: " . date('Y-m-d H:i:s', $lastTime));
            // 内部子查询
            $subQuery = ExceptionStreamAll::query()
                ->select('exception_block_id')
                ->where('stream_time', '>=', $lastTime)
                ->whereNotNull('exception_block_id')
                ->where('exception_block_id', '!=', '')
                ->where('stream_date', '>=', $dateTime)
                ->groupBy('exception_block_id');
            // 保存统计的数据
            $data = ExceptionStreamAll::query()
                ->joinSub($subQuery, 'b', function ($join) {
                    $join->on('exception_stream_all.exception_block_id', '=', 'b.exception_block_id');
                })
                ->whereNotNull('exception_stream_all.extra_app_id')
                ->where('exception_stream_all.extra_app_id', '!=', '')
                ->whereNotNull('exception_stream_all.event_name')
                ->where('exception_stream_all.event_name', '!=', '')
                ->whereNotNull('exception_stream_all.server_dev_str')
                ->where('exception_stream_all.server_dev_str', '!=', '')
                ->where('exception_stream_all.stream_date', '>=', $dateTime)
                ->groupBy('exception_stream_all.stream_date', 'exception_stream_all.extra_app_id', 'exception_stream_all.exception_block_id', 'exception_stream_all.event_name', 'exception_stream_all.server_dev_str')
                ->select('exception_stream_all.extra_app_id', 'exception_stream_all.exception_block_id', 'exception_stream_all.event_name', 'exception_stream_all.stream_date', DB::raw("CASE WHEN `exception_stream_all`.`server_dev_str` = '' OR `exception_stream_all`.`server_dev_str` IS NULL THEN `exception_stream_all`.`event_name` ELSE `exception_stream_all`.`server_dev_str` END as server_dev_str"), DB::raw('min(stream_time) as min_stream_time'), DB::raw('max(stream_time) as max_stream_time'), DB::raw('count(*) as count'))
                ->getSql();
            // 执行SQL
            (new StarRocksService())->execute("insert into exception_stat_all_view {$data}");
            // sql语句
            $sql = <<<SQL
insert into exception_stat_all_v2
select extra_app_id,
       upper(exception_stat_all_view.exception_block_id) as exception_block_id,
       event_name,
       min(min_stream_time)           as min_stream_time,
       max(max_stream_time)           as max_stream_time,
       count(distinct server_dev_str) as server_dev_str_count,
       sum(count)                     as count
from exception_stat_all_view
         inner join (select exception_block_id
                     from exception_stat_all_view
                     where max_stream_time >= {$lastTime}
                     group by exception_block_id) b on upper(b.exception_block_id) = upper(exception_stat_all_view.exception_block_id)
group by extra_app_id, upper(exception_stat_all_view.exception_block_id), event_name
SQL;
            // 同步数据表
            (new StarRocksService())->execute($sql);
            //打印日志
            Log::info("执行统计数据脚本完成, dateTime: {$dateTime}, lastTime: " . date('Y-m-d H:i:s', $lastTime));
        } catch (\Exception $e) {
            Log::error("执行统计数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
