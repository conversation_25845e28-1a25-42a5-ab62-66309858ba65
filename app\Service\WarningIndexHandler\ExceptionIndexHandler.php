<?php

/**
 * （崩溃/错误）维度预警
 * @desc （崩溃/错误）维度预警
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\WarningIndexHandler;

use App\Components\ClickHouse\ClickHouse;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Warning;
use Carbon\Carbon;

class ExceptionIndexHandler extends WarningIndexHandlerBase
{
    /**
     * 异常类型
     *
     * @var int
     */
    private $exceptionType;

    public function __construct(int $exceptionType, Warning $warning)
    {
        parent::__construct($warning);
        $this->exceptionType = $exceptionType;
    }

    /**
     * 查询数据
     *
     * @return array
     */
    public function getData(): array
    {
        return (new ClickHouse())->getData($this->getBuilder());
    }

    /**
     * 查询构造器
     */
    protected function getBuilder()
    {
        $eventName = 'exception_' . Warning::EXCEPTION_TYPE[$this->exceptionType];
        $builder = $this->getBaseBuilder()->where('event_name', $eventName);
        //判断是否有监控范围
        if ($this->warning->monitor_range == 0) {
            return $builder;
        }

        //判断 monitor_range 为 7 时，需要特殊处理
        if ($this->warning->monitor_range == 7) {
            $builder->where(function ($query) {
                //循环关键词数据
                foreach ($this->warning->exception_keywords as $keyword) {
                    $this->explainWhere($query, $keyword);
                }
            });
            return $builder;
        }
        //组装子SQL
        $subSql = $this->getSubSql($eventName);
        return $builder->whereRaw("upper(exception_block_id) in (SELECT upper(exception_block_id) FROM ({$subSql}) AS t ORDER BY num desc,exception_block_id desc limit {$this->getMonitorRange($this->warning->monitor_range)})");
    }

    /**
     * 获取子SQL
     *
     * @param $eventName
     * @return string
     */
    private function getSubSql($eventName): string
    {
        $now = Carbon::now();
        $startTime = (clone $now)->subHours()->startOfHour()->timestamp;
        $endTime = (clone $now)->startOfHour()->timestamp;
        $streamDate = date('Y-m-d', $startTime);
        $streamEndDate = date('Y-m-d', $endTime);
        $builder = (new UserLogDataAll())
            ->selectRaw('exception_block_id, count(*) as num')
            ->where('stream_date', '>=', $streamDate)
            ->where('stream_date', '<=', $streamEndDate)
            ->where('stream_time', '>=', $startTime)
            ->where('stream_time', '<', $endTime)
            ->where('event_name', $eventName)
            ->when($this->warning->app_version, function ($query, $appVersion) {
                return $query->whereIn('app_version', $appVersion);
            })
            ->when($this->warning->os_type, function ($query, $osType) {
                return $query->where('os_type', $osType);
            })
            ->where('extra_app_id', $this->warning->developer_app_id)
            ->where('server_dev_str', '!=', '')
            ->whereRaw("(exception_block_id not in (select exception_block_id from (select exception_block_id, unnest as keyword from exception_stream_keyword, UNNEST(keywords) where extra_app_id = {$this->warning->developer_app_id} and event_name = 'exception_error' and stream_date >= '{$streamDate}' and stream_date <= '{$streamEndDate}' group by exception_block_id, keyword) t inner join (select keyword from exception_filter_keyword where developer_app_id = '{$this->warning->developer_app_id}') k on t.keyword = k.keyword group by exception_block_id))")
            ->groupBy('exception_block_id');

        if ($this->warning->stream_time_filter == 1 && $this->warning->stream_time_filter_hours != 0) {
            $builder = $builder->whereRaw(sprintf('upper(exception_block_id) in (select upper(exception_block_id) from exception_stat_all_v2 where min_stream_time >= %d)', strtotime("-{$this->warning->stream_time_filter_hours} hours")));
        }
        $baseQuery = (new ClickHouse())->getSqlBindings($builder);

        return $baseQuery;
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        $columns = ['explain_desc', 'subject_name'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }

    /**
     * 获取监控范围
     *
     * @param $value
     * @return int
     */
    private function getMonitorRange($value): int
    {
        switch ($value) {
            case 1:
                return 5;
            case 2:
                return 10;
            case 3:
                return 20;
            case 4:
                return 30;
            case 5:
                return 50;
            case 6:
                return 100;
        }
        return 100;
    }
}
