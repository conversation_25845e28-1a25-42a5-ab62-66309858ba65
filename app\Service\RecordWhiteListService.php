<?php

namespace App\Service;

use App\Components\Scope\CrashScope;
use App\Http\Logic\ExceptionLogic;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Record;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;

class RecordWhiteListService extends WhiteListService
{
    /**
     * 排序字段
     *
     * @var string
     */
    protected $sortField = 'block_num';

    /**
     * 排序类型
     *
     * @var string
     */
    protected $sortType = 'desc';

    /**
     * @var CrashScope
     */
    private $scope;
    /**
     * @var UserLogDataAll
     */
    private $query;

    /**
     * 异常记录白名单更新类型
     */
    const ADD_LIST = 0; // 隐藏操作
    const DELETE_LIST = 1; // 显示操作

    public function __construct($params)
    {
        parent::__construct($params);
        $this->scope = new CrashScope($params);
        $this->query = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query();
        isset($this->params['sort_field']) && $this->sortField = $params['sort_field'];
        isset($this->params['sort_type']) && $this->sortType = $params['sort_type'];
    }

    /**
     * 获取异常白名单列表
     * @return array
     */
    public function recordsWhiteList()
    {
        $statFilter = array();
        // 查询mysql获取白名单内的异常记录
        $statFilter['exception_block_id'] = $this->getRecordExceptionBlockIds();
        //判断数据是否为空
        if (empty($statFilter['exception_block_id'])) return [
            'total' => 0,
            'list' => [],
        ];
        //$statFilter['exception_block_id'] 转为大写
        $statFilter['exception_block_id'] = array_map('strtoupper', $statFilter['exception_block_id']);
        //不为空才往下走
        if (!empty($this->params['os_type'])) {
            $statFilter['os_type'] = $this->params['os_type'];
        }
        $eventName = ExceptionLogic::EVENT_NAME[$this->params['type'] ?? 1];
        [$total, $list] = (new ExceptionLogic())->pullExceptionList(
            $this->params['developer_app_id'],
            $eventName,
            [],
            $statFilter,
            [],
            '',
            [],
            [],
            $this->sortField,
            $this->sortType, $this->page, $this->limit);
        return compact('total', 'list');
    }

    /**
     * 更新异常记录表的白名单状态
     */
    public function updated()
    {
        $exceptionBlockIds = explode(",", $this->params['exception_block_ids']);
        foreach ($exceptionBlockIds as $exceptionBlockId) {
            $record = Record::query()
                ->where('developer_app_id', $this->params['developer_app_id'])
                ->where('exception_block_id', $exceptionBlockId)
                ->first();
            // 更新白名单状态
            if ($this->params['operate_type'] == self::ADD_LIST) {
                $record->is_add_white_list = 1;
            } else if ($this->params['operate_type'] == self::DELETE_LIST) {
                $record->is_add_white_list = 0;
            }
            $record->save();
        }
    }

    /**
     * 查询mysql获取白名单内的异常记录
     * @return array
     */
    public function getRecordExceptionBlockIds()
    {
        return Record::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('is_add_white_list', 1)
            ->where('type', $this->scope->type)
            ->when(!empty($this->scope->osType), function ($query) {
                $query->where('os_type', $this->scope->osType);
            })
            ->pluck('exception_block_id')
            ->toArray();
    }

    /**
     * 将$records中的record_id 填入 $list
     * @param $records
     * @param $list
     * @return
     */
    public function handle($records, $list)
    {
        $recordsMap = [];
        foreach ($records as $record) {
            $key = $record['event_name'] . $record['category'] . $record['os_type'] . $record['name'] . $record['explain'];
            $recordsMap[$key] = $record['record_id'];
        }

        foreach ($list as &$listItem) {
            $key = $listItem['event_name'] . $listItem['category'] . $listItem['os_type'] . $listItem['name'] . $listItem['explain'];
            if (isset($recordsMap[$key])) {
                $listItem['record_id'] = $recordsMap[$key];
            }
        }
        return $list;
    }
}


