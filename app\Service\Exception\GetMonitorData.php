<?php

/**
 * 获取监控数据
 * @desc 获取监控数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/05/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception;

use App\Model\StarRocks\ExceptionStreamAll;
use Carbon\Carbon;

class GetMonitorData
{
    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        $current = [$this->params['start_time'], $this->params['end_time']];
        $chartData = $this->getChartData($current);
        return [
            'chartData' => $chartData
        ];
    }

    /**
     * 获取折线图数据
     * 按资源版本统计监控指标
     *
     * @param array $dates 日期范围，格式为 [$startDate, $endDate]
     * @return array 按资源版本分组的监控数据
     */
    public function getChartData(array $dates)
    {
        // 修改开始时间，调整为-15天
        $startDate = Carbon::parse($this->params['start_time'])->subDays(15)->toDateTimeString();
        $dates = [$startDate, $this->params['end_time']];

        $result = ExceptionStreamAll::query() // 查询性能分数表
            ->selectRaw("event_name, inner_version, count(1) as count, count(distinct server_dev_str) as dev_str_count")
            ->whereIn('event_name', ['exception_crash', 'exception_error', 'exception_start'])
            ->whereBetween("stream_time", [strtotime($dates[0]), strtotime($dates[1])]) // 过滤掉不在时间范围内的数据
            ->whereBetween("stream_date", [Carbon::parse($dates[0])->toDateString(), Carbon::parse($dates[1])->toDateString()]) // 过滤掉不在时间范围内的数据
            ->where("extra_app_id", $this->params['developer_app_id']) // 只获取当前效能后台Id的数据
            ->when(!empty($this->params['os_type']), function ($json) { // 如果有传平台值，过滤掉不是当前平台的数据
                $json->where("os_type", $this->params['os_type']);
            })
            ->where("is_emulator", 0) // 过滤掉模拟器数据
            ->where("inner_version", '!=', '') // inner_version 不为空
            ->groupBy("event_name", "inner_version") // 按资源版本分组
            ->getFromSR();

        // 遍历数组，将 inner_version 相同的值，合并到一起
        $newResult = [];
        foreach ($result as $item) {
            $newResult[$item['inner_version']][$item['event_name']] = $item;
        }

        // 计算崩溃率和错误率
        $result = [];
        foreach ($newResult as $innerVersion => $item) {
            $startCount = 1;
            if (!empty($item['exception_start']['count'])) {
                $startCount = $item['exception_start']['count'];
            }
            $result[$innerVersion]['crash_rate'] = bcadd(round((($item['exception_crash']['count'] ?? 0) / $startCount) * 100, 2), 0, 2);
            $result[$innerVersion]['error_rate'] = bcadd(round((($item['exception_error']['count'] ?? 0) / $startCount) * 100, 2), 0, 2);
        }

        // 根据key进行排序，如果key有小数点则替换成空字符串再进行排序，倒序
        $tempResult = [];
        foreach ($result as $innerVersion => $item) {
            // 保存原始键以便在排序后恢复
            $cleanKey = str_replace('.', '', $innerVersion);
            $tempResult[$cleanKey] = [
                'original_key' => $innerVersion,
                'data' => $item
            ];
        }

        // 使用数值排序，将字符串转换为数字后进行比较
        // 对于纯数字键和日期格式键都能正确排序
        uksort($tempResult, function ($a, $b) {
            // 尝试将键转换为数字进行比较
            $numA = is_numeric($a) ? (float)$a : 0;
            $numB = is_numeric($b) ? (float)$b : 0;
            return $numB <=> $numA; // 使用太空船操作符进行倒序排序
        });

        // 恢复原始结构
        $result = [];
        foreach ($tempResult as $item) {
            $result[$item['original_key']] = $item['data'];
        }

        // 只获取前50个，保留原始键
        return array_slice($result, 0, 50, true);
    }
}
