<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSymbolTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('symbol', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedTinyInteger('type')->default(0)->comment('异常类型 1为崩溃异常');
            $table->string('app_version', 64)->comment('app版本');
            $table->string('file_name', 128)->comment('文件名');
            $table->string('file_md5', 128)->comment('md5');
            $table->string('file_url', 255)->comment('文件下载地址');
            $table->unsignedInteger('file_size')->default(0)->comment('文件大小');
            $table->timestamps();
        });
        \DB::connection('exception')->statement("ALTER TABLE `symbol` comment '异常符号表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('symbol');
    }
}
