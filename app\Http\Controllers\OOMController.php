<?php

/**
 * 崩溃OOM控制器
 * @desc 崩溃OOM控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/02/20
 */

namespace App\Http\Controllers;

use App\Components\Scope\CrashScope;
use App\Service\Exception\Analysis\OOMException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OOMController extends BaseController
{
    /**
     * @var CrashScope
     */
    protected $scope;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->scope = new CrashScope($request->toArray());
    }

    /**
     * OOM崩溃列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15601
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        try {
            $order = [
                'stream_time' => 'stream_time',
                'ram' => 'ram',
                'foot_print_memory' => 'foot_print_memory',
                'app_version' => 'app_version',
                'os_version' => 'os_version',
                'duration' => 'duration',
            ];
            $orderField = $order[$this->scope->sortField] ?? 'stream_time';
            $orderSequence = $this->scope->sortType ?? 'DESC';
            $pageIndex = $request->page;
            $pageLimit = $request->per_page;
            [$total, $list] = (new OOMException($request))->getList($orderField, $orderSequence, $pageIndex, $pageLimit);
            return $this->response(0, compact('total', 'list'));
        } catch (\Exception $e) {
            Log::error('OOM崩溃列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * OOM崩溃统计
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15602
     * @param Request $request
     * @return JsonResponse
     */
    public function statistic(Request $request)
    {
        try {
            $data = (new OOMException($request))->getStatistic();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            Log::error('OOM崩溃统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }
}
