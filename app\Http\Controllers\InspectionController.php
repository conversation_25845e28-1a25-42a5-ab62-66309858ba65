<?php

/**
 * 性能质量巡检配置
 * @desc 性能质量巡检配置
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Model\InspectionConfig;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class InspectionController extends BaseController
{
    /**
     * 新增接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15135
     * @return JsonResponse
     * @throws Exception
     */
    public function add(): JsonResponse
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|int',
            'os_type' => 'required|int',
            'category' => 'required|int',
            'trigger_time' => 'required|string',
            'day' => 'int',
            'status' => 'required|int',
            'webhook_url' => 'array',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors()->first());
        }

        try {
            //判断是否存在
            $exists = InspectionConfig::query()
                ->where('developer_app_id', $this->request['developer_app_id'])
                ->where('os_type', $this->request['os_type'])
                ->where('category', $this->request['category'])
                ->exists();
            if ($exists) {
                return $this->response(1000, [], '该巡检配置已存在');
            }
            //保存
            InspectionConfig::query()
                ->create([
                    'trigger_time' => $this->request['trigger_time'],
                    'day' => $this->request['day'] ?? 0,
                    'webhook_url' => $this->request['webhook_url'],
                    'status' => $this->request['status'],
                    'developer_app_id' => $this->request['developer_app_id'],
                    'os_type' => $this->request['os_type'],
                    'category' => $this->request['category'],
                    'creator' => auth()->user()->alias,
                    'editor' => auth()->user()->alias,
                ]);
            //返回
            return $this->response();
        } catch (Exception $e) {
            Log::error('保存巡检配置接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
        return $this->response(1005);
    }

    /**
     * 编辑巡检配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15137
     * @return JsonResponse
     * @throws Exception
     */
    public function edit(): JsonResponse
    {
        $validator = Validator::make($this->request, [
            'id' => 'required|int',
            'trigger_time' => 'required|string',
            'day' => 'int',
            'webhook_url' => 'array',
            'status' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors()->first());
        }

        try {
            InspectionConfig::query()
                ->where('id', $this->request['id'])
                ->update([
                    'trigger_time' => $this->request['trigger_time'],
                    'day' => $this->request['day'] ?? 0,
                    'webhook_url' => $this->request['webhook_url'],
                    'status' => $this->request['status'],
                    'editor' => auth()->user()->alias,
                ]);
            // 返回
            return $this->response();
        } catch (Exception $e) {
            Log::error('编辑巡检配置接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
        return $this->response(1005);
    }

    /**
     * 巡检配置列表接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15136
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors()->first());
        }
        try {
            $list = InspectionConfig::query()->where('developer_app_id', $this->request['developer_app_id'])
                ->orderByDesc('id')
                ->get();
            return $this->response(0, $list->all());
        } catch (Exception $e) {
            Log::error('巡检配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
        return $this->response(1005);
    }

    /**
     * 删除预警接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15139
     * @return JsonResponse
     * @throws Exception
     */
    public function delete(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors()->first());
        }

        try {
            InspectionConfig::query()->where('id', $this->request['id'])->delete();
            return $this->response();
        } catch (Exception $e) {
            Log::error('删除巡检配置接口报错-id:' . $this->request['id'] . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
        }
        return $this->response(1005);
    }

    /**
     * 修改巡检配置状态
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15138
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors()->first());
        }

        try {
            InspectionConfig::query()->where('id', $this->request['id'])
                ->update(['status' => $this->request['status'], 'editor' => auth()->user()->alias]);
            return $this->response();
        } catch (Exception $e) {
            Log::error('修改巡检配置状态接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
        }
        return $this->response(1005);
    }
}
