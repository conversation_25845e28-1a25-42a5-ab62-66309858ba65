<?php

/**
 * 异常处理人巡检配置表模型
 * @desc 异常处理人巡检配置表模型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/09
 */

namespace App\Model;

class ExceptionHandlerWebhook extends BaseModel
{
    use ModelTrait;

    protected $table = 'exception_handler_webhook';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'frequency',
        'range',
        'webhook_urls',
        'status',
        'sort_field',
        'operator'
    ];

    protected $casts = [
        'webhook_urls' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
