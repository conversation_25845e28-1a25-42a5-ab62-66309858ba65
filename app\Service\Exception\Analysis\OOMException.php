<?php

/**
 * OOM异常分析列表数据
 * @desc OOM异常分析列表数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/02/20
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Model\BaseModel;
use App\Model\ClickHouse\Crash;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\ExceptionHandler;
use App\Model\Record;
use App\Model\StarRocks\FilterKeyword;
use App\Model\StarRocks\StarRocksDB;
use App\Service\Exception\BaseService;
use App\Service\Exception\KeyWordSearch;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OOMException extends BaseService
{
    use KeyWordSearch;

    /**
     * HTTP请求对象
     *
     * @var Request
     */
    private $request;
    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;
    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;
    /**
     * 时间范围
     *
     * @var array
     */
    private $timeRange;
    /**
     * 上报时间
     *
     * @var array
     */
    private $streamTime;

    /**
     * 原始上报时间
     * @var string
     */
    private $rawStartDate;

    /**
     * 原始上报时间（end）
     * @var string
     */
    private $rawEndDate;
    /**
     * 最后上报时间
     *
     * @var array
     */
    private $lastStreamTime;
    /**
     * 首次上报时间
     *
     * @var array
     */
    private $firstStreamTime;
    /**
     * 过滤条件数组
     *
     * @var array
     */
    private $filters;
    /**
     * 标签
     *
     * @var string
     */
    private $labelName = '';
    /**
     * 处理人ID
     *
     * @var array
     */
    private $handlerId = [];
    /**
     * 处理状态
     *
     * @var array
     */
    private $handlerStatus = [];
    /**
     * 是否剔除白名单
     *
     * @var int
     */
    private $isFilter = 0;
    /**
     * 崩溃时长
     *
     * @var int
     */
    private $useDuration;

    /**
     * 关联录屏
     *
     * @var int
     */
    private $relateRecord;

    /**
     * 关联性能
     *
     * @var int
     */
    private $relatePerf;

    /**
     * 关联TAPD
     *
     * @var int
     */
    private $relateTapd;

    /**
     * 需要过滤的关键词ID列表
     *
     * @var array
     */
    private $filterKeywordIds = [];

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->request = $request;
        $this->initParameter();
    }

    /**
     * 初始化参数
     *
     * @return void
     */
    private function initParameter()
    {
        $filters = $this->getFilters();
        //获取数据进行处理
        foreach ($filters as $item) {
            foreach ($item['columns'] as $key => $column) {
                //在请求参数中获取
                $value = $this->request->input($key);
                //null、空字符串、空数组跳过
                if ($value === null || $value === '' || $value === []) {
                    continue;
                }
                //赋值
                $this->filters[$column] = $item['callback']($value);
            }
        }
        //os_type要特殊处理，如果为0，不需要查询
        if (isset($this->filters['os_type']) && $this->filters['os_type'] == 0) {
            unset($this->filters['os_type']);
        }
        //效能后台ID
        $this->extraAppId = $this->request->input('developer_app_id');
        //时间名称
        $this->eventName = self::EVENT_NAME[$this->request->input('type')];
        //获取时间参数
        $starTime = $this->request->input('start_time');
        $endTime = $this->request->input('end_time');
        $this->rawStartDate = $starDate = $this->request->input('start_date');
        $this->rawEndDate = $endDate = $this->request->input('end_date');
        $firstStartDate = $this->request->input('first_start_date');
        $firstEndDate = $this->request->input('first_end_date');
        $lastStartDate = $this->request->input('last_start_date');
        $lastEndDate = $this->request->input('last_end_date');
        //上报时间
        if ($starTime && $endTime) {
            $this->timeRange = [strtotime($starTime), strtotime($endTime)];
        }
        //上报时间
        if ($starDate && $endDate) {
            $this->streamTime = [strtotime($starDate), strtotime($endDate)];
        }
        //首次上报时间
        if ($firstStartDate && $firstEndDate) {
            $this->firstStreamTime = [strtotime($firstStartDate), strtotime($firstEndDate)];
        }
        //最后上报时间
        if ($lastStartDate && $lastEndDate) {
            $this->lastStreamTime = [strtotime($lastStartDate), strtotime($lastEndDate)];
        }
        //标签
        if ($this->request->input('label_name')) {
            $this->labelName = $this->request->input('label_name');
        }
        //处理人
        if ($this->request->input('handler')) {
            $this->handlerId = explode(',', $this->request->input('handler'));
        }
        //处理状态
        if ($this->request->input('issue_status')) {
            $this->handlerStatus = explode(',', $this->request->input('issue_status'));
        }
        //是否剔除白名单
        if ($this->request->input('is_filter')) {
            $this->isFilter = intval($this->request->input('is_filter'));
        }
        //奔溃时长
        if ($this->request->input('use_duration')) {
            $this->useDuration = intval($this->request->input('use_duration'));
        }
        //需要过滤的关键词ID列表
        if ($this->request->input('filter_keyword_ids')) {
            $this->filterKeywordIds = json_decode($this->request->input('filter_keyword_ids'), true);
        }
        // 是否关联录屏
        if ($this->request->input('relate_record')) {
            $this->relateRecord = intval($this->request->input('relate_record'));
        }
        // 是否关联性能
        if ($this->request->input('relate_perf')) {
            $this->relatePerf = intval($this->request->input('relate_perf'));
        }
        // 是否关联TAPD
        if ($this->request->input('relate_tapd')) {
            $this->relateTapd = intval($this->request->input('relate_tapd'));
        }
    }

    /**
     * 获取过滤的规则
     *
     * @return array[]
     */
    private function getFilters(): array
    {
        return [
            [
                'columns' => [
                    'crash_type' => 'type', //崩溃类型
                    'app_version' => 'app_version', //APP版本
                    'channel' => 'release_store', //发布渠道
                    'app_page' => 'current_page_title', //APP页面
                    'os_version' => 'os_version', //系統版本
                    'device_model' => 'device_model', //机型
                    'sdk_version' => 'version', //SDK版本
                    'manufacturer' => 'manufacturer', //厂商
                    'sdk_package_name' => 'sdk_package_name', //SDK包名
                    'inner_version' => 'inner_version', //游戏资源版本
                ],
                'callback' => function ($value) {
                    return json_decode($value, true);
                },
            ],
            [
                'columns' => [
                    'is_emulator' => 'is_emulator', //是否模擬器
                    'server_dev_str' => 'server_dev_str', //设备ID
                    'account_id' => 'account_id', //账号ID
                    'role_id' => 'role_id', //角色ID
                    'role_name' => 'role_name', //角色名称
                    'exception_block_id' => 'exception_block_id', //异常ID
                    'origin_stacks' => 'explain', //【崩溃/错误】詳情
                    'server_id' => 'server_id', //服务器ID
                ],
                'callback' => function ($value) {
                    return $value;
                },
            ],
            [
                'columns' => [
                    'os_type' => 'os_type', //系统版本 all/android/ios/pc
                ],
                'callback' => function ($value) {
                    return strval($value);
                },
            ],
            [
                'columns' => [
                    'break_status' => 'is_success', //是否越獄，值：0否，1是
                    'console_status' => 'operate_status', //是否前台，值：0前台，1后台
                ],
                'callback' => function ($value) {
                    return intval($value);
                },
            ],
        ];
    }

    /**
     * 获取列表
     *
     * @param $orderField
     * @param $orderSequence
     * @param $pageIndex
     * @param $pageLimit
     * @return array
     */
    public function getList($orderField, $orderSequence, $pageIndex, $pageLimit): array
    {
        $ClickHouse = new ClickHouse();
        //other[END]
        //拼装并行查询sql
        $queries['total'] = $this->getSubSql(true, "count(*) AS `total`");
        $queries['list'] = $this->getSubSql(true, "upper(exception_block_id) as exception_block_id, os_type, type as category, event_name, explain_desc, subject_name as name, from_unixtime(stream_time, '%Y-%m-%d %H:%i:%s') as stream_time, account_id, server_dev_str, exception_merge_id, app_version, os_version, CAST(get_json_object(basic_info_json, 'use_duration') AS BIGINT) as duration, CAST(get_json_object(memory_info_json,'total_memory') AS BIGINT) as ram, CAST(get_json_object(memory_info_json, 'app_used_memory') AS BIGINT) as foot_print_memory")
            ->orderBy($orderField, $orderSequence)
            ->skip(($pageIndex - 1) * $pageLimit - 1)
            ->limit($pageLimit);
        //多条并行查询
        $results = $ClickHouse->getMultiSqlData($queries);
        $total = (int)($results['total'][0]['total'] ?? 0);
        $list = $results['list'] ?? [];
        $format = [];
        if ($list) {
            $format = $this->format($list);
        }
        return [
            $total,
            $format,
        ];
    }

    /**
     * 获取统计
     *
     * @return array
     */
    public function getStatistic(): array
    {
        $ClickHouse = new ClickHouse();
        //other[END]
        //拼装并行查询sql
        $queries['os_type'] = $this->getSubSql(true, "os_type, count(1) as value")->groupBy('os_type');
        $queries['app_version'] = $this->getSubSql(true, "app_version, count(1) as value")->groupBy('app_version');
        $queries['os_version'] = $this->getSubSql(true, "os_version, os_type, count(1) as value")->groupBy('os_version', 'os_type');
        $queries['device_model'] = $this->getSubSql(true, "device_model, count(1) as value")->groupBy('device_model');
        $queries['ram'] = DB::table(DB::raw("(" . $this->getSubSql(false, "ROUND(get_json_object(memory_info_json,'total_memory') / (1024*1024*1024), 2) as ram") . ") t"))->selectRaw("ram, count(1) as value")->whereNotNull('ram')->groupBy('ram');
        $queries['duration'] = DB::table(DB::raw("(" . $this->getSubSql(false, "get_json_object(basic_info_json, 'use_duration') as duration") . ") t"))->selectRaw("CASE WHEN CAST(duration AS BIGINT) BETWEEN 0 AND 300 THEN '5' WHEN CAST(duration AS BIGINT) BETWEEN 301 AND 900 THEN '5-15' WHEN CAST(duration AS BIGINT) BETWEEN 901 AND 1800 THEN '15-30' WHEN CAST(duration AS BIGINT) BETWEEN 1801 AND 3600 THEN '30-60' ELSE '60' END AS duration_range, COUNT(1) AS value, SUM(CAST(duration AS BIGINT)) AS total_duration")->whereNotNull('duration')->groupBy('duration_range');
        //多条并行查询
        $results = $ClickHouse->getMultiSqlData($queries);
        // 计算  avg_duration, total_duration相加除以count相加，不需要数组，直接计算
        $totalCount = array_sum(array_column($results['duration'] ?? [], 'value'));
        $totalDuration = array_sum(array_column($results['duration'] ?? [], 'total_duration'));
        $avgDuration = empty($totalCount) ? 0 : bcadd(round($totalDuration / $totalCount, 2), 0, 2);
        // 返回结果
        return [
            'os_type' => array_map(function ($item) {
                return [
                    'key' => $item['os_type'],
                    'value' => intval($item['value']),
                ];
            }, $results['os_type'] ?? []),
            'app_version' => array_map(function ($item) {
                return [
                    'key' => $item['app_version'],
                    'value' => intval($item['value']),
                ];
            }, $results['app_version'] ?? []),
            'os_version' => array_map(function ($item) {
                return [
                    'key' => (BaseModel::OS_TYPE[$item['os_type']] ?? 'unknown') . '_' . $item['os_version'],
                    'value' => intval($item['value']),
                ];
            }, $results['os_version'] ?? []),
            'device_model' => array_map(function ($item) {
                return [
                    'key' => $item['device_model'],
                    'value' => intval($item['value']),
                ];
            }, $results['device_model'] ?? []),
            'ram' => array_map(function ($item) {
                return [
                    'key' => $item['ram'],
                    'value' => intval($item['value']),
                ];
            }, $results['ram'] ?? []),
            'duration' => array_map(function ($item) {
                return [
                    'key' => $item['duration_range'],
                    'value' => intval($item['value']),
                ];
            }, $results['duration'] ?? []),
            'avg_duration' => $avgDuration,
        ];
    }

    /**
     * 获取子查询SQL
     *
     * @param bool $returnBuilder
     * @param string $selectRaw
     * @return string|Builder
     */
    private function getSubSql(bool $returnBuilder = false, string $selectRaw = 'upper(exception_block_id) as exception_block_id')
    {
        $builder = (new UserLogDataAll())
            ->selectRaw($selectRaw)
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->where('type', Record::CATEGORY_OOM_EXCEPTION)
            ->when($this->streamTime, function (Builder $query, $streamTime) {
                $format = "Y-m-d H:i:s";
                $sd = \DateTime::createFromFormat($format, $this->rawStartDate);
                $ed = \DateTime::createFromFormat($format, $this->rawEndDate);
                if ($sd && $ed && $sd->format($format) == $this->rawStartDate && $ed->format($format) == $this->rawEndDate) {
                    $query->where('stream_time', '>=', $streamTime[0])->where('stream_time', '<=', $streamTime[1]);
                }
                return $query->whereBetween('stream_date', [date('Y-m-d', $streamTime[0]), date('Y-m-d', $streamTime[1])]);
            })
            ->when($this->timeRange, function (Builder $query, $streamTime) {
                return $query->where('stream_time', '>=', $streamTime[0])
                    ->where('stream_time', '<=', $streamTime[1]);
            })
            ->when($this->firstStreamTime, function (Builder $query, $firstStreamTime) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $firstStreamTime[0]), date('Y-m-d', $firstStreamTime[1])]);
            })
            ->when($this->lastStreamTime, function (Builder $query, $lastStreamTime) {
                return $query->whereBetween('stream_date', [date('Y-m-d', $lastStreamTime[0]), date('Y-m-d', $lastStreamTime[1])]);
            })
            ->when($this->useDuration, function ($query, $useDuration) {
                return $query->whereRaw("`basic_info_json` LIKE '%use_duration%' AND cast(get_json_object(`basic_info_json`, 'use_duration') as int) <= {$useDuration}");
            })
            ->when($this->relateRecord > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where record_id > 0 and server_dev_str != '协议未被同意')");
            })
            ->when($this->relatePerf > 0, function ($query) {
                return $query->whereRaw("`exception_merge_id` in (select exception_merge_id from exception_record_perf where perf_id > 0 and server_dev_str != '协议未被同意')");
            })
            ->when($this->relateTapd > 0, function ($query) {
                if ($this->relateTapd == 1) {
                    return $query->whereRaw("upper(exception_block_id) in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$this->extraAppId} and bind_status = 1)");
                } else {
                    return $query->whereRaw("upper(exception_block_id) not in (select upper(exception_block_id) as exception_block_id from exception_tapd_bug where developer_app_id = {$this->extraAppId} and bind_status = 1)");
                }
            })
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            });

        if ($this->isFilter) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            $builder->whereRaw("upper(exception_block_id) not in (" . Record::getRecordsSql($this->extraAppId, $type) . ")");
        }
        // 标签筛选
        if ($this->labelName) {
            $labelResult = $this->getExceptionBlockIdListByLabelName($this->eventName, $this->labelName);
            if ($labelResult) {
                $builder->whereIn(DB::raw("upper(exception_block_id)"), array_map(function ($item) {
                    return strtoupper($item);
                }, array_values($labelResult)));
            } else {
                $builder->where(DB::raw("upper(exception_block_id)"), '00');
            }
        }
        // 处理人筛选
        if ($this->handlerId) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            $handlerIdResult = ExceptionHandler::getExceptionBlockIdByHandler($this->extraAppId, $this->handlerId, $type);
            $builder->whereRaw("upper(exception_block_id) in ({$handlerIdResult})");
        }
        // 处理状态筛选
        if ($this->handlerStatus) {
            //type条件
            $type = BaseService::TYPE[$this->eventName] ?? 0;
            list($opt, $sql) = Record::getHandleStatusSql($this->extraAppId, $type, $this->handlerStatus);
            $builder->whereRaw("upper(exception_block_id) {$opt} (" . $sql . ")");
        }

        $builder = $this->whereFilter($builder);

        //过滤关键词
        $subSql = StarRocksDB::toSql(DB::table(FilterKeyword::TABLE_NAME)
            ->select(['keyword'])
            ->when($this->filterKeywordIds, function ($query) {
                return $query->whereIn('uuid', $this->filterKeywordIds);
            })
            ->where('developer_app_id', $this->extraAppId));
        if (empty($this->filterKeywordIds)) {
            $this->filterKeyWordWhere($builder, $subSql);
        } else {
            $this->filterKeyWordWhere($builder, $subSql, 'in');
        }

        return $returnBuilder ? $builder : (new ClickHouse())->getSqlBindings($builder);
    }

    /**
     * 过滤条件
     *
     * @param $builder
     * @return $builder
     */
    private function whereFilter($builder)
    {
        $builder->when($this->filters, function ($query, $filters) {
            foreach ($filters as $field => $value) {
                //值类型判断
                if (is_array($value)) {
                    $query->whereIn($field, $value);
                } elseif ($field === 'explain') { //判断是否错误详情搜索
                    $query->where(function ($query) use ($value) {
                        $this->explainWhere($query, $value);
                    });
                } elseif ($field === 'role_name') { //判断是否角色名称搜索
                    $query->where(function ($query) use ($value) {
                        return $query->whereRaw("`role_name` LIKE ?", ['%' . $value . '%'])
                            ->orWhereRaw("`extra` LIKE ?", ['%' . $value . '%']);
                    });
                } elseif ($field === 'role_id') { //判断是否角色ID搜索
                    $query->where(function ($query) use ($value) {
                        return $query->whereRaw("`role_id` = ?", [$value])
                            ->orWhereRaw("`extra` LIKE ?", ['%' . $value . '%']);
                    });
                } elseif ($field === 'server_id') { //判断是否服务器ID搜索
                    $query->where(function ($query) use ($value) {
                        $arr = explode(',', $value);
                        foreach ($arr as $v) {
                            $query->orWhereRaw("`extra` LIKE ?", ['%{"key":"serverID","value":"' . $v . '","name":"服务器的ID"}%']);
                        }
                        return $query;
                    });
                } else {
                    $query->where($field, $value);
                }
            }
            return $query;
        });
        return $builder;
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        // 判断时间 结束时间 减去 开始时间 是否超过一个月
        $startDate = Carbon::parse($this->rawStartDate);
        $endDate = Carbon::parse($this->rawEndDate);
        if ($startDate->diffInDays($endDate) > 30) {
            // 抛出异常
            throw new \Exception('错误详情筛选，时间范围不能超过一个月');
        }
        $columns = ['explain_desc', 'subject_name'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }

    /**
     *关键词where条件
     *
     * @param $builder
     * @param string $subSql
     * @param string $opt
     * @return void
     */
    private function filterKeyWordWhere($builder, string $subSql, string $opt = 'not in'): void
    {
        $this->keyWordSearch($builder, $subSql, [
            'extra_app_id' => $this->extraAppId,
            'event_name' => $this->eventName,
            'start_date' => !empty($this->streamTime) ? date('Y-m-d', $this->streamTime[0]) : null,
            'end_date' => !empty($this->streamTime) ? date('Y-m-d', $this->streamTime[1]) : null,
        ], $opt);
    }

    /**
     * 格式化
     *
     * @param array $list
     * @return array
     */
    public function format(array $list): array
    {
        $format = [];
        foreach ($list as $item) {
            $item['os_type'] = intval($item['os_type']);
            $item['category'] = intval($item['category']);
            $item['duration'] = intval($item['duration']);
            $item['explain'] = $item['explain_desc'];
            $item['ram'] = bcadd(round($item['ram'] / (1024 * 1024 * 1024), 2), 0, 2);
            $item['foot_print_memory'] = bcadd(round($item['foot_print_memory'] / (1024 * 1024 * 1024), 2), 0, 2);
            unset($item['explain_desc']);
            $format[] = $item;
        }
        return $format;
    }
}
