<?php

namespace App\Model;

class VersionWhiteList extends BaseModel
{
    use ModelTrait;

    protected $table = 'version_white_list';
    protected $primaryKey = 'id';
    protected $fillable = [
        'developer_app_id', 'app_version'
    ];

    // 获取需要剔除的版本白名单列表
    public static function getVersionList($developerAppId)
    {
        return VersionWhiteList::query()
            ->where('developer_app_id', $developerAppId)
            ->get()
            ->pluck('app_version')
            ->toArray();
    }

    // 获取版本白名单筛选where sql
    public static function getVersionWhiteListSql(int $extraAppId)
    {
        $whereNotInAppVersion = '';
        // 获取进入白名单的 版本集合 与 exceptionBlockId集合
        $appVersionWhiteList = self::getVersionList($extraAppId);
        if (!empty($appVersionWhiteList)) {
            $appVersionWhiteList = array_map(function($element) {
                return "'" . $element . "'";
            }, $appVersionWhiteList);
            $appVersionWhiteList = implode(',', $appVersionWhiteList);
            $whereNotInAppVersion = "AND `app_version` NOT IN ({$appVersionWhiteList})";
        }
        return $whereNotInAppVersion;
    }
}
