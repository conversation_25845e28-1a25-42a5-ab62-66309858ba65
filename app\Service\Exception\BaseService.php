<?php

/**
 * 异常崩溃服务基础类
 * @desc 异常崩溃服务基础类
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception;

use Illuminate\Support\Facades\DB;

abstract class BaseService
{
    /**
     *  事件名称
     *
     * @var array
     */
    const EVENT_NAME = [
        1 => 'exception_crash',
        2 => 'exception_error',
        3 => 'exception_anr',
    ];

    /**
     *  异常类型
     *
     * @var array
     */
    const TYPE = [
        'exception_crash' => 1,
        'exception_error' => 2,
        'exception_anr' => 3,
    ];

    /**
     * 根据时间名称和标签名称获取异常崩溃块ID列表
     *
     * @param string $eventName
     * @param string $labelName
     * @return array
     */
    protected function getExceptionBlockIdListByLabelName(string $eventName, string $labelName): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('exception_unique_id')
            ->where('event_name', $eventName)
            ->where('label_name', $labelName)
            ->get()->toArray();
        return array_column($result, 'exception_unique_id', 'exception_unique_id');
    }
}
