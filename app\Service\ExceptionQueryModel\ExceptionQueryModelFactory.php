<?php
/**
 * ExceptionQueryModelFactory.php
 * <AUTHOR>
 * @date 2022/12/2
 * @email <EMAIL>
 */

namespace App\Service\ExceptionQueryModel;

use App\Model\ClickHouse\Anr;
use App\Model\ClickHouse\Crash;
use App\Model\ClickHouse\Error;
use App\Model\Record;

class ExceptionQueryModelFactory
{
    /**
     * 异常类型分类的ck事件模型map
     */
    private const ExceptionQueryModelMap = [
        Record::TYPE_CRASH => Crash::class,
        Record::TYPE_ERROR => Error::class,
        Record::TYPE_ANR => Anr::class,
    ];

    /**
     * 获取一个事件模型
     * @param int $type
     * @return mixed
     */
    public static function getExceptionQueryModel(int $type)
    {
        $class = self::ExceptionQueryModelMap[$type];
        return new $class();
    }
}
