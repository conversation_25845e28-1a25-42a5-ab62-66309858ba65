<?php

namespace App\Model;

use App\Model\StarRocks\ExceptionHandler as StarRocksExceptionHandler;

class ExceptionHandler extends BaseModel
{
    use ModelTrait;

    protected $table = 'exception_handler';
    protected $primaryKey = 'id';
    protected $fillable = [
        'record_id',
        'developer_app_id',
        'handler_id',
        'handler'
    ];

    /**
     * 获取此异常处理记录所对应的异常记录记录
     */
    public function record()
    {
        return $this->belongsTo('App\Model\Record', 'record_id');
    }

    public static function getArrayByField($field, $developerAppId, $handler)
    {
        $exceptionHandlers = ExceptionHandler::query()
            ->where('developer_app_id', $developerAppId)
            ->whereIn('handler_id', explode(",", $handler))
            ->with('record')
            ->get()->toArray();
        $records = array_map(function ($exceptionHandler) use ($field) {
            return strval($exceptionHandler['record'][$field]);
        }, $exceptionHandlers);
        return array_filter($records);
    }

    /**
     * 根据处理人id获取exception_block_id数组
     * @param int $extraAppId
     * @param array $handlerId
     * @return array
     */
    public static function getExceptionBlockIdByHandler(int $extraAppId, array $handlerId, int $type)
    {
        return StarRocksExceptionHandler::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id')
            ->where('developer_app_id', $extraAppId)
            ->where('type', $type)
            ->whereIn('handler_id', $handlerId)
            ->getSql();
    }
}
