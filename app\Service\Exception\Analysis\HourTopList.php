<?php

/**
 * 每小时排行榜
 * @desc 每小时排行榜
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Http\Logic\TapdLogic;
use App\Model\BaseModel;
use App\Model\Record;
use App\Model\StarRocks\ExceptionStatAllV2;
use App\Model\StarRocks\ExceptionStatVersionV2;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\StarRocks\FilterKeyword;
use App\Model\StarRocks\StarRocksDB;
use App\Model\TapdBug;
use App\Service\Exception\BaseService;
use App\Service\Exception\KeyWordSearch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HourTopList extends BaseService
{
    use KeyWordSearch;

    /**
     * 24小时内
     */
    public const HOUR_24 = '24_hour';

    /**
     * 7天内
     */
    public const DAY_7 = '7_day';

    /**
     * HTTP请求对象
     *
     * @var Request
     */
    private $request;

    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;

    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;

    /**
     * 限制条数
     *
     * @var int
     */
    private $limit;

    /**
     * 按设备、按数量
     *
     * @var int
     */
    private $filterType;

    /**
     * app版本
     *
     * @var int
     */
    private $appVersion;

    /**
     * 开始时间
     *
     * @var int
     */
    private $startDate;

    /**
     * 结束时间
     *
     * @var int
     */
    private $endDate;

    /**
     * 开始时间
     *
     * @var int
     */
    private $startTime;

    /**
     * 结束时间
     *
     * @var int
     */
    private $endTime;

    /**
     * 系统类型
     *
     * @var int
     */
    private $osType;

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->request = $request;
        $this->extraAppId = $request->input('developer_app_id');
        $this->eventName = self::EVENT_NAME[$request->input('type')] ?? '';
        $this->limit = $request->input('limit', 20);
        $this->filterType = $request->input('filter_type');
        $this->appVersion = $request->input('app_version');
        $this->osType = $request->input('os_type');
        $this->startDate = Carbon::parse($request->input('start_date', date('Y-m-d')))->toDateString();
        $this->endDate = Carbon::parse($request->input('end_date', date('Y-m-d')))->toDateString();
        $this->startTime = Carbon::parse($request->input('start_date', date('Y-m-d H') . ':00:00'))->timestamp;
        $this->endTime = Carbon::parse($request->input('end_date', date('Y-m-d H') . ':59:59'))->timestamp;
    }

    /**
     * 获取列表
     *
     * @return array
     */
    public function getList()
    {
        // 获取block_id
        $blockIds = $this->getBlockIds();
        // 判断是否为空
        if (empty($blockIds)) {
            return [];
        }
        //获取数据
        $list = $this->getHourTopList($blockIds);
        //格式化
        return $this->format($list);
    }

    /**
     * 获取要统计的block_id
     *
     * @return array
     */
    private function getBlockIds()
    {
        $selectRaw = 'upper(exception_block_id) as exception_block_id, count(distinct server_dev_str) as num';
        // 判断是否按设备还是次数
        if ($this->filterType == 2) {
            $selectRaw = 'upper(exception_block_id) as exception_block_id, count(1) as num';
        }
        $builder = ExceptionStreamAll::query()
            ->selectRaw($selectRaw)
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->where('stream_time', '<=', $this->endTime)
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            })
            ->groupBy(DB::raw('upper(exception_block_id)'))
            ->orderByDesc('num')
            ->limit($this->limit);
        // 过滤关键词
        $this->filterKeyWordWhere($builder);
        // 获取数据
        $list = $builder->getFromSR();
        return array_column($list, 'exception_block_id');
    }

    /**
     * 获取统计信息
     *
     * @param array $blockIds
     * @return Builder
     */
    private function getStatAllQuery(array $blockIds)
    {
        return ExceptionStatAllV2::query()
            ->selectRaw('exception_block_id, server_dev_str_count, count, min_stream_time, max_stream_time')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn('exception_block_id', $blockIds);
    }

    /**
     * 获取版本信息
     *
     * @param array $blockIds
     * @return Builder
     */
    private function getStatVersionQuery(array $blockIds)
    {
        return ExceptionStatVersionV2::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, min(version) as min_version, max(version) as max_version')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->groupBy(DB::raw('upper(exception_block_id)'));
    }

    /**
     * 获取异常信息
     *
     * @param array $blockIds
     * @return Builder
     */
    private function getExceptionInfoQuery(array $blockIds)
    {
        return ExceptionStreamAll::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, count(1) as num, count(distinct server_dev_str) as dev_num, any_value(subject_name) as subject_name, any_value(explain_desc) as explain_desc, any_value(type) as type, any_value(os_type) as os_type, any_value(app_version) as app_version, min(stream_time) as stream_time, max(stream_time) as max_stream_time')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_time', '<=', $this->endTime)
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            })
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->groupBy(DB::raw('upper(exception_block_id)'))
            ->orderByDesc('num');
    }

    /**
     * 获取趋势查询
     *
     * @param array $blockIds
     * @return Builder
     */
    private function getTrendQuery(array $blockIds)
    {
        return ExceptionStreamAll::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, stream_date, count(1) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_date', '>=', Carbon::parse($this->startDate)->subDays(13)->toDateString())
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            })
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->groupBy(DB::raw('upper(exception_block_id)'), 'stream_date')
            ->orderBy(DB::raw('upper(exception_block_id)'))
            ->orderBy('stream_date');
    }

    /**
     * 获取环比查询
     *
     * @param array $blockIds
     * @return Builder
     */
    private function getMoMQuery(array $blockIds)
    {
        return ExceptionStreamAll::query()
            ->selectRaw('upper(exception_block_id) as exception_block_id, count(1) as num')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->where('stream_time', '<=', Carbon::parse($this->endTime)->subHours()->timestamp)
            ->where('stream_time', '>=', Carbon::parse($this->startTime)->subHours()->timestamp)
            ->where('stream_date', '>=', Carbon::parse($this->startDate)->subHours()->toDateString())
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            })
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->groupBy(DB::raw('upper(exception_block_id)'));
    }

    /**
     * 获取异常总设备数
     *
     * @return Builder
     */
    private function getExceptionDevNumQuery()
    {
        return ExceptionStreamAll::query()
            ->selectRaw('count(distinct server_dev_str) as dev_num')
            ->where('extra_app_id', $this->extraAppId)
            ->where('event_name', $this->eventName)
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->where('stream_time', '<=', $this->endTime)
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            });
    }

    /**
     * 获取启动总设备数
     *
     * @return Builder
     */
    private function getInitDevNumQuery()
    {
        return ExceptionStreamAll::query()
            ->selectRaw('count(distinct server_dev_str) as dev_num')
            ->where('extra_app_id', $this->extraAppId)
            ->when($this->appVersion, function ($query) {
                return $query->where('app_version', $this->appVersion);
            })
            ->when($this->osType, function ($query) {
                return $query->where('os_type', $this->osType);
            })
            ->where('stream_time', '<=', $this->endTime)
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId] ?? false, function ($query) { // 屏蔽包名
                return $query->whereNotIn('sdk_package_name', BaseModel::FILTER_PACKAGE_NAME[$this->extraAppId]);
            });
    }

    /**
     *关键词where条件
     *
     * @param $builder
     * @param string $subSql
     * @param string $opt
     */
    private function filterKeyWordWhere($builder)
    {
        $subSql = StarRocksDB::toSql(DB::table(FilterKeyword::TABLE_NAME)
            ->select(['keyword'])
            ->where('developer_app_id', $this->extraAppId));
        $this->keyWordSearch($builder, $subSql, [
            'extra_app_id' => $this->extraAppId,
            'event_name' => $this->eventName,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
        ], 'not in');
    }

    /**
     * 获取数据
     *
     * @param array $blockIds
     * @return array
     */
    private function getHourTopList(array $blockIds)
    {
        // 获取数据
        $quires = [
            'stat_all' => $this->getStatAllQuery($blockIds),
            'stat_version' => $this->getStatVersionQuery($blockIds),
            'exception_info' => $this->getExceptionInfoQuery($blockIds),
            'trend' => $this->getTrendQuery($blockIds),
            'mom' => $this->getMoMQuery($blockIds),
            'exception_dev_num' => $this->getExceptionDevNumQuery(),
            'init_dev_num' => $this->getInitDevNumQuery(),
        ];
        $results = (new ClickHouse())->multiGetData($quires);
        // 处理数据
        $list = array_map(function ($item) {
            return ['exception_block_id' => $item];
        }, $blockIds);
        // 处理统计数据
        $this->handleStatAll($list, $results['stat_all']);
        // 处理版本数据
        $this->handleStatVersion($list, $results['stat_version']);
        // 处理异常信息
        $this->handleExceptionInfo($list, $results['exception_info']);
        // 处理趋势数据
        $this->handleTrend($list, $results['trend']);
        // 处理环比数据
        $this->handleMoM($list, $results['mom']);
        // 处理异常设备数
        $this->handleExceptionDevNum($list, $results['exception_dev_num']);
        // 处理启动设备数
        $this->handleInitDevNum($list, $results['init_dev_num']);
        // 处理时间范围
        $this->handleTimeRange($list);
        // 返回数据
        return $list;
    }

    /**
     * 处理统计数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleStatAll(&$list, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = array_column($data, null, 'exception_block_id');

        foreach ($list as &$item) {
            $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
            $item['block_num'] = $statData['count'] ?? 0;
            $item['device_num'] = $statData['server_dev_str_count'] ?? 0;
            $item['stream_min_stream_time'] = $statData['min_stream_time'] ?? 0;
            $item['stream_max_stream_time'] = $statData['max_stream_time'] ?? 0;
        }
    }

    /**
     * 处理版本数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleStatVersion(&$list, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = array_column($data, null, 'exception_block_id');

        foreach ($list as &$item) {
            $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
            $item['min_version'] = $statData['min_version'] ?? '';
            $item['max_version'] = $statData['max_version'] ?? '';
        }
    }

    /**
     * 处理异常信息
     *
     * @param array $list
     * @param array $data
     */
    private function handleExceptionInfo(&$list, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = array_column($data, null, 'exception_block_id');

        foreach ($list as &$item) {
            $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
            $item['type'] = $statData['type'] ?? '';
            $item['os_type'] = $statData['os_type'] ?? '';
            $item['subject_name'] = $statData['subject_name'] ?? '';
            $item['explain_desc'] = $statData['explain_desc'] ?? '';
            $item['match_num'] = $statData['num'] ?? '';
            $item['dev_match_num'] = $statData['dev_num'] ?? '';
            $item['bak_app_version'] = $statData['app_version'] ?? '';
            $item['bak_stream_time'] = $statData['stream_time'] ?? '';
            if (($statData['max_stream_time'] ?? 0) > $item['stream_max_stream_time']) {
                $item['stream_max_stream_time'] = $statData['max_stream_time'];
            }
        }
    }

    /**
     * 处理趋势数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleTrend(&$list, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = [];
        foreach ($data as $item) {
            $dataIndexedById[$item['exception_block_id']][] = $item;
        }

        foreach ($list as &$item) {
            $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
            // 用日期作为key
            $statData = array_column($statData, null, 'stream_date');
            // 判断是否存在没有日期的数据
            $startDate = Carbon::parse($this->startDate)->subDays(13)->toDateString();
            while ($startDate <= $this->endDate) {
                // 判断是否存在
                if (!isset($statData[$startDate])) {
                    $statData[$startDate] = [
                        'exception_block_id' => $item['exception_block_id'],
                        'stream_date' => $startDate,
                        'num' => '0',
                    ];
                }
                $startDate = Carbon::parse($startDate)->addDay()->toDateString();
            }
            // 根据日期排序
            usort($statData, function ($a, $b) {
                return strtotime($a['stream_date']) - strtotime($b['stream_date']);
            });
            $item['trend'] = array_values($statData);
        }
    }

    /**
     * 处理环比数据
     *
     * @param array $list
     * @param array $data
     */
    private function handleMoM(&$list, $data)
    {
        // 把data数据中的 exception_block_id 字段作为key值
        $dataIndexedById = array_column($data, null, 'exception_block_id');

        foreach ($list as &$item) {
            $statData = isset($dataIndexedById[$item['exception_block_id']]) ? $dataIndexedById[$item['exception_block_id']] : [];
            // 判断 match_num 是否为0
            if (empty($statData['num'])) {
                $item['mom'] = bcmul($item['match_num'], 100, 2);
            } else {
                $item['mom'] = bcmul(bcdiv(bcsub($item['match_num'], $statData['num'], 4), $statData['num'], 4), 100, 2);
            }
        }
    }

    /**
     * 处理异常设备数
     *
     * @param array $list
     * @param array $data
     */
    private function handleExceptionDevNum(&$list, $data)
    {
        // 获取异常设备数
        $total = $data[0]['dev_num'] ?? 1;

        foreach ($list as &$item) {
            $item['ratio'] = round(bcdiv($item['dev_match_num'], $total, 6) * 100, 2);
        }
    }

    /**
     * 处理启动设备数
     *
     * @param array $list
     * @param array $data
     */
    private function handleInitDevNum(&$list, $data)
    {
        // 获取异常设备数
        $total = $data[0]['dev_num'] ?? 1;

        foreach ($list as &$item) {
            $item['exception_ratio'] = round(bcdiv($item['dev_match_num'], $total, 6) * 100, 2);
        }
    }

    /**
     * 处理启动设备数
     *
     * @param array $list
     */
    private function handleTimeRange(&$list)
    {
        foreach ($list as &$item) {
            $streamMaxStreamTime = (int)$item['stream_min_stream_time'];
            $timeRange = null;
            // 判断时间是否少于 86400
            if (time() - $streamMaxStreamTime <= 86400) {
                $timeRange = self::HOUR_24;
            } elseif (time() - $streamMaxStreamTime <= 604800) { // 判断时间是否少于 604800
                $timeRange = self::DAY_7;
            }
            $item['time_range'] = $timeRange;
        }
    }

    /**
     * @param array $list
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:44
     * memo : 格式化
     */
    public function format(array $list): array
    {
        // 获取异常ID
        $blockIds = array_column($list, 'exception_block_id');
        //标签
        $labelList = $this->getLabelListByExceptionList($blockIds);
        //处理人、状态、白名单
        $recordAbstractList = $this->matchRecordAbstract($blockIds);
        //tabp
        $tapdBugList = $this->getTapdBugList($blockIds);
        //格式化处理
        $format = [];
        foreach ($list as $value) {
            $item = [
                'exception_block_id' => $value['exception_block_id'],
                'category' => (int)$value['type'],
                'os_type' => (int)$value['os_type'],
                'event_name' => $this->eventName,
                'name' => $value['subject_name'],
                'explain' => $value['explain_desc'],
                'crash_count' => (int)(empty($value['block_num']) ? $value['match_num'] : $value['block_num']),
                'crash_user_count' => (int)(empty($value['device_num']) ? $value['dev_match_num'] : $value['device_num']),
                'first_happen_time' => (int)(empty($value['stream_min_stream_time']) ? $value['bak_stream_time'] : $value['stream_min_stream_time']),
                'first_report_time' => (int)(empty($value['stream_min_stream_time']) ? $value['bak_stream_time'] : $value['stream_min_stream_time']), $value['stream_min_stream_time'],
                'last_happen_time' => (int)(empty($value['stream_max_stream_time']) ? $value['bak_stream_time'] : $value['stream_max_stream_time']),
                'last_report_time' => (int)(empty($value['stream_max_stream_time']) ? $value['bak_stream_time'] : $value['stream_max_stream_time']),
                'handler' => $recordAbstractList[$value['exception_block_id']]['handler'] ?? '',
                'handler_id' => $recordAbstractList[$value['exception_block_id']]['handler_id'] ?? '',
                'status' => $recordAbstractList[$value['exception_block_id']]['status'] ?? 1,
                'record_id' => $recordAbstractList[$value['exception_block_id']]['record_id'] ?? 0,
                'is_white_list' => $recordAbstractList[$value['exception_block_id']]['is_add_white_list'] ?? 0,
                'bug_id' => $tapdBugList[$value['exception_block_id']]['tapd_bug_id'] ?? '',
                'label_list' => $labelList[$value['exception_block_id']] ?? [],
                'match_num' => (int)$value['match_num'],
                'dev_match_num' => (int)$value['dev_match_num'],
                'version' => [
                    'min' => empty($value['min_version']) ? $value['bak_app_version'] : $value['min_version'],
                    'max' => empty($value['max_version']) ? $value['bak_app_version'] : $value['max_version'],
                ],
                'trend' => $value['trend'],
                'mom' => $value['mom'],
                'ratio' => $value['ratio'],
                'exception_ratio' => $value['exception_ratio'],
                'time_range' => $value['time_range'] ?? '',
            ];
            // 判断 crash_count 是否小于 match_num
            if ($item['crash_count'] < $item['match_num']) {
                $item['crash_count'] = $item['match_num'];
            }
            // 判断 dev_match_num 是否小于 crash_user_count
            if ($item['crash_user_count'] < $item['dev_match_num']) {
                $item['crash_user_count'] = $item['dev_match_num'];
            }
            $format[] = $item;
        }
        return $format;
    }

    /**
     * 根据异常列表获取标签列表
     *
     * @param array $blockIds
     * @return array
     */
    private function getLabelListByExceptionList(array $blockIds): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('label_name', DB::raw('upper(exception_unique_id) as exception_unique_id'))
            ->where('developer_app_id', $this->extraAppId)
            ->whereIn(DB::raw('upper(exception_unique_id)'), $blockIds)
            ->get()->toArray();
        $format = [];
        foreach ($result as $record) { //避免循環查庫
            $format[$record->exception_unique_id][] = [
                'id' => "{$this->extraAppId}##" . $record->label_name,
                'label_name' => $record->label_name,
                'exception_block_id' => $record->exception_unique_id,
            ];
        }
        return $format;
    }

    /**
     * 匹配记录摘要信息
     *
     * @param array $blockIds
     * @return array
     */
    private function matchRecordAbstract(array $blockIds): array
    {
        $result = Record::query()
            ->select(['record_id', 'is_add_white_list', 'status', 'handler_id', 'handler', DB::raw('upper(exception_block_id) as exception_block_id')])
            ->where('developer_app_id', $this->extraAppId)
            ->where('type', self::TYPE[$this->eventName])
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    /**
     * 根据exception_block_id获取最新且未解绑的缺陷记录
     *
     * @param array $blockIds
     * @return array
     */
    private function getTapdBugList(array $blockIds): array
    {
        // 获取当前项目绑定的item_id
        $tapdAccountDetail = TapdLogic::pullTapdAccountDetailCache($this->extraAppId);
        $result = TapdBug::query()
            ->select([DB::raw('upper(exception_block_id) as exception_block_id'), 'tapd_bug_id'])
            ->where('developer_app_id', $this->extraAppId)
            ->where('type', self::TYPE[$this->eventName]) // 1 崩溃 2错误
            ->where('bind_status', 1) // 1 已绑定 2未绑定
            ->when(!empty($tapdAccountDetail), function ($query) use ($tapdAccountDetail) {
                $query->where('tapd_account_id', $tapdAccountDetail['id']);
            })
            ->whereIn(DB::raw('upper(exception_block_id)'), $blockIds)
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }
}
