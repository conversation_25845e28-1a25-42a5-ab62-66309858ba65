<?php

/**
 * 统计APP版本脚本
 * @desc 统计APP版本脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/15
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\StarRocksService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StatVersionCommand extends Command
{
    /**
     * 统计时间间隔
     *
     * @var int
     */
    public const TIME = 10;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stat:version';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '统计APP版本脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //设置脚本内存
            ini_set('memory_limit', '1024M');
            //打印日志
            Log::info("执行统计APP版本脚本开始");
            // 获取当前时间的时间戳
            $nowTime = time();
            // 上次同步时间
            $lastTime = $nowTime - ((static::TIME + 1) * 60);
            // 日期时间
            $dateTime = date('Y-m-d', ($nowTime - (static::TIME * 60)));
            // 内部子查询
            $subQuery = ExceptionStreamAll::query()
                ->select('exception_block_id')
                ->where('stream_time', '>', $lastTime)
                ->where('stream_time', '<=', $nowTime)
                ->whereNotNull('exception_block_id')
                ->where('exception_block_id', '!=', '')
                ->where('stream_date', $dateTime)
                ->groupBy('exception_block_id');
            // 保存统计的数据
            $data = ExceptionStreamAll::query()
                ->joinSub($subQuery, 'b', function ($join) {
                    $join->on('exception_stream_all.exception_block_id', '=', 'b.exception_block_id');
                })
                ->whereNotNull('exception_stream_all.extra_app_id')
                ->where('exception_stream_all.extra_app_id', '!=', '')
                ->whereNotNull('exception_stream_all.event_name')
                ->where('exception_stream_all.event_name', '!=', '')
                ->whereNotNull('exception_stream_all.app_version')
                ->where('exception_stream_all.app_version', '!=', '')
                ->where('exception_stream_all.stream_date', $dateTime)
                ->groupBy('exception_stream_all.extra_app_id', 'exception_stream_all.exception_block_id', 'exception_stream_all.event_name', 'exception_stream_all.stream_date', 'exception_stream_all.app_version')
                ->select('exception_stream_all.extra_app_id', 'exception_stream_all.exception_block_id', 'exception_stream_all.event_name', 'exception_stream_all.stream_date', DB::raw('exception_stream_all.app_version AS version'))
                ->getSql();
            // 执行SQL
            (new StarRocksService())->execute("insert into exception_stat_version_v3 {$data}");
            //打印日志
            Log::info("执行统计APP版本脚本完成");
        } catch (\Exception $e) {
            Log::error("执行统计APP版本脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
