<?php

namespace App\Model;

use Illuminate\Support\Facades\Redis;

class SwitchConfig extends BaseModel
{
    use ModelTrait;

    protected $table = 'switch_config';
    protected $primaryKey = 'id';

    /**
     * 开关状态,1开启,0关闭
     */
    const STATUS_ON = 1;
    const STATUS_OFF = 0;

    /**
     * 是否允许错误上报,1开启,0关闭
     */
    const ALLOW_ERROR_REPORT = 1;
    const NOT_ALLOW_ERROR_REPORT = 0;

    /**
     * 是否允许崩溃上报,1开启,0关闭
     */
    const ALLOW_CRASH_REPORT = 1;
    const NOT_ALLOW_CRASH_REPORT = 0;

    /**
     * 是否允许ANR上报,1开启,0关闭
     */
    const ALLOW_ANR_REPORT = 1;
    const NOT_ALLOW_ANR_REPORT = 0;

    /**
     * 是否允许截图上报,1开启,0关闭
     */
    const ALLOW_SCREENSHOT = 1;
    const NOT_ALLOW_SCREENSHOT = 0;

    /**
     * 是否允许oom上报,1开启,0关闭
     */
    const ALLOW_OOM_REPORT = 1;
    const NOT_ALLOW_OOM_REPORT = 0;

    /**
     * 设置缓存大小
     *
     * @param string $value
     * @return void
     */
    public function setCacheSizeAttribute(string $value)
    {
        $this->attributes['cache_size'] = bcmul($value, 1024, 0);
    }

    /**
     * 获取开关缓存KEY
     *
     * @param $developerAppId
     * @return string
     */
    private static function getCacheKey($developerAppId): string
    {
        return 'exception_switch_config:' . $developerAppId;
    }

    /**
     * 刷新缓存
     *
     * @param $developerAppId
     * @return void
     */
    public static function flushCache($developerAppId)
    {
        $cacheKey = self::getCacheKey($developerAppId);
        $list = self::query()
            ->where('developer_app_id', $developerAppId)
            ->get();
        Redis::connection('api')->set($cacheKey, json_encode($list));
    }
}
