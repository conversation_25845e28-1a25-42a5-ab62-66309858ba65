<?php

/**
 * 异常处理配置表模型
 * @desc 异常处理配置表模型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/03/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model;

class ExceptionHandlerConfig extends BaseModel
{
    use ModelTrait;

    protected $table = 'exception_handler_config';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'keyword',
        'handler',
        'operator'
    ];

    protected $casts = [
        'handler' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
