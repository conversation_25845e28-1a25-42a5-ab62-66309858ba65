<?php

/**
 * 最近一个小时（崩溃/错误）维度预警
 * @desc 最近一个小时（崩溃/错误）维度预警
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/06/27
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\WarningIndexHandler;

use App\Model\Warning;
use Carbon\Carbon;

class LastHourExceptionIndexHandler extends ExceptionIndexHandler
{
    /**
     * 初始化
     */
    public function __construct(int $exceptionType, Warning $warning)
    {
        parent::__construct($exceptionType, $warning);
        //获取当前的时间
        $now = Carbon::now();
        //$this->startTime减2个小时的时间，因为和上一个小时做对比
        $this->startTime = (clone $now)->subHours(2)->startOfHour()->timestamp;
        //$this->endTime减1个小时的时间，因为和上一个小时做对比
        $this->endTime = (clone $now)->subHours(1)->endOfHour()->timestamp;
    }
}
