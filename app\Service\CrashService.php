<?php
/**
 * CrashService.php
 * <AUTHOR>
 * @date 2022/9/20
 * @email <EMAIL>
 */

namespace App\Service;

use App\Components\Scope\Scope;
use App\Model\Record;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;

class CrashService
{
    /**
     * @var Scope
     */
    public $scope;

    public $query;

    public function __construct($scope = null)
    {
        empty($scope) && $scope = new Scope();
        $this->scope = $scope;
        $this->query = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query();
    }

    /**
     * 获取issue状态信息
     * @return array
     */
    public function getStatusValue(): array
    {
        $status = $this->getRecordBuilder()->groupBy('status')
            ->selectRaw('distinct status as value')->get()->toArray();
        //对查询到的issue状态信息做映射
        foreach ($status as $key => $item) {
            $label = Record::$statusMessage[$item['value']] ?? null;
            if ($label == null) {
                continue;
            }
            $status[$key]['label'] = $label;
        }
        return $status;
    }

    /**
     * 获取具体异常类型
     * @return array
     */
    public function getCategoryValue(): array
    {
        $category = $this->getRecordBuilder()->groupBy('category')
            ->selectRaw('distinct category as value')->get()->toArray();
        //对查询到的具体异常类型做映射
        foreach ($category as $key => $item) {
            $label = Record::$categoryMessage[$item['value']] ?? null;
            if ($label == null) {
                continue;
            }
            $category[$key]['label'] = $label;
        }
        return $category;
    }

    /**
     * 获取处理人信息
     * @return array 返回处理人id不为0的数据
     */
    public function getHandlerValue(): array
    {
        return $this->getRecordBuilder()->where('handler_id', '!=', 0)
            ->selectRaw('distinct handler as label, handler_id as value')
            ->get()->toArray();
    }

    /**
     * 查询ck数据
     * @param Object $object
     * @param string $sql
     * @return array
     */
    public function getCKValue(object $object, string $sql): array
    {
        return $object->tap(function ($query) {
            $this->scope->getBuilder($query);
        })
            ->selectRaw($sql)
            ->distinct()
            ->getFromCK();
    }

    /**
     * 从ck获取筛选下拉列表的数据
     * @param array $types
     * @return array
     */
    public function getSearchValue(array $types): array
    {
        //获取数据
        ['data' => $data, 'newTypes' => $newTypes] = $this->getData($types);
        //格式化
        $data = $this->format($data, $newTypes);
        //去重
        return $this->removeDuplicates($data);
    }

    /**
     * 数组值的替换
     * @param array $types 原数组
     * @param array $needles 需要替换的数据集
     * @param array $replacements 替换结果的数据集
     * @return array
     */
    private function replaceTypes(array $types, array $needles, array $replacements): array
    {
        foreach ($needles as $key => $needle) {
            $index = array_search($needle, $types, true);
            if ($index === false) {
                continue;
            }
            array_splice($types, $index, 1, $replacements[$key]);
        }
        return $types;
    }

    /**
     * 数据格式化
     * @param array $data
     * @param array $newTypes
     * @return array
     */
    private function format(array $data, array $newTypes): array
    {
        //对数据进行处理
        $list = [];
        foreach ($data as $key => $item) {
            foreach ($newTypes as $type) {
                if ($item[$type . '_label'] != "") {
                    $list[$type][$key]['label'] = $item[$type . '_label'];
                    $list[$type][$key]['value'] = $item[$type . '_value'];
                }
                if (isset($item['type_label']) && $item['type_label'] != "") {
                    $label = Record::$categoryMessage[$item['type_value']] ?? null;
                    if ($label == null) {
                        continue;
                    }
                    $list['type'][$key]['value'] = $item['type_value'];
                    $list['type'][$key]['label'] = $label;
                }
            }
        }
        foreach ($newTypes as $type) {
            if (!isset($list[$type])) {
                $list[$type] = [];
            }
        }
        return $list;
    }

    /**
     *  数据去除重复
     * @param array $list
     * @return array|false
     */
    private function removeDuplicates(array $list)
    {
        foreach ($list as $key => $item) {
            $collection = collect($item);
            $duplicates = $collection->duplicates()->all();
            $duplicateKeys = array_keys($duplicates);
            $list[$key] = $collection->forget($duplicateKeys)->values()->all();
        }

        // 修改数据的key
        $keys = array_keys($list);
        $needles = ['version', 'current_page_title', 'release_store', 'current_province', 'type'];
        $replacements = ['sdk_version', 'app_page', 'channel', 'province', 'crash_type'];
        $newTypes = $this->replaceTypes($keys, $needles, $replacements);

        return array_combine($newTypes, $list);
    }

    /**
     * 获取数据
     * @param array $types
     * @return array
     */
    private function getData(array $types): array
    {
        //转换成ck的字段 方便查询
        $needles = ['sdk_version', 'app_page', 'channel', 'province', 'crash_type'];
        $replacements = ['version', 'current_page_title', 'release_store', 'current_province', 'type'];
        $newTypes = $this->replaceTypes($types, $needles, $replacements);
        //拼接sql
        $sql = "";
        foreach ($newTypes as $type) {
            $sql .= $type . ' as ' . $type . '_label, ' . $type . ' as ' . $type . '_value, ';
        }
        //去掉最后一个拼接sql的,
        $sql = substr($sql, 0, -2);
        //异常上报数据
        $data = $this->getCKValue(clone($this->query), $sql);
        return ['data' => $data, 'newTypes' => $newTypes];
    }

    private function getRecordBuilder()
    {
        return Record::query()->where('type', $this->scope->type)
            ->where('developer_app_id', $this->scope->developerAppId)
            ->whereBetween('created_at', [$this->scope->startDate, $this->scope->endDate])
            ->whereIn('category', Record::$categoryIndex)
            ->when(!empty($this->scope->issueStatus), function ($query) {
                $query->whereIn('status', json_decode($this->scope->issueStatus, true));
            })->when(!empty($this->scope->handleId), function ($query) {
                $query->whereIn('handler_id', json_decode($this->scope->handleId, true));
            })->when(!empty($this->scope->crashType), function ($query) {
                $query->whereIn('category', json_decode($this->scope->crashType, true));
            })->when(!empty($this->scope->exceptionUniqueId), function ($query) {
                $query->where('exception_unique_id', $this->scope->exceptionUniqueId);
            });
    }
}
