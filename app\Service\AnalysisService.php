<?php
/**
 * AnalysisService.php
 *
 * User: Dican
 * Date: 2022/9/27
 * Email: <<EMAIL>>
 */

namespace App\Service;

use App\Components\HttpAgent;
use App\Jobs\ClickHouseUploadJob;
use App\Model\ClickHouse\Analysis;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\Symbol;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class AnalysisService
{
    protected $request;
    protected $query;

    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * 符号表解析上报
     * @param $originStacksJson
     */
    public function analysisUpload($originStacksJson)
    {
        $this->query = Analysis::query();
        $data = [
            'event_name' => 'exception_analysis',
            'event_time' => $this->request['ts'] ?? time(),
            'ip' => $_SERVER["REMOTE_ADDR"],
            'extra_app_id' => $this->request['developer_app_id'] ?? 0,
            'server_dev_str' => $this->request['server_dev_str'] ?? '',
            'app_version' => $this->request['app_version'] ?? '',
            'type' => $this->request['category'] ?? 0, //异常类型
            'subject_name' => $this->request['name'] ?? '', //异常错误名称
            'dev_create_time' => $this->request['client_ts'] ?? 0, //异常发生时间
            'origin_stacks_json' => json_encode($originStacksJson), //原始堆栈
            'game_info_json' => json_encode($this->request['uuid_info']),
        ];
        ClickHouseUploadJob::dispatch($data)->onQueue('ck_upload');
    }

    /**
     * PC 上传符号表
     *
     * @return \Illuminate\Database\Eloquent\Model|static
     */
    public function pcUUid($developerAppId, $appVersion, $url, $name)
    {
        $params = [
            "pdb_url" => $url, // pdb文件下载地址
            "pdb_name" => $name, // 对应modules中每个字典中的debug_file
        ];
        $response = HttpAgent::getInstance()->setTimeout(3600)->setConnectTimeout(3600)->request('POST', 'https://crashpad.shiyue.com/pdb/decode', [
            'json' => $params,
        ]);
        // 打印日志
        Log::info("pcUUid: " . $response['message']);
        // 解析pdb
        $message = json_decode($response['message'], true);
        if (empty($message)) {
            throw new Exception('解析pdb失败');
        }
        if ($message['code'] != 0) {
            throw new Exception($message['message']);
        }
        // 上传参数中的uuid
        $uuid = request()->input('uuid');
        // 判断是否有uuid参数，如果有则判断，解析的uuid和上传的uuid是否一致
        if ($uuid && $uuid !== $message['data']['uuid']) {
            throw new \Exception('UUID不一致，请重新上传', -1);
        }
        // 获取文件路径
        $path = parse_url($url, PHP_URL_PATH);
        $filePath = substr($path, strpos($path, '/files'));
        $prefixPath = '/data/www/developer/efficacy-manager/storage/app/public';
        return Symbol::query()->updateOrCreate([
            'developer_app_id' => $developerAppId,
            'app_version' => $appVersion,
            'os_type' => 3,
            'uuid' => $message['data']['uuid'],
        ], [
            'file_url' => $url,
            'file_name' => $name,
            'file_size' => filesize("{$prefixPath}{$filePath}"),
            'created_at' => Carbon::now()->toDateTimeString(),
            'updated_at' => Carbon::now()->toDateTimeString(),
        ]);
    }

    /**
     * PC 异常分析
     *
     * @return array
     */
    public function pcAnalysis($exceptionBlockId, $exceptionMergeId)
    {
        // 获取文件名称
        $data = ExceptionStreamAll::query()
            ->select(['exception_file'])
            ->where('exception_block_id', $exceptionBlockId)
            ->where('exception_merge_id', $exceptionMergeId)
            ->firstFromSR();
        // 请求解析
        $params = [
            "dump_file_name" => $data['exception_file'], //文件地址
        ];
        $response = HttpAgent::getInstance()->request('POST', 'https://crashpad.shiyue.com/dump/decode', [
            'json' => $params,
        ]);
        $message = json_decode($response['message'], true);
        if (empty($message)) {
            throw new Exception('解析失败');
        }
        if ($message['code'] != 0) {
            throw new Exception($message['message']);
        }
        // 循环请求判断是否解析完成
        $i = 0;
        while ($i < 10) {
            $resp = HttpAgent::getInstance()->request('GET', 'https://crashpad.shiyue.com/decode/task/' . $message['data']['task_id']);
            // 转为数组
            $msg = json_decode($resp['message'], true);
            // 判断是否解析完成
            if (!empty($msg) && $msg['code'] != 0 && $msg['code'] != 4002) {
                throw new Exception($msg['message']);
            }
            if (!empty($msg) && $msg['code'] == 0) {
                return $this->convertStacks($data['exception_file']);
            }
            // 睡眠5秒
            sleep(5);
            $i++;
        }
        throw new Exception('生成解析文件失败');
    }

    /**
     * 转换堆栈信息
     *
     * @param string $filePath
     * @return array
     */
    public function convertStacks($filePath)
    {
        $filePath = str_replace('.dmp', '_sym.json', $filePath);
        $response = HttpAgent::getInstance()->request('GET', env('COS_BASE_URL') . $filePath);
        $message = json_decode($response['message'], true);
        if (empty($message)) {
            return [];
        }
        $stacks = $message['crashing_thread']['frames'];
        $content = $message['threads'];
        // 解析数据
        $list = [];
        $stacksContent = [];
        foreach ($stacks as $stack) {
            $stacksContent[] = $this->getThreadStackText($stack);
        }
        $list[] = [
            'thread_no' => $message['crashing_thread']['threads_index'],
            'thread_name' => "Threads",
            'stacks' => $stacksContent,
        ];
        foreach ($content as $key => $item) {
            // 如果是崩溃线程，跳过
            if ($key == $message['crashing_thread']['threads_index']) {
                continue;
            }
            $frames = $item['frames'];
            $stacksContent = [];
            foreach ($frames as $stack) {
                $stacksContent[] = $this->getThreadStackText($stack);
            }
            $list[] = [
                'thread_no' => $key,
                'thread_name' => "Threads",
                'stacks' => $stacksContent,
            ];
        }
        return $list;
    }

    /**
     * 获取线程栈内容
     *
     * @param array $stack
     * @return string
     */
    public function getThreadStackText(array $stack): string
    {
        if (empty($stack['module'])) {
            $text = $stack['offset'];
        } else {
            $text = $stack['module'];
            // 判断是否有 function
            if (empty($stack['function']) && !empty($stack['module_offset'])) {
                $text .= ' ' . $stack['module_offset'];
            }
        }
        if (!empty($stack['function']) && !empty($stack['function_offset'])) {
            $text .= "\t\t" . $stack['function'] . ' ' . $stack['function_offset'];
        }
        if (!empty($text) && !empty($stack['file']) && !empty($stack['line'])) {
            $text .= "\t\t" . $stack['file'] . ":" . $stack['line'];
        }
        return $text;
    }
}
