<?php

/**
 * 录屏记录模型类
 * @desc 录屏记录模型类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/11
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class RecordLog extends Model
{
    /**
     * 数据库连接
     *
     * @var string
     */
    protected $connection = 'screen_record';

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'record_log';

    /**
     * 需要写入的字段
     *
     * @var string[]
     */
    protected $fillable = [
        'image_num',
        'video_url',
        'is_end',
    ];

    /**
     * 应进行类型转换的属性
     *
     * @var array
     */
    protected $casts = [
        'image_num' => 'integer',
        'video_url' => 'string',
        'is_end' => 'integer',
    ];

    /**
     * 平台，安卓
     *
     * @var int
     */
    const ANDROID = 1;

    /**
     * 平台，苹果
     *
     * @var int
     */
    const IOS = 2;

    /**
     * 平台，PC
     *
     * @var int
     */
    const PC = 3;

    /**
     * 结束
     *
     * @var int
     */
    const END = 1;

    /**
     * 未结束
     *
     * @var int
     */
    const NOT_END = 0;

    /**
     * 操作系统中文
     *
     * @var array
     */
    const OS_TYPE_TEXT = [
        self::ANDROID => '安卓',
        self::IOS => '苹果',
        self::PC => 'PC',
    ];
}
