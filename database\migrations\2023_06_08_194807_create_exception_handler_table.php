<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExceptionHandlerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('exception_handler', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('record_id')->comment('异常记录唯一id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedInteger('handler_id')->default(0)->comment('处理人id');
            $table->string('handler', 64)->nullable()->comment('处理人');
            $table->timestamps();
            $table->index('record_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `exception_handler` comment '异常处理人表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('exception_handler');
    }
}
