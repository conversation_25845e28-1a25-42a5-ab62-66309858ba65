<?php

namespace App\Http\Logic;

use App\Components\ClickHouse\ClickHouse;
use App\Components\Helper\CommonHelper;
use App\Components\Redis\RedisHandler;
use App\Components\Redis\RedisKeyEnum;
use App\Components\Scope\CrashScope;
use App\Model\Record;
use Illuminate\Support\Facades\DB;
use PhpParser\Builder;

Class CrashLogic
{

    public static $eventName = [
        1 => 'exception_crash',
        2 => 'exception_error'
    ];

    //操作標籤（含：1新增/2編輯/3刪除）
    public function labelHandler(
        int $developerAppId,
        string $labelId,
        string $labelName,
        string $deletedAt = '',
        string $subjectName = '',
        string $explain = '',
        int $type = 0,
        string $osType = '',
        string $eventName = '',
        string $exceptionBlockId = ''
    )
    {
        try{
            $currentDate = date('Y-m-d H:i:s');
            $builder = DB::connection('exception')->table('label');
            if($labelId){
                [$developerAppId, $currentLabelName] = explode('##', $labelId);
                if($deletedAt) { //刪除
                    $builder->where('developer_app_id', $developerAppId)->where('label_name', $currentLabelName);
                    if($exceptionBlockId) $builder->where('exception_unique_id', $exceptionBlockId);
                    return $builder->delete();
                    //$builder->delete($labelId);
                }else{ //編輯
                    $record = [
                        //'id' => $labelId,
                        'label_name' => $labelName,
                        'updated_at' => $currentDate,
                    ];
                    return $builder->where('developer_app_id', $developerAppId)->where('label_name', $currentLabelName)->update($record);
                }
            }else{ //新增
                //檢測標籤是否已存在[START]
                $labelExceptionUniqueIndex = $this->createLabelExceptionUniqueIndex($subjectName,$explain,$type,$osType,$eventName,$exceptionBlockId);
                $result = $builder
                    ->where('developer_app_id', $developerAppId)
                    ->where('label_name', $labelName)
                    ->where('exception_unique_index', $labelExceptionUniqueIndex)
                    ->first();
                if($result) {
                    return "{$developerAppId}##{$labelName}";
                }
                //檢測標籤是否已存在[END]
                $record = [//template
                    'developer_app_id' => $developerAppId,
                    'subject_name' => $subjectName,
                    'explain' => $explain,
                    'type' => $type,
                    'os_type' => $osType,
                    'event_name' => $eventName,
                    'exception_unique_id' => $exceptionBlockId,//屬於clickhouse
                    'exception_unique_index' => $labelExceptionUniqueIndex,//屬於mysql（生成方式區別於：exception_unique_id）
                    'label_name' => $labelName,
                    'created_at' => $currentDate,
                    'updated_at' => $currentDate,
                ];
                $builder->insertGetId($record);
                return "{$developerAppId}##{$labelName}";
            }
        }catch (\Throwable $e){
            if(strpos($e->getMessage(), 'developerappid_labelname_exceptionuniqueid')) throw new \Exception("标签名称已存在");
            throw $e;
        }
    }

    //生成label.exception_unique_index
    public function createLabelExceptionUniqueIndex(
        string $subjectName,
        string $explain,
        int $type,
        string $osType,
        string $eventName,
        string $exceptionBlockId
    ): string
    {
        return md5("{$subjectName}_{$explain}_{$type}_{$osType}_{$eventName}_{$exceptionBlockId}");
    }

    /**
     * @param int $developerAppId
     * @param string $exceptionUniqueIndex
     * @param string $keyword
     * @param int $page
     * @param int $pageSize
     * @return array
     * author : <EMAIL>
     * datetime: 2023/06/27 15:28
     * memo : 標籤列表
     */
    public function labelList(int $developerAppId, string $exceptionBlockId, string $keyword = '', int $page = 1, int $pageSize = 15): array
    {
        $builder = DB::connection('exception')->table('label');
        $builder = $builder->select('label_name AS name')->where('developer_app_id', $developerAppId);
        if($exceptionBlockId) $builder->where('exception_unique_id', $exceptionBlockId);
        if($keyword) $builder->where('label_name','LIKE', "%{$keyword}%");
        $result = $builder->get()->toArray();
        $list = [];
        foreach ($result as &$unitRecord){
            $unitRecord = (array)$unitRecord;
            $labelId = "{$developerAppId}##{$unitRecord['name']}";
            $list[$labelId]['id'] = $labelId;
            $list[$labelId]['name'] = $unitRecord['name'];
            $list[$labelId]['num'] = ($list[$labelId]['num'] ?? 0) + 1;
        }
        $pagination = CommonHelper::pagination(array_values($list), $page, $pageSize);
        return $pagination ?? [];
    }

    //TODO:...
    public function labelDetail(int $developerAppId, string $labelId, string $labelName = ''): array
    {
        $builder = DB::connection('exception')->table('label')->where('developer_app_id', $developerAppId);
        if($labelId) {
            [$currentDeveloperAppId, $currentLabelName] = explode('##', $labelId);
            $builder->where('label_name', $currentLabelName);
        }
        if($labelName) $builder->where('label_name', $labelName);
        return $builder->limit(1)->get()->toArray();
    }

    public function matchExceptionList(int $developerAppId, string $labelName, string $eventName): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->selectRaw('MAX(`subject_name`) AS `subject_name`, MAX(`explain`) AS `explain`, MAX(`type`) AS `type`, MAX(`os_type`) AS `os_type`, `exception_unique_index`')
            ->where('developer_app_id', $developerAppId)
            ->where('event_name', $eventName)
            ->where('label_name', $labelName)
            ->groupBy('exception_unique_index')
            ->get()->toArray();
        $return = [
            'subject_name' => array_map(function($item) {
                return addslashes($item);
            }, array_column($result,'subject_name')),
            'explain' => array_map(function($item) {
                return addslashes($item);
            }, array_column($result,'explain')),
            'type' => array_map(function($item) {
                return addslashes($item);
            }, array_column($result,'type')),
            'os_type' => array_map(function($item) {
                return addslashes($item);
            }, array_column($result,'os_type')),
            //event_name
        ];
        return $return;
    }

    public function ableSearchLabelList(string $eventName, int $developerAppId, int $timeType, string $startDate, string $endDate)
    {
        $timeField = $timeType === CrashScope::TIME_TYPE_CREATE ? '`dev_create_time`' : '`event_time`';
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate);
        $table = '`data_warehouse_efficiencies`.`' . env('DATA_WAREHOUSE_TABLE_NAME') . '`';
        $sql = "SELECT `subject_name`, `explain`, `type`, `os_type`, `event_name` FROM {$table} WHERE `extra_app_id` = {$developerAppId} AND {$timeField} >= {$startTime} AND {$timeField} <= {$endTime} AND `exception_block_id` != '' AND `event_name` = '{$eventName}' GROUP BY `subject_name`, `explain`, `type`, `os_type`, `event_name`";
        $clickhouseResult = \ClickHouse::getSqlData($sql);
        $subjectNameList = array_column($clickhouseResult, 'subject_name');
        $explainList = array_column($clickhouseResult, 'explain');
        $typeList = array_column($clickhouseResult, 'type');
        $osTypeList = array_column($clickhouseResult, 'os_type');
        $labelResult = DB::connection('exception')
            ->table('label')
            ->select('label_name')
            ->where('developer_app_id', $developerAppId)
            ->where('event_name', $eventName)
            ->whereIN('subject_name', $subjectNameList)
            ->whereIN('explain', $explainList)
            ->whereIN('type', $typeList)
            ->whereIN('os_type', $osTypeList)
            ->groupBy('label_name')
            ->get()->toArray();//TODO:效率略低，優化至exception_unique_index
        $filterLabelList = [];
        foreach ($labelResult as $value){
            $filterLabelList[$value->label_name] = [
                'label_id' => "{$developerAppId}##{$value->label_name}",
                'label_name' => $value->label_name,
            ];
        }
        return array_values($filterLabelList);
    }

    public function analysisVersion(array $versionList): array
    {
        //TODO:如出現00.00.00時，則需要補零矯正
        if($versionList){
            $minVersion = $maxVersion = '0.0.0';
            $minVersionNumber = $maxVersionNumber = 0;
            foreach ($versionList as &$value){
                if(!$value) continue;
                //補零矯正[START]
                [$part1, $part2, $part3] = explode('.', $value);
                $part1 = str_pad($part1, 2, '0', STR_PAD_LEFT);
                $part2 = str_pad($part2, 2, '0', STR_PAD_LEFT);
                $part3 = str_pad($part3, 2, '0', STR_PAD_LEFT);
                $eachVersionNumber = intval("{$part1}{$part2}{$part3}");
                //-----
                //補零矯正[END]
                if($maxVersionNumber <= $eachVersionNumber || $maxVersionNumber == 0) {
                    $maxVersion = $value;
                    $maxVersionNumber = $eachVersionNumber;
                }
                if($minVersionNumber >= $eachVersionNumber || $minVersionNumber == 0) {
                    $minVersion = $value;
                    $minVersionNumber = $eachVersionNumber;
                }
            }
        }
        return [
            'max' => $maxVersion ?? '',
            'min' => $minVersion ?? '',
        ];
    }

    /**
     * @param int $developerAppId
     * @param string $eventName
     * @param array $list
     * author : <EMAIL>
     * datetime: 2023/06/27 12:02
     * memo : 匹配記錄摘要信息（避免遍歷查庫）
     */
    public function matchRecordAbstract(int $developerAppId, int $type, array &$list): array
    {
        $redisKey = RedisKeyEnum::STRING['STRING:MatchRecordAbstract:'] . md5(CommonHelper::prettyJsonEncode(func_get_args()));
        return RedisHandler::autoGet($redisKey, function() use($developerAppId, $type, &$list){
            $result = Record::query()
                ->select(['record_id', 'is_add_white_list', 'status', 'handler', 'exception_block_id'])
                ->where('developer_app_id', $developerAppId)
                ->where('type', $type/*1異常，2錯誤*/)
                ->whereIn('exception_block_id', array_column($list, 'exception_block_id'))
                ->get()->toArray();
            return array_column($result,null,'exception_block_id');
        }, 1);
    }

    /**
     * @param $builder
     * @param int $developerAppId
     * @param string $labelName
     * @param int $startTimestamp
     * @param int $endTimestamp
     * @param string $sortField
     * @param string $sortType
     * @param int $pageIndex
     * @param int $pageLimit
     * @return array
     * author : <EMAIL>
     * datetime: 2023/06/27 15:03
     * memo : 獲取{異常/錯誤}列表的總數/詳情
     */
    public function pullExceptionResult(
        &$builder,
        int $developerAppId,
        int $exceptionType,
        string $labelName,
        int $startTimestamp,
        int $endTimestamp,
        string $sortField,
        string $sortType,
        int $pageIndex,
        int $pageLimit
    ): array
    {
        $totalBuilder = (clone $builder);//需獨立賦值，否則會耦合查詢條件
        $listBuilder = (clone $builder);
        if($labelName){
            $exceptionList = $this->matchExceptionList($developerAppId ?? 0, $labelName, CrashLogic::$eventName[$exceptionType]);
            $totalBuilder
                ->whereIn('subject_name', $exceptionList['subject_name'])
                ->whereIn('explain', $exceptionList['explain'])
                ->whereIn('type', $exceptionList['type'])
                ->whereIn('os_type', $exceptionList['os_type']);
            $listBuilder
                ->whereIn('subject_name', $exceptionList['subject_name'])
                ->whereIn('explain', $exceptionList['explain'])
                ->whereIn('type', $exceptionList['type'])
                ->whereIn('os_type', $exceptionList['os_type']);
        }
        $redisKey = RedisKeyEnum::STRING['STRING:ExceptionResult:'] . md5(spl_object_hash($builder) . CommonHelper::prettyJsonEncode(func_get_args()));
        return RedisHandler::autoGet($redisKey, function()use(
            $totalBuilder,
            $listBuilder,
            $developerAppId,
            $startTimestamp,
            $endTimestamp,
            $sortField,
            $sortType,
            $pageIndex,
            $pageLimit
        ){
            $Clickhouse = new ClickHouse();
            $exceptionListSql = $Clickhouse->getSqlBindings(
                $totalBuilder
                    ->selectRaw('max(`event_time`) AS last_report_time')
                    ->whereBetween('event_time',  [$startTimestamp, $endTimestamp])
                    ->groupByRaw('`exception_block_id`')
            );
            $total = $Clickhouse->getSqlData("SELECT COUNT(1) AS total FROM ({$exceptionListSql}) LIMIT 1")[0]['total'] ?? 0;
            $list = $listBuilder
                ->selectRaw('`type` AS `category`, `subject_name` AS `name`, COUNT(1) AS `crash_count`, COUNT(distinct `server_dev_str`) AS `crash_user_count`, MAX(`event_time`) AS `last_report_time`, `explain`, `os_type`, `event_name`, MAX(`dev_create_time`) AS `last_happen_time`, MIN(`dev_create_time`) AS `first_happen_time`, MIN(`event_time`) AS `first_report_time`, arrayDistinct(groupArray(`version`)) AS `version_list`')
                ->where('extra_app_id', $developerAppId)
                ->whereBetween('event_time', [$startTimestamp, $endTimestamp])
                ->groupByRaw('`exception_block_id`, `subject_name`, `explain`, `type`, `os_type`, `event_name`')
                //->groupByRaw('`exception_unique_id`')
                ->orderBy($sortField, $sortType)
                ->forPage($pageIndex, $pageLimit)
                ->getFromCK();
            return [$total, $list];
        }, 30);
    }

}
