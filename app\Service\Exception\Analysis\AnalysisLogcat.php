<?php

/**
 * 分析Logcat报告
 * @desc 分析Logcat报告
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/08/20
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Model\ClickHouse\UserLogDataAll;
use App\Service\Exception\BaseService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AnalysisLogcat extends BaseService
{
    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;

    /**
     * 异常ID
     *
     * @var string
     */
    private $exceptionBlockId;

    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->extraAppId = $request->input('developer_app_id');
        $this->exceptionBlockId = strtoupper($request->input('exception_block_id'));
        $this->eventName = self::EVENT_NAME[$request->input('type')] ?? '';
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        // 从缓存中获取数据
        $cacheKey = 'analysis_logcat_' . $this->extraAppId . '_' . $this->exceptionBlockId . '_' . $this->eventName;
        $data = Cache::get($cacheKey, json_encode([
            'data' => [],
            'date' => Carbon::yesterday()->toDateString(),
        ]));
        // 转为数组
        $data = json_decode($data, true);
        // query
        $queries = [
            'today_log' => $this->getTodayLogQuery(),
            'time_log' => $this->getTimeLogQuery($data['date']),
        ];
        // 执行查询
        $results = (new ClickHouse())->getMultiSqlData($queries);
        // 返回结果
        $list = $this->handleData($data['data'], array_merge($results['today_log'], $results['time_log']));
        // 设置缓存
        Cache::put($cacheKey, json_encode([
            'data' => $list,
            'date' => Carbon::parse($data['date'])->subDays()->toDateString(),
        ]), 3600 * 24 * 180);
        // 返回数据
        return array_values($list);
    }

    /**
     * 处理数据
     *
     * @param $todayLog
     * @param $timeLog
     * @return array
     */
    private function handleData($list, $log)
    {
        $newList = [];
        // 判断list是否为空
        if (empty($list) && isset($log[0]) && !empty($log[0]['console_info_json'])) {
            $consoles = json_decode($log[0]['console_info_json'], true);
            // 获取列表
            $list = array_column($consoles, 'details');
            // 移除log，第一个元素
            unset($log[0]);
        }
        // 遍历log
        foreach ($log as $item) {
            // 判断是否为空
            if (empty($log[0]['console_info_json'])) {
                continue;
            }
            // 转为数组
            $consoles = json_decode($item['console_info_json'], true);
            // 判断是否为空
            if (empty($consoles)) {
                continue;
            }
            // 遍历里面内容
            foreach ($consoles as $console) {
                // 判断list是否存在
                if (in_array($console['details'], $list)) {
                    $newList[] = $console['details'];
                }
            }
            // 重新赋值
            $list = array_unique($newList);
            $newList = [];
        }
        // 返回结果
        return $list;
    }

    /**
     * 获取今天的log
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getTodayLogQuery()
    {
        return (new UserLogDataAll())->selectRaw('console_info_json')
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->where('os_type', 1)
            ->where('stream_date', Carbon::now()->toDateString())
            ->limit(30);
    }

    /**
     * 获取指定时间的log
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getTimeLogQuery($date)
    {
        return (new UserLogDataAll())->selectRaw('console_info_json')
            ->where('extra_app_id', $this->extraAppId)
            ->where(DB::raw('upper(exception_block_id)'), $this->exceptionBlockId)
            ->where('event_name', $this->eventName)
            ->where('os_type', 1)
            ->where('stream_date', $date)
            ->limit(30);
    }
}
