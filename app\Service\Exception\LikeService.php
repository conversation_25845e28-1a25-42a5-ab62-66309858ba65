<?php

/**
 * 异常模糊匹配
 * @desc 异常模糊匹配
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/13
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception;

use App\Model\StarRocks\FilterKeyword;
use App\Model\StarRocks\StarRocksDB;
use Illuminate\Support\Facades\DB;

class LikeService
{
    /**
     * 表名
     *
     * @var string
     */
    private $table = 'exception_stream_keyword';

    /**
     * 子SQL
     *
     * @var string
     */
    private $subSql;

    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;

    /**
     * 事件名称
     *
     * @var int
     */
    private $eventName;

    /**
     * 开始时间
     *
     * @var string
     */
    private $startDate;

    /**
     * 结束时间
     *
     * @var string
     */
    private $endDate;

    /**
     * 初始化
     *
     * @param string $subSql
     */
    public function __construct(string $subSql)
    {
        $this->subSql = $subSql;
    }

    /**
     * 事件名称
     *
     * @param string $eventName
     * @return $this
     */
    public function setEventName(string $eventName): LikeService
    {
        $this->eventName = $eventName;
        return $this;
    }

    /**
     * 效能后台ID
     *
     * @param int $extraAppId
     * @return $this
     */
    public function setExtraAppId(int $extraAppId): LikeService
    {
        $this->extraAppId = $extraAppId;
        return $this;
    }

    /**
     * 开始时间
     *
     * @param string $startDate
     * @return $this
     */
    public function setStartDate(string $startDate): LikeService
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * 结束时间
     *
     * @param string $endDate
     * @return $this
     */
    public function setEndDate(string $endDate): LikeService
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * like 查询
     *
     * @return string
     */
    public function likeToSQL(): string
    {
        $subSql = StarRocksDB::toSql(DB::table(DB::raw($this->table . ", UNNEST(keywords)"))
            ->selectRaw('exception_block_id, unnest as keyword')
            ->when($this->extraAppId, function ($query) {
                return $query->where('extra_app_id', $this->extraAppId);
            })
            ->when($this->eventName, function ($query) {
                return $query->where('event_name', $this->eventName);
            })
            ->when($this->startDate, function ($query) {
                return $query->where('stream_date', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query) {
                return $query->where('stream_date', '<=', $this->endDate);
            })
            ->groupBy(['exception_block_id', 'keyword']));
        return StarRocksDB::toSql(DB::table(DB::raw("({$subSql}) t"))
            ->joinSub($this->subSql, 'b', function ($join) {
                $join->on('t.keyword', '=', 'b.keyword');
            })
            ->selectRaw('exception_block_id')
            ->groupBy('exception_block_id'));
    }
}
