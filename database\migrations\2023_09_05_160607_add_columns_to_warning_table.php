<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToWarningTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->json('exception_type')->comment('异常类型：1 崩溃 2 错误');
            $table->json('effective_time')->comment('生效时间：json格式：{"start_time":"08:00:00","end_time":"20:00:00"}');
            $table->unsignedTinyInteger('monitor_range')->default(0)->comment('监控范围：1,2,3,4,5,6 -> 最近一小时TOP 5\10\20\30\50\100问题');
            $table->json('monitor_status')->comment('监控状态：1：未处理、2：处理中、3：已处理');
            $table->unsignedTinyInteger('condition_mode')->default(0)->comment('条件关系，1：任意、2：所有');
            $table->unsignedTinyInteger('is_create_tapd')->default(0)->comment('是否创建TAPD，0: 不是，1：是');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('warning', function (Blueprint $table) {
            $table->dropColumn('exception_type');
            $table->dropColumn('effective_time');
            $table->dropColumn('monitor_range');
            $table->dropColumn('monitor_status');
            $table->dropColumn('condition_mode');
            $table->dropColumn('is_create_tapd');
        });
    }
}
