<?php
/**
 * CrashService.php
 * <AUTHOR>
 * @date 2023/7/19
 * @email <EMAIL>
 */

namespace App\Service;

use App\Components\Helper\CommonHelper;
use App\Components\Helper\TapdCurl;
use Illuminate\Support\Facades\Log;

class TapdService
{
    const TAPD_CREATE_BUG_URL = '/bugs';
    const TAPD_GET_WORKSPACES_USERS = '/workspaces/users';
    const TAPD_GET_ITERATIONS_URL = '/iterations';
    const TAPD_GET_RELEASES_URL = '/releases';
    const TAPD_GET_VERSIONS_URL = '/versions';
    const TAPD_GET_WORKSPACES_INFO_URL = '/workspaces/get_workspace_info';
    const TAPD_GET_WORK_FLOWS_STATUS_MAP_URL = '/workflows/status_map';

    private static $domainKey = 'TAPD_API_SERVICE_DOMAIN';
    private static $apiUserKey = 'TAPD_API_SERVICE_ACCOUNT';
    private static $apiSecretKey = 'TAPD_API_SERVICE_SECRET';

    public static function post(string $url, array $params = [])
    {
        return CommonHelper::commonHttpPost(
            env(self::$domainKey) . $url,
            $params,
            self::authHeader()
        );
    }

    public static function get(string $url, array $params = [])
    {
        try {
            return CommonHelper::commonHttpGet(
                env(self::$domainKey) . $url,
                $params,
                self::authHeader()
            );
        } catch (\Exception $e) {
            Log::error('get请求调用tapd—api报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return [];
        }

    }

    public static function authHeader(): array
    {
        return ['Authorization' => "Basic " . base64_encode(env(self::$apiUserKey) . ':' . env(self::$apiSecretKey))];
    }

    /**
     * @param string $itemId
     * @param string $bugIdList 逗號拼接
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/20 17:05
     * memo : null
     *
     */
    public function pullBugStatus(string $itemId, string $bugIdList = ''/*成員數超過200需分頁*/): array
    {
        $api = 'https://api.tapd.cn/bugs';
        $query = [
            'workspace_id' => $itemId,
            'limit' => 200,//最大支持200
            'fields' => 'id,status',
        ];
        if($bugIdList) $query['id'] = $bugIdList;
        return CommonHelper::commonHttpGet($api, $query, self::authHeader());
    }



}
