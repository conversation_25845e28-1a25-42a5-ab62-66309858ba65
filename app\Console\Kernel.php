<?php

namespace App\Console;

use App\Console\Commands\PCCrashAnalysisCommand;
use App\Console\Commands\PCCrashAnalysisNewCommand;
use App\Console\Commands\PCCrashDmpCommand;
use App\Console\Commands\PCCrashDmpNewCommand;
use App\Console\Commands\RelationRecordPerfCommand;
use App\Console\Commands\StatAnalysisCommand;
use App\Console\Commands\StatDataCommand;
use App\Console\Commands\StatVersionCommand;
use App\Jobs\ProblemWarningJob;
use App\Jobs\WarningReportJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->job(new ProblemWarningJob, 'exception_check_warning')->hourly();

        // $schedule->job(new CheckWarningJob, 'exception_check_warning')->everyFiveMinutes();
        $schedule->job(new WarningReportJob, 'exception_warning_report')->dailyAt(WarningReportJob::DAILY_TIME);
        // $schedule->job(new LoadHomeDataCacheJob(), 'load_home_data_cache_job')->everyTenMinutes();
        //$schedule->job(new VirtualCoroutineJob(), 'virtual_coroutine_job')->everyMinute();//TODO:配置supervisor
        // 加载首页数据
        $schedule->command('load:home:data')
            ->everyTenMinutes()
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/load_home_data.log'))
            ->runInBackground();
        // 统计数据
        $schedule->command('stat:data')
            ->cron('*/' . StatDataCommand::TIME . ' * * * *')
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/stat_data.log'))
            ->runInBackground();
        $schedule->command('stat:version')
            ->cron('*/' . StatVersionCommand::TIME . ' * * * *')
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/stat_version.log'))
            ->runInBackground();
        $schedule->command('relation:record:perf')
            ->cron('*/' . RelationRecordPerfCommand::TIME . ' * * * *')
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/relation_record_perf.log'))
            ->runInBackground();
        $schedule->command('stat:analysis')
            ->cron('*/' . StatAnalysisCommand::TIME . ' * * * *')
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/stat_analysis.log'))
            ->runInBackground();
        //关键词过滤脚本
        $schedule->command('filter:exception:keyword')
            ->everyTenMinutes()
            ->withoutOverlapping(10)
            ->appendOutputTo(storage_path('logs/filter_exception_keyword.log'))
            ->runInBackground();
        //只在正式服执行的脚本
        if (config('app.env') === 'production') {
            //每天2点，执行
            $schedule->command("scheduled:del:pdb")->withoutOverlapping()->dailyAt('02:00')->runInBackground();
            //每天10点，执行
            $schedule->command("monitor:data")->withoutOverlapping()->dailyAt('10:03')->runInBackground();
            $schedule->command("monitor:exception")->withoutOverlapping()->dailyAt('10:03')->runInBackground();
            $schedule->command("cahx:warning")->withoutOverlapping()->hourly()->runInBackground();
        }
        // 每分钟执行
        $schedule->command('pc:crash:dmp')
            ->everyMinute()
            ->withoutOverlapping(PCCrashDmpCommand::TIME)
            ->runInBackground();
        $schedule->command('pc:crash:analysis')
            ->everyMinute()
            ->withoutOverlapping(PCCrashAnalysisCommand::TIME)
            ->runInBackground();
        $schedule->command('pc:crash:dmp:new')
            ->everyMinute()
            ->withoutOverlapping(PCCrashDmpNewCommand::TIME)
            ->runInBackground();
        $schedule->command('pc:crash:analysis:new')
            ->everyMinute()
            ->withoutOverlapping(PCCrashAnalysisNewCommand::TIME)
            ->runInBackground();
        $schedule->command('sync:tapd:bug')
            ->everyMinute()
            ->withoutOverlapping(5)
            ->runInBackground();
        $schedule->command('sync:record:new')
            ->everyMinute()
            ->withoutOverlapping(5)
            ->runInBackground();
        $schedule->command('refresh:data')
            ->everyMinute()
            ->withoutOverlapping(5)
            ->runInBackground();
        // 每个五分钟执行一次
        $schedule->command('match:handler')
            ->everyFiveMinutes()
            ->withoutOverlapping(5)
            ->runInBackground();
        // 每个十分钟执行一次
        $schedule->command('tapd:auto:push')
            ->everyTenMinutes()
            ->runInBackground();
        // 每个小时执行一次
        $schedule->command("perf:day:report")
            ->withoutOverlapping(60)
            ->hourly()
            ->runInBackground();
        $schedule->command("perf:week:report")
            ->withoutOverlapping(60)
            ->hourly()
            ->runInBackground();
        $schedule->command('tapd:auto:bug')
            ->hourly()
            ->runInBackground();
        $schedule->command('check:report:status')
            ->hourly()
            ->runInBackground();
        $schedule->command('handler:webhook 3')
            ->withoutOverlapping(60)
            ->hourly()
            ->runInBackground();
        // 每天十点执行
        $schedule->command("handler:webhook 1")->withoutOverlapping()->dailyAt('10:00')->runInBackground();
        // 每周一十点执行
        $schedule->command("handler:webhook 2")->withoutOverlapping()->weeklyOn(1, '10:00')->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
