<?php
/**
 * WarningScope.php
 * <AUTHOR>
 * @date 2022/10/18
 * @email <EMAIL>
 */

namespace App\Components\Scope;

use Illuminate\Database\Eloquent\Builder;

class WarningScope extends Scope
{
    protected $timeType;//查询时间类型
    //查询时间类型 1为创建时间（默认）、2为最近报警时间
    private const TIME_TYPE_CREATE = 1;
    private const TIME_TYPE_WARNING = 2;

    public function getBuilder(Builder $builder)
    {
        return $builder->when($this->developerAppId, function ($query) {
            $query->where('developer_app_id', $this->developerAppId);
        })->when($this->timeType && !empty($this->defaultStartDate) && !empty($this->defaultEndDate), function ($query) {
            $this->timeType == self::TIME_TYPE_CREATE && $query->whereBetween('created_at', [$this->startDate, $this->endDate]);
            $this->timeType == self::TIME_TYPE_WARNING && $query->whereBetween('last_warning_time', [$this->startDate, $this->endDate]);
        })->when(!empty($this->osType), function ($query) {
            $query->where('os_type', $this->osType);
        })->when(!empty($this->appVersion), function ($query) {
            $query->whereJsonContains('app_version', $this->appVersion);
        });
    }

}
