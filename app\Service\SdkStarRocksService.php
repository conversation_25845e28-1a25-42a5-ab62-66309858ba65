<?php

/**
 * SDK StarRocks操作方法
 * @desc SDK StarRocks操作方法
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2025/05/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use Illuminate\Support\Facades\Log;

class SdkStarRocksService
{
    //连接句柄
    private $conn;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $starRocks = config('starRocksSdk');
        $conn = new \mysqli($starRocks['starRocks_host_sdk'], $starRocks['starRocks_username_sdk'], $starRocks['starRocks_password_sdk'], $starRocks['starRocks_database_sdk'], $starRocks['starRocks_port_sdk']);
        if ($conn->connect_error) {
            Log::error("starRocks连接失败: " . $conn->connect_error);
            die("starRocks连接失败: " . $conn->connect_error);
        }
        $this->conn = $conn;
    }

    /**
     * 查询
     * @param $sql
     * @return array
     */
    public function query($sql)
    {
        $result = $this->conn->query($sql);
        $res = $result->fetch_all(MYSQLI_ASSOC);
        $result->free_result();
        return $res;
    }

    /**
     * 执行
     * @param $sql
     * @return bool|\mysqli_result
     */
    public function execute($sql)
    {
        return $this->conn->query($sql);
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }

    /**
     * 关闭连接
     */
    public function close()
    {
        $this->conn->close();
    }
}
