<?php

/**
 * Scope.php
 *
 * User: Dican
 * Date: 2022/9/22
 * Email: <<EMAIL>>
 */

namespace App\Components\Scope;

use App\Components\Helper\DataHelper;
use App\Model\Record;
use DateInterval;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Str;

class Scope
{
    //格式: Y-m-d
    public $startDate;
    public $endDate;

    //格式: Y-m-d H:i:s
    public $startTimestamp;
    public $endTimestamp;

    //时间戳, 格式: 数字
    public $startTime;
    public $endTime;

    //时间查询模式
    public $dateMode = 'ymdhis';
    //默认时间跨度值
    public $defaultDateRange = 29;
    public $defaultAddDateRange = 0;
    public $duration;

    public $displayDates = true;
    //分页: 每页多少行 第几页
    public $perPage;
    public $page;

    public $developerAppId; //效能appId
    public $appVersion; //APP版本
    public $serverDevStr; //设备id
    public $accountId; //账号id
    public $osType; //系统类型
    public $osVersion; //系统版本号
    public $sdkVersion; //SDK版本号
    public $deviceModel; //机型
    public $channel; //渠道
    public $province; //省份
    public $manufacturer; //厂商
    public $type; //异常类型

    //默认开始、结束日期
    public $defaultStartDate;
    public $defaultEndDate;

    // 排序字段及类型
    public $sortField;
    public $sortType;

    // 是否过滤白名单记录
    public $isFilter;

    // 是否需要进行时间筛选
    public $isTimeFilter; // 0不需要 1需要

    public $useDuration;

    public $sdkPackageName; // 包名

    public $crashType; // 异常类型

    public $innerVersion; // 游戏资源版本

    public function __construct($params = [])
    {
        //参数key都是下划线, 将参数key改成小驼峰
        foreach ($params as $k => $v) {
            $k = Str::camel($k);
            if (property_exists($this, $k)) {
                $this->{$k} = $v;
            }
        }
        $this->type = $params['type'] ?? Record::TYPE_CRASH; //默认是崩溃
        $this->defaultStartDate = $params['start_date'] ?? null;
        $this->defaultEndDate = $params['end_date'] ?? null;
        $this->isTimeFilter = $params['is_time_filter'] ?? 1; // 默认需要拼接时间筛选条件
        $this->useDuration = $params['use_duration'] ?? 0;
        $this->innerVersion = json_decode($params['inner_version'] ?? '', true);

        $dateRange = $params['dateRange'] ?? $this->defaultDateRange;
        $dateAddRange = $params['dateAddRange'] ?? $this->defaultAddDateRange;

        $dateTime = new DateTime();
        switch ($this->dateMode) {
            case 'ymd': //年月日
                $this->endDate = isset($params['end_date']) ? DataHelper::asDate(strtotime($params['end_date'])) : $dateTime->add(new DateInterval("P{$dateAddRange}D"))->format('Y-m-d');
                $this->startDate = isset($params['start_date']) ? DataHelper::asDate(strtotime($params['start_date'])) : $dateTime->sub(new DateInterval("P{$dateRange}D"))->format('Y-m-d');
                break;
            case 'ymdhis': //年月日时分秒
                $this->endDate = $params['end_date'] ?? DataHelper::asDateTime();
                $this->startDate = $params['start_date'] ?? $dateTime->sub(new DateInterval("PT{$dateRange}M"))->format('Y-m-d H:i:s');
                break;
            case 'ym-ym': //跨月份查询
                $this->endDate = isset($params['end_date'])
                    ? date('Y-m-t', is_numeric($params['end_date']) ? $params['end_date'] / 1000 : strtotime($params['end_date']))
                    : DataHelper::asDate();
                $this->startDate = isset($params['start_date'])
                    ? date('Y-m-01', is_numeric($params['start_date']) ? $params['start_date'] / 1000 : strtotime($params['start_date']))
                    : $dateTime->sub(new DateInterval("P{$dateRange}M"))->format('Y-m-01');
                break;
            case 'ym': //单月查询
                $this->startDate = isset($params['start_date'])
                    ? date('Y-m-01', is_numeric($params['start_date']) ? $params['start_date'] / 1000 : strtotime($params['start_date']))
                    : $dateTime->sub(new DateInterval("P{$dateRange}M"))->format('Y-m-01');
                $this->endDate = date('Y-m-t', strtotime($this->startDate));
                break;
            case 'y-y': //按年查询
                $this->endDate = isset($params['end_date'])
                    ? date('Y-12-t', is_numeric($params['end_date']) ? $params['end_date'] / 1000 : strtotime($params['end_date']))
                    : DataHelper::asDate();
                $this->startDate = isset($params['start_date'])
                    ? date('Y-01-01', is_numeric($params['start_date']) ? $params['start_date'] / 1000 : strtotime($params['start_date']))
                    : $dateTime->sub(new DateInterval("P{$dateRange}Y"))->format('Y-01-01');
                break;
            case 'day':
                $this->endDate = isset($params['end_date'])
                    ? date('Y-m-d', is_numeric($params['end_date']) ? $params['end_date'] / 1000 : strtotime($params['end_date']))
                    : DataHelper::asDate();
                $this->startDate = $this->endDate;
                break;
        }

        $this->startTimestamp = strlen($this->startDate) == 19 ? $this->startDate : $this->startDate . ' 00:00:00';
        $this->endTimestamp = strlen($this->endDate) == 19 ? $this->endDate : $this->endDate . ' 23:59:59';

        $this->startTime = strtotime($this->startTimestamp);
        $this->endTime = strtotime($this->endTimestamp);
        $this->duration = $this->endTime - $this->startTime;

        $this->perPage = request()->input('per_page', 15);
        $this->page = request()->input('page', 1);
    }

    public function getBuilder(Builder $builder)
    {
        return $builder->when($this->developerAppId, function ($query) {
            $query->where('extra_app_id', (string)$this->developerAppId);
        })->when(isset($this->serverDevStr), function ($query) {
            $query->where('server_dev_str', 'like', '%' . $this->serverDevStr . '%');
        })->when(isset($this->accountId), function ($query) {
            $query->whereRaw('cast(`account_id` as char) like ?', ['%' . $this->accountId . '%']);
        })->when(!empty($this->appVersion), function ($query) {
            $query->whereIn('app_version', json_decode($this->appVersion, true));
        })->when(!empty($this->sdkVersion), function ($query) {
            $query->whereIn('version', json_decode($this->sdkVersion, true));
        })->when(!empty($this->osType), function ($query) {
            if (is_array($this->osType)) {
                $query->whereIn('os_type', $this->osType);
            } else {
                $query->where('os_type', (string)$this->osType);
            }
        })->when(!empty($this->deviceModel), function ($query) {
            $query->whereIn('device_model', json_decode($this->deviceModel, true));
        })->when(!empty($this->channel), function ($query) {
            $query->whereIn('release_store', json_decode($this->channel, true));
        })->when(!empty($this->province), function ($query) {
            $query->whereIn('current_province', json_decode($this->province, true));
        })->when(!empty($this->manufacturer), function ($query) {
            $query->whereIn('manufacturer', json_decode($this->manufacturer, true));
        })->when(!empty($this->osVersion), function ($query) {
            $query->whereIn('os_version', json_decode($this->osVersion, true));
        })->when(!empty($this->sdkPackageName), function ($query) {
            $query->whereIn("sdk_package_name", json_decode($this->sdkPackageName, true));
        })->when(!empty($this->crashType), function ($query) {
            $query->whereIn("type", json_decode($this->crashType, true));
        })->when(!empty($this->defaultStartDate), function ($query) {
            $time = strtotime($this->defaultStartDate);
            $query->where("stream_time", '>=', $time);
            $query->where("stream_date", '>=', date('Y-m-d', $time));
        })->when(!empty($this->defaultEndDate), function ($query) {
            $time = strtotime($this->defaultEndDate);
            $query->where("stream_time", '<=', $time);
            $query->where("stream_date", '<=', date('Y-m-d', $time));
        });
    }
}
