<?php

/**
 * 崩溃和异常分析列表数据
 * @desc 崩溃和异常分析列表数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Analysis;

use App\Components\ClickHouse\ClickHouse;
use App\Http\Logic\TapdLogic;
use App\Model\ClickHouse\ExceptionStat;
use App\Model\ClickHouse\ExceptionVersion;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\Record;
use App\Model\StarRocks\FilterKeyword;
use App\Model\StarRocks\StarRocksDB;
use App\Model\TapdBug;
use App\Service\Exception\BaseService;
use App\Service\Exception\KeyWordSearch;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SearchList extends BaseService
{
    use KeyWordSearch;

    /**
     * 24小时内
     */
    public const HOUR_24 = '24_hour';

    /**
     * 7天内
     */
    public const DAY_7 = '7_day';

    /**
     * HTTP请求对象
     *
     * @var Request
     */
    private $request;

    /**
     * 效能后台ID
     *
     * @var int
     */
    private $extraAppId;

    /**
     * 开始时间时间戳
     *
     * @var int
     */
    private $startTime;

    /**
     * 结束时间时间戳
     *
     * @var int
     */
    private $endTime;

    /**
     * 开始时间
     *
     * @var string
     */
    private $startDate;

    /**
     * 结束时间
     *
     * @var string
     */
    private $endDate;

    /**
     * 事件名称
     *
     * @var string
     */
    private $eventName;

    /**
     * 初始化
     *
     * @param $request
     */
    public function __construct($request)
    {
        $this->request = $request;
        $this->extraAppId = $request->input('developer_app_id');
        $this->startTime = strtotime($this->request->input('start_date'));
        $this->endTime = strtotime($this->request->input('end_date'));
        $this->startDate = date('Y-m-d', $this->startTime);
        $this->endDate = date('Y-m-d', $this->endTime);
        // 事件名称
        $this->eventName = self::EVENT_NAME[$this->request->input('type')] ?? '';
    }

    /**
     * 获取列表
     *
     * @param $orderField
     * @param $orderSequence
     * @param $pageIndex
     * @param $pageLimit
     * @return array
     */
    public function getList($orderField, $orderSequence, $pageIndex, $pageLimit): array
    {
        $ClickHouse = new ClickHouse();
        $ExceptionStat = new ExceptionStat();
        //stat[START]
        $subSql = $this->getSubSql()->groupBy('exception_block_id');
        $matchSql = $this->getSubSql('exception_block_id, count(*) as match_num, count(distinct server_dev_str) as dev_match_num')->groupBy('exception_block_id');
        $statBuilder = $ExceptionStat
            ->where('extra_app_id', $this->extraAppId) //where()自動加反引號
            ->when($this->eventName, function ($query) {
                return $query->where('event_name', $this->eventName);
            })
            ->joinSub($subSql, 't', function ($join) use ($ExceptionStat) {
                $join->on('t.exception_block_id', '=', $ExceptionStat->getTable() . '.exception_block_id');
            })
            ->joinSub($matchSql, 'match', function ($join) use ($ExceptionStat) {
                $join->on('match.exception_block_id', '=', $ExceptionStat->getTable() . '.exception_block_id');
            });
        //other[END]
        //拼装并行查询sql
        $queries['total'] = $this->getSubSql("COUNT(DISTINCT exception_block_id) AS `total`");
        $queries['list'] = (clone $statBuilder)->selectRaw("{$ExceptionStat->getTable()}.exception_block_id, min_stream_time AS `stream_min_stream_time`, max_stream_time AS `stream_max_stream_time`, server_dev_str_count AS `device_num`, count AS `block_num`, match.match_num, match.dev_match_num")
            ->orderBy($orderField, $orderSequence)
            ->skip(($pageIndex - 1) * $pageLimit - 1)
            ->limit($pageLimit);
        //多条并行查询
        $results = $ClickHouse->getMultiSqlData($queries);
        $total = (int) ($results['total'][0]['total'] ?? 0);
        $list = $results['list'] ?? [];
        $format = [];
        if ($list) {
            $format = $this->format($list);
        }
        return [
            $total,
            $format,
        ];
    }

    /**
     * 获取子查询SQL
     *
     * @param string $selectRaw
     * @return string|Builder
     */
    private function getSubSql(string $selectRaw = 'exception_block_id')
    {
        $builder = (new UserLogDataAll())
            ->selectRaw($selectRaw)
            ->where('extra_app_id', $this->extraAppId)
            ->when($this->eventName, function ($query) {
                return $query->where('event_name', $this->eventName);
            })
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(!empty($this->request->input('os_type')), function ($query) {
                return $query->where('os_type', $this->request->input('os_type'));
            })
            ->where(function (Builder $query) {
                return $query->orWhere('server_dev_str', 'like', "%{$this->request->input('keywords')}%")
                    ->orWhere('account_id', 'like', "%{$this->request->input('keywords')}%")
                    ->orWhere('role_name', 'like', "%{$this->request->input('keywords')}%");
            });
        //过滤关键词
        $subSql = StarRocksDB::toSql(DB::table(FilterKeyword::TABLE_NAME)
            ->select(['keyword'])
            ->where('developer_app_id', $this->extraAppId));
        $this->filterKeyWordWhere($builder, $subSql);
        return $builder;
    }

    /**
     *关键词where条件
     *
     * @param $builder
     * @param string $subSql
     * @param string $opt
     * @return void
     */
    private function filterKeyWordWhere($builder, string $subSql, string $opt = 'not in'): void
    {
        $this->keyWordSearch($builder, $subSql, [
            'extra_app_id' => $this->extraAppId,
            'event_name' => $this->eventName,
            'start_date' => date('Y-m-d', strtotime($this->request->input('start_date'))),
            'end_date' => date('Y-m-d', strtotime($this->request->input('end_date'))),
        ], $opt);
    }

    /**
     * @param array $list
     * @return array
     * author : <EMAIL>
     * datetime: 2023/07/11 13:44
     * memo : 格式化
     */
    public function format(array $list): array
    {
        $blockIds = array_column($list, 'exception_block_id');
        $results = $this->getRelationData($blockIds);
        //异常详细信息
        $exceptionDetail = array_column($results['list'], null, 'exception_block_id');
        //版本
        $versions = [];
        foreach ($results['version'] as $item) {
            $versions[$item['exception_block_id']][] = $item['version'];
        }
        //标签
        $labelList = $this->getLabelListByExceptionList($blockIds);
        //处理人、状态、白名单
        $recordAbstractList = $this->matchRecordAbstract($blockIds);
        //tabp
        $tapdBugList = $this->getTapdBugList($blockIds);
        //格式化处理
        $format = [];
        foreach ($list as $value) {
            $format[] = [
                'exception_block_id' => $value['exception_block_id'],
                'category' => (int) $exceptionDetail[$value['exception_block_id']]['type'],
                'os_type' => (int) $exceptionDetail[$value['exception_block_id']]['os_type'],
                'event_name' => $exceptionDetail[$value['exception_block_id']]['event_name'],
                'type' => $exceptionDetail[$value['exception_block_id']]['event_name'] == 'exception_error' ? 2 : 1,
                'name' => $exceptionDetail[$value['exception_block_id']]['subject_name'],
                'explain' => $exceptionDetail[$value['exception_block_id']]['explain'],
                'crash_count' => (int) $value['block_num'],
                'crash_user_count' => (int) $value['device_num'],
                'first_happen_time' => (int) $value['stream_min_stream_time'],
                'first_report_time' => (int) $value['stream_min_stream_time'],
                'last_happen_time' => (int) $value['stream_max_stream_time'],
                'last_report_time' => (int) $value['stream_max_stream_time'],
                'handler' => $recordAbstractList[$value['exception_block_id']]['handler'] ?? '',
                'handler_id' => $recordAbstractList[$value['exception_block_id']]['handler_id'] ?? '',
                'status' => $recordAbstractList[$value['exception_block_id']]['status'] ?? 1,
                'record_id' => $recordAbstractList[$value['exception_block_id']]['record_id'] ?? 0,
                'is_white_list' => $recordAbstractList[$value['exception_block_id']]['is_add_white_list'] ?? 0,
                'bug_id' => $tapdBugList[$value['exception_block_id']]['tapd_bug_id'] ?? '',
                'label_list' => $labelList[$value['exception_block_id']] ?? [],
                'match_num' => (int) $value['match_num'],
                'dev_match_num' => (int) $value['dev_match_num'],
                'version' => $this->getMinMaxVersions($versions[$value['exception_block_id']] ?? []),
                'time_range' => $this->getTimeRange((int) $value['stream_min_stream_time']),
            ];
        }
        return $format;
    }

    /**
     * 获取时间范围
     *
     * @param $value
     * @return int|string
     */
    private function getTimeRange($value)
    {
        $list = [
            self::HOUR_24 => 86400,
            self::DAY_7 => 604800,
        ];
        //判断value值是否是字符串，是则返回时间戳
        if (is_string($value)) {
            return $list[$value] ?? 0; //没有默认为0
        }
        //判断是否int类型
        if (is_numeric($value)) {
            $timeRange = time() - $value;
            //循环判断
            foreach ($list as $key => $item) {
                //判断时间是否符合
                if ($timeRange < $item) {
                    return $key;
                }
            }
            return "";
        }
        //返回null
        return null;
    }

    /**
     * 获取关联数据
     *
     * @param array $blockIds
     * @return array
     */
    private function getRelationData(array $blockIds): array
    {
        //列表
        $queries['list'] = (new UserLogDataAll())
            ->selectRaw('`exception_block_id`, any_value(`event_name`) AS `event_name`, any_value(`type`) AS `type`, any_value(`os_type`) AS `os_type`, any_value(`subject_name`) AS `subject_name`, any_value(`explain_desc`) AS `explain`')
            ->where('extra_app_id', $this->extraAppId)
            ->whereIn('exception_block_id', $blockIds)
            ->when($this->eventName, function ($query) {
                return $query->where('event_name', $this->eventName);
            })
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_time', '<=', $this->endTime)
            ->where('stream_date', '>=', $this->startDate)
            ->where('stream_date', '<=', $this->endDate)
            ->when(!empty($this->request->input('os_type')), function ($query) {
                return $query->where('os_type', $this->request->input('os_type'));
            })
            ->where(function (Builder $query) {
                return $query->orWhere('server_dev_str', 'like', "%{$this->request->input('keywords')}%")
                    ->orWhere('account_id', 'like', "%{$this->request->input('keywords')}%")
                    ->orWhere('role_name', 'like', "%{$this->request->input('keywords')}%");
            })
            ->groupByRaw('`exception_block_id`');
        //版本
        $queries['version'] = (new ExceptionVersion())
            ->select(['exception_block_id', 'version'])
            ->where('extra_app_id', $this->extraAppId)
            ->whereIn('exception_block_id', $blockIds)
            ->when($this->eventName, function ($query) {
                return $query->where('event_name', $this->eventName);
            })
            ->where('stream_date', '>=', Carbon::parse($this->startDate)->subMonth()->toDateString())
            ->where('stream_date', '<=', $this->endDate)
            ->when(!empty($this->request->input('os_type')), function ($query) {
                return $query->where('os_type', $this->request->input('os_type'));
            })
            ->groupBy('exception_block_id', 'version');
        //多条并行查询
        return (new ClickHouse())->getMultiSqlData($queries);
    }

    /**
     * 根据异常列表获取标签列表
     *
     * @param array $blockIds
     * @return array
     */
    private function getLabelListByExceptionList(array $blockIds): array
    {
        $result = DB::connection('exception')
            ->table('label')
            ->select('label_name', 'exception_unique_id')
            ->where('developer_app_id', $this->extraAppId)
            ->whereIn('exception_unique_id', $blockIds)
            ->get()->toArray();
        $format = [];
        foreach ($result as $record) { //避免循環查庫
            $format[$record->exception_unique_id][] = [
                'id' => "{$this->extraAppId}##" . $record->label_name,
                'label_name' => $record->label_name,
                'exception_block_id' => $record->exception_unique_id,
            ];
        }
        return $format;
    }

    /**
     * 匹配记录摘要信息
     *
     * @param array $blockIds
     * @return array
     */
    private function matchRecordAbstract(array $blockIds): array
    {
        $result = Record::query()
            ->select(['record_id', 'is_add_white_list', 'status', 'handler_id', 'handler', 'exception_block_id'])
            ->where('developer_app_id', $this->extraAppId)
            ->whereIn('exception_block_id', $blockIds)
            ->when($this->eventName, function ($query) {
                return $query->where('type', self::TYPE[$this->eventName]);
            })
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    /**
     * 根据exception_block_id获取最新且未解绑的缺陷记录
     *
     * @param array $blockIds
     * @return array
     */
    private function getTapdBugList(array $blockIds): array
    {
        // 获取当前项目绑定的item_id
        $tapdAccountDetail = TapdLogic::pullTapdAccountDetailCache($this->extraAppId);
        $result = TapdBug::query()
            ->select(['exception_block_id', 'tapd_bug_id'])
            ->where('developer_app_id', $this->extraAppId)
            ->when($this->eventName, function ($query) {
                return $query->where('type', self::TYPE[$this->eventName]);
            })
            ->where('bind_status', 1) // 1 已绑定 2未绑定
            ->when(!empty($tapdAccountDetail), function ($query) use ($tapdAccountDetail) {
                $query->where('tapd_account_id', $tapdAccountDetail['id']);
            })
            ->whereIn('exception_block_id', $blockIds)
            ->get()->toArray();
        return array_column($result, null, 'exception_block_id');
    }

    /**
     * 获取最大和最小版本
     *
     * @param array $versions
     * @return array
     */
    private function getMinMaxVersions(array $versions): array
    {
        $minVersion = $versions[0] ?? '';
        $maxVersion = $versions[0] ?? '';
        foreach ($versions as $version) {
            if ($this->compareVersions($version, $minVersion) < 0) {
                $minVersion = $version;
            }
            if ($this->compareVersions($version, $maxVersion) > 0) {
                $maxVersion = $version;
            }
        }
        return [
            'min' => $minVersion,
            'max' => $maxVersion,
        ];
    }

    /**
     * 比较版本
     *
     * @param $version1
     * @param $version2
     * @return int
     */
    private function compareVersions($version1, $version2): int
    {
        $version1Parts = explode('.', $version1);
        $version2Parts = explode('.', $version2);

        $numParts = max(count($version1Parts), count($version2Parts));

        for ($i = 0; $i < $numParts; $i++) {
            $part1 = isset($version1Parts[$i]) ? intval($version1Parts[$i]) : 0;
            $part2 = isset($version2Parts[$i]) ? intval($version2Parts[$i]) : 0;
            if ($part1 < $part2) {
                return -1;
            } elseif ($part1 > $part2) {
                return 1;
            }
        }

        return 0;
    }
}
