<?php

/**
 * WarningRule.php
 *
 * User: Dican
 * Date: 2022/10/17
 * Email: <<EMAIL>>
 */

namespace App\Model;


/**
 * 异常预警触发条件模型
 * App\Model\WarningRule
 *
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule query()
 * @mixin \Eloquent
 * @property int $rule_id
 * @property int $warning_id 预警id
 * @property int $schedule_time 频率/计划时间
 * @property int $index 指标
 * @property string $operator 运算符
 * @property int $value 值
 * @property int value_type 值类型
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereOperator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereRuleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereScheduleTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\WarningRule whereWarningId($value)
 */
class WarningRule extends BaseModel
{
    use ModelTrait;

    const INDEX_CRASH_COUNT = 1;
    const INDEX_CRASH_RATE = 2;
    const INDEX_CRASH_USER_COUNT = 3;
    const INDEX_CRASH_USER_RATE = 4;
    const INDEX = [
        self::INDEX_CRASH_COUNT => '次数：',
        self::INDEX_CRASH_RATE => '次数率：',
        self::INDEX_CRASH_USER_COUNT => '影响设备数：',
        self::INDEX_CRASH_USER_RATE => '影响设备占比：',
    ];
    const VALUE_TYPE = [
        1 => '数值',
        2 => '和昨日同比数值增长',
        3 => '和昨日同比百分比增长',
        4 => '和7日前同比数值增长',
        5 => '和7日前同比百分比增长',
        6 => '1小时环比数值增长',
        7 => '1小时环比百分比增长',
    ];
    public $foreignKey = 'warning_id';

    //指标 崩溃数、崩溃率、影响用户数、影响用户占比
    public $fillable = ['warning_id', 'schedule_time', 'index', 'operator', 'value', 'value_type'];
    public $validateRule = [
        'warning_id' => 'required|integer',
        'schedule_time' => 'required|integer',
        'index' => 'required|integer',
        'operator' => 'required|string',
        'value' => 'required|numeric',
        'value_type' => 'required|numeric',
    ];
    protected $table = 'warning_rule';
    protected $primaryKey = 'rule_id';
    protected $hidden = ['value_format'];

    /**
     * 关联预警表
     */
    public function warning()
    {
        return $this->belongsTo(Warning::class, 'warning_id', 'warning_id');
    }

    /**
     * 值格式化-访问器
     * @return int|string
     */
    public function getValueFormatAttribute()
    {
        switch ($this->index) {
            case self::INDEX_CRASH_COUNT:
            case self::INDEX_CRASH_USER_COUNT:
                return $this->operator . intval($this->value);
            case self::INDEX_CRASH_RATE:
            case self::INDEX_CRASH_USER_RATE:
                return $this->operator . $this->value * 100 . '%';
            default:
                return $this->operator . $this->value;
        }
    }
}
