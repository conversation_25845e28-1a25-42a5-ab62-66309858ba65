<?php

/**
 * 创建tapd缺陷的队列脚本
 * @desc 创建tapd缺陷的队列脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs;

use App\Service\AnalysisService;
use App\Service\Exception\Tapd\Create;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;

class CreateTapdBugJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 执行时长
     *
     * @var int
     */
    public $timeout = 1800;

    /**
     * 数据
     *
     * @var array
     */
    private $data;

    /**
     * redis 的 key
     *
     * @var string
     */
    private $key;

    /**
     * 匹配到的关键词
     *
     * @var string
     */
    private $keyword;

    /**
     * 初始化函数
     *
     * @return void
     */
    public function __construct($data, $key)
    {
        $this->data = $data;
        $this->key = $key;
    }

    /**
     * 执行逻辑
     *
     * @return void
     */
    public function handle()
    {
        // 获取redis实例
        $redis = Redis::connection();
        // +1
        $redis->hIncrby($this->key, 'success_num', 1);
        // 获取所有数据
        $data = $redis->hGetAll($this->key);
        // json转为数组
        $data['config'] = json_decode($data['config'], true);
        $data['tapd_auto_config'] = json_decode($data['tapd_auto_config'], true);
        // 创建tapd单参数
        $params = [
            'developer_app_id' => $data['tapd_auto_config']['developer_app_id'],
            'exception_block_id' => $this->data['exception_block_id'],
            'title' => urldecode("【Hitbug一键提单】 {$this->data['subject_name']} - " . date('Y-m-d H:i:s', $this->data['stream_time'])),
            'iteration_id' => $data['config']['iteration_id'] ?? '',
            'priority' => $data['config']['priority'] ?? '',
            'release_id' => $data['config']['release_id'] ?? '',
            'reporter' => 'HitBug自动提单',
            'reporter_id' => 0,
            'severity' => $data['config']['severity'] ?? '',
            'type' => $this->data['event_name'] == 'exception_crash' ? 1 : 2,
            'version_report' => $data['config']['version_report'] ?? '',
        ];
        //先生成current_owner
        $params['current_owner'] = $this->getCurrentOwner($data['config'], $data['tapd_auto_config']);
        //再生产description
        $params['description'] = urldecode($this->getDescription($data['tapd_auto_config']));
        //判断developer_app_id是否等于28，如果是要配置QA
        if ($params['developer_app_id'] == 28) {
            //QA字段
            $params['custom_field_8'] = '张峰铭';
            // 预计修复时间
            $params['custom_field_11'] = Carbon::now()->addDays(3)->toDateTimeString();
            // 预计结束时间
            $params['due'] = Carbon::parse($params['custom_field_11'])->addDays()->toDateTimeString();
            // 判断是否崩溃异常
            if ($this->data['event_name'] == 'exception_crash') {
                // 设置优先级，紧急
                $params['priority'] = 'urgent';
            }
        }
        // 创建tapd单
        (new Create($params))->save();
        // 睡眠1秒
        sleep(1);
    }

    /**
     * 获取处理人
     *
     * @param array $config
     * @param array $tapdAutoConfig
     * @return string
     */
    private function getCurrentOwner($config, $tapdAutoConfig)
    {
        $userMap = $tapdAutoConfig['user_map'] ?? [];
        foreach ($userMap as $item) {
            // 转为数组
            $item = is_string($item) ? json_decode($item, true) : $item;
            // 是否转换成功
            if (empty($item)) {
                continue;
            }
            if (empty($item['keyword']) || empty($item['user'])) {
                continue;
            }
            // 判断标题和异常说明是否命中关键词
            if (stripos(urldecode($this->data['subject_name']), $item['keyword']) !== false || stripos(urldecode($this->data['explain_desc']), $item['keyword']) !== false) {
                // 赋值匹配的关键词
                $this->keyword = $item['keyword'];
                // 增加+1
                Redis::hIncrby($this->key, implode('、', $item['user']) . "({$item['keyword']})", 1);
                // 返回匹配的用户
                return implode(';', $item['user']);
            }
        }
        return implode(';', $config['current_owner']);
    }

    /**
     * 获取描述
     *
     * @param array $tapdAutoConfig
     * @return string
     */
    private function getDescription($tapdAutoConfig)
    {
        $currentTime = date('Y-m-d H:i:s', $this->data['stream_time']);
        $startTime = date('Y-m-d H:i:s', $this->data['min_stream_time']);
        // 判断是否崩溃
        $type = 2;
        $this->data['origin_stacks_json'] = json_decode($this->data['origin_stacks_json'], true);
        if (isset($this->data['origin_stacks_json'][0]['frame'])) {
            $type = 1;
            $stack = (new AnalysisService(null))->getThreadStackText($this->data['origin_stacks_json'][0] ?? []);
        } else {
            $stack = $this->data['origin_stacks_json'][0]['stacks'][0] ?? '';
        }
        // 判断环境
        $test = config('app.env') === 'production' ? '' : 'test-';
        //获取复现的包名
        $sdkPackageName = implode('、', json_decode($this->data['sdk_package_name'], true));
        // 获取复现的资源版本
        $innerVersion = implode('、', json_decode($this->data['inner_version'], true));

        return <<<TEXT

        首次发生时间: {$startTime}
        <br><br>
        最近上报时间:  {$currentTime}
        <br><br>
        复现包名:  {$sdkPackageName}
        <br><br>
        复现资源版本:  {$innerVersion}
        <br><br>
        提单关键字:  {$this->keyword}
        <br><br>
        异常详情地址: <a href='https://{$test}developer.shiyue.com/console/my-app/{$tapdAutoConfig['developer_app_id']}/app-monitor/exception-overview/error-list/{$this->data['exception_block_id']}?type={$type}'>https://{$test}developer.shiyue.com/console/my-app/{$tapdAutoConfig['developer_app_id']}/app-monitor/exception-overview/error-list/{$this->data['exception_block_id']}?type={$type}</a>
        <br><br>
        异常说明: <pre>{$this->data['explain_desc']}</pre>
        <br>
        出错堆栈: <pre><br>{$stack}</pre>

TEXT;
    }
}
