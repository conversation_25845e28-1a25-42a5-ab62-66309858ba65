<?php

/**
 * 开关配置
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Http\Validation\SwitchValidation;
use App\Model\BaseModel;
use App\Model\SwitchConfig;
use App\Service\MonitorConfigChangeService;
use Exception;
use Illuminate\Http\JsonResponse;

class SwitchController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    const PER_PAGE = 10;

    /**
     * 开关列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3489
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->developerAppId()
            ->validate();

        try {
            //获取数据
            $res = SwitchConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->get()
                ->toArray();
            $default = [
                'id' => 0,
                'developer_app_id' => $params['developer_app_id'],
                'time_interval' => 30,
                'cache_size' => 2,
                'status' => SwitchConfig::STATUS_ON,
                'is_allow_error_report' => SwitchConfig::ALLOW_ERROR_REPORT,
                'is_allow_crash_report' => SwitchConfig::ALLOW_CRASH_REPORT,
                'is_allow_screenshot' => SwitchConfig::NOT_ALLOW_SCREENSHOT,
                'is_allow_oom_report' => SwitchConfig::ALLOW_OOM_REPORT,
                'is_allow_anr_report' => SwitchConfig::ALLOW_ANR_REPORT,
                'screenshot_num' => 0, /* 截图数量 */
                'screenshot_cd' => 0, /* 截图CD */
            ];
            //默认返回的数据
            $list = [
                'android' => array_merge($default, ['os_type' => BaseModel::ANDROID]),
                'ios' => array_merge($default, ['os_type' => BaseModel::IOS]),
                'pc' => array_merge($default, ['os_type' => BaseModel::PC]),
                'mini' => array_merge($default, ['os_type' => BaseModel::MINI]),
                'harmony' => array_merge($default, ['os_type' => BaseModel::HARMONY]),
            ];
            $index = 'android';
            //处理数据
            foreach ($res as $value) {
                switch ($value['os_type']) {
                    case BaseModel::ANDROID:
                        $index = 'android';
                        break;
                    case BaseModel::IOS:
                        $index = 'ios';
                        break;
                    case BaseModel::PC:
                        $index = 'pc';
                        break;
                    case BaseModel::MINI:
                        $index = 'mini';
                        break;
                    case BaseModel::HARMONY:
                        $index = 'harmony';
                        break;
                }
                $list[$index]['id'] = $value['id'];
                $list[$index]['time_interval'] = $value['time_interval'];
                $list[$index]['cache_size'] = intval(bcdiv($value['cache_size'], 1024));
                $list[$index]['status'] = $value['status'];
                $list[$index]['is_allow_error_report'] = $value['is_allow_error_report'];
                $list[$index]['is_allow_crash_report'] = $value['is_allow_crash_report'];
                $list[$index]['is_allow_screenshot'] = $value['is_allow_screenshot'] ?? SwitchConfig::NOT_ALLOW_SCREENSHOT;
                $list[$index]['is_allow_oom_report'] = $value['is_allow_oom_report'] ?? SwitchConfig::ALLOW_OOM_REPORT;
                $list[$index]['is_allow_anr_report'] = $value['is_allow_anr_report'] ?? SwitchConfig::ALLOW_ANR_REPORT;
                $list[$index]['screenshot_num'] = $value['screenshot_num'] ?? 0;
                $list[$index]['screenshot_cd'] = $value['screenshot_cd'] ?? 0;
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list]);
        } catch (Exception $e) {
            \Log::error('获取开关列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3492
     * @return JsonResponse
     */
    public function add(): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->developerAppId()->osType()->timeInterval()->cacheSize()
            ->status()->isAllowErrorReport()->isAllowCrashReport()->isAllowAnrReport()
            ->isAllowScreenshot()->screenshotNum()->screenshotCd()->isAllowOomReport()
            ->validate();

        try {
            //添加开关
            $config = SwitchConfig::query()->create([
                'developer_app_id' => $params['developer_app_id'],
                'os_type' => $params['os_type'],
                'time_interval' => $params['time_interval'],
                'cache_size' => $params['cache_size'],
                'status' => $params['status'],
                'is_allow_error_report' => $params['is_allow_error_report'],
                'is_allow_crash_report' => $params['is_allow_crash_report'],
                'is_allow_anr_report' => $params['is_allow_anr_report'],
                'is_allow_screenshot' => $params['is_allow_screenshot'],
                'is_allow_oom_report' => $params['is_allow_oom_report'],
                'screenshot_num' => $params['screenshot_num'],
                'screenshot_cd' => $params['screenshot_cd'],
            ]);
            //刷新缓存
            SwitchConfig::flushCache($config->developer_app_id);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('添加开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3494
     * @return JsonResponse
     */
    public function edit(): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->developerAppId()->switchId()->osType()->timeInterval()->isAllowAnrReport()
            ->cacheSize()->status()->isAllowErrorReport()->isAllowCrashReport()
            ->isAllowScreenshot()->screenshotNum()->screenshotCd()->isAllowOomReport()
            ->validate();

        try {
            // 创建监控服务类
            $service = null;
            if (!$params['switch_id']) {
                //添加开关
                $config = SwitchConfig::query()->create([
                    'developer_app_id' => $params['developer_app_id'],
                    'os_type' => $params['os_type'],
                    'time_interval' => $params['time_interval'],
                    'cache_size' => $params['cache_size'],
                    'status' => $params['status'],
                    'is_allow_error_report' => $params['is_allow_error_report'],
                    'is_allow_crash_report' => $params['is_allow_crash_report'],
                    'is_allow_screenshot' => $params['is_allow_screenshot'],
                    'is_allow_oom_report' => $params['is_allow_oom_report'],
                    'is_allow_anr_report' => $params['is_allow_anr_report'],
                    'screenshot_num' => $params['screenshot_num'],
                    'screenshot_cd' => $params['screenshot_cd'],
                ]);
                // 创建监控服务类
                $service = new MonitorConfigChangeService(null, "HitBug添加开关配置");
            } else {
                //修改
                $config = SwitchConfig::query()->find($params['switch_id']);
                //判断是否存在
                if (empty($config)) {
                    return $this->response(StatusCode::C_PARAM_ERROR);
                }
                // 创建监控服务类
                $service = new MonitorConfigChangeService($config->toArray(), "HitBug修改开关配置");
                //修改开关
                $config->update([
                    'os_type' => $params['os_type'],
                    'time_interval' => $params['time_interval'],
                    'cache_size' => $params['cache_size'],
                    'status' => $params['status'],
                    'is_allow_error_report' => $params['is_allow_error_report'],
                    'is_allow_crash_report' => $params['is_allow_crash_report'],
                    'is_allow_screenshot' => $params['is_allow_screenshot'],
                    'is_allow_oom_report' => $params['is_allow_oom_report'],
                    'is_allow_anr_report' => $params['is_allow_anr_report'],
                    'screenshot_num' => $params['screenshot_num'],
                    'screenshot_cd' => $params['screenshot_cd'],
                ]);
            }
            //刷新缓存
            SwitchConfig::flushCache($config->developer_app_id);
            // 监控
            $service->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['switch_id' => $config->id]);
        } catch (Exception $e) {
            \Log::error('编辑开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 修改状态
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3495
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->switchId()->status()
            ->validate();

        try {
            $config = SwitchConfig::query()->find($params['switch_id']);
            //判断是否存在
            if (empty($config)) {
                return $this->response(StatusCode::C_PARAM_ERROR);
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($config->toArray(), "HitBug修改开关状态");
            //修改状态
            $config->update(['status' => $params['status']]);
            //刷新缓存
            SwitchConfig::flushCache($config->developer_app_id);
            // 监控
            $service->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('修改状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3496
     * @return JsonResponse
     */
    public function del(): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()->switchId()->validate();

        try {
            //删除开关配置
            $config = SwitchConfig::query()->where('id', $params['switch_id'])->first();
            //检测状态
            if (empty($config) || $config->status == SwitchConfig::STATUS_ON) {
                return $this->response(StatusCode::C_PARAM_ERROR, [], '请先关闭开关');
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($config->toArray(), "HitBug删除开关配置");
            //删除开关
            $config->delete();
            // 监控
            $service->monitor([]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('删除开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
