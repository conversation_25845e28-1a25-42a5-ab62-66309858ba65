<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWarningRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('warning_record', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedBigInteger('warning_id')->default(0)->comment('预警id');
            $table->unsignedBigInteger('rule_id')->default(0)->comment('预警规则id');
            $table->string('app_name', '255')->default('')->comment('app名称');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台类型;1为安卓,2为iOS');
            $table->json('app_version')->comment('app版本');
            $table->dateTime('start_date')->comment('统计开始时间');
            $table->dateTime('end_date')->comment('统计结束时间');
            $table->timestamps();
            $table->index('warning_id');
        });
        \DB::connection('exception')->statement("ALTER TABLE `warning_record` comment '预警记录表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('warning_record');
    }
}
