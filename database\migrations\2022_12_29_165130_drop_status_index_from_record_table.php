<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropStatusIndexFromRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            $table->dropIndex(['developer_app_id', 'type', 'status', 'os_type']);
            $table->dropIndex(['developer_app_id', 'type', 'handler_id', 'os_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            $table->index(['developer_app_id', 'type', 'status', 'os_type']);
            $table->index(['developer_app_id', 'type', 'handler_id', 'os_type']);
        });
    }
}
