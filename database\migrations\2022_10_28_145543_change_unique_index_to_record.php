<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeUniqueIndexToRecord extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            $table->dropUnique(['developer_app_id', 'type', 'name']);
            $table->index(['developer_app_id', 'type', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('record', function (Blueprint $table) {
            $table->unique(['developer_app_id', 'type', 'name']);
            $table->dropIndex(['developer_app_id', 'type', 'name']);
        });
    }
}
