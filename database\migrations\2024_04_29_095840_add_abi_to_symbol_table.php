<?php

/**
 * 增加所属架构字段
 * @desc 增加所属架构字段
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/29
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAbiToSymbolTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->string('abi', 255)->default('')->comment('所属架构');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('symbol', function (Blueprint $table) {
            $table->dropColumn('abi');
        });
    }
}
