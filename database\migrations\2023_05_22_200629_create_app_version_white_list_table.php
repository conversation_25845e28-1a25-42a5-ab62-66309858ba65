<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppVersionWhiteListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('version_white_list', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->string('app_version', 50)->default('')->comment('app版本号');
            $table->timestamps();
            $table->index('app_version');
        });

        \DB::connection('exception')->statement("ALTER TABLE `version_white_list` comment 'app版本白名单'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('version_white_list');
    }
}
