<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->create('record', function (Blueprint $table) {
            $table->bigIncrements('record_id');
            $table->unsignedBigInteger('developer_app_id')->default(0)->comment('APP项目id');
            $table->unsignedTinyInteger('type')->default(0)->comment('异常类型 1为崩溃异常');
            $table->unsignedTinyInteger('category')->default(0)->comment('具体异常类型');
            $table->string('name', 128)->comment('异常说明');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台类型;1为安卓,2为iOS');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态');
            $table->unsignedInteger('handler_id')->default(0)->comment('处理人id');
            $table->string('handler', 64)->nullable()->comment('处理人');
            $table->timestamps();
            $table->unique(['developer_app_id', 'type', 'name']);
            $table->index(['developer_app_id', 'type', 'os_type']);
            $table->index(['developer_app_id', 'type', 'handler_id', 'os_type']);
            $table->index(['developer_app_id', 'type', 'status', 'os_type']);
        });
        \DB::connection('exception')->statement("ALTER TABLE `record` comment '异常记录表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->dropIfExists('record');
    }
}
