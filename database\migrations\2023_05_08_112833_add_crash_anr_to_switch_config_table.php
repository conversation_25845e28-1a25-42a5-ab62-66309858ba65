<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCrashAnrToSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('switch_config', function (Blueprint $table) {
            $table->unsignedTinyInteger('is_allow_crash_report')->default(0)->comment('是否允许崩溃上报，1:允许、0:不允许');
            $table->unsignedTinyInteger('is_allow_anr_report')->default(0)->comment('是否允许ANR上报，1:允许、0:不允许');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('switch_config', function (Blueprint $table) {
            $table->dropColumn('is_allow_crash_report');
            $table->dropColumn('is_allow_anr_report');
        });
    }
}
