<?php

/**
 * 崩溃控制器
 * @desc 崩溃控制器
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2024/01/05
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Components\Helper\CommonHelper;
use App\Components\Redis\RedisKeyEnum;
use App\Components\Scope\CrashScope;
use App\Http\Logic\CrashLogic;
use App\Http\Logic\ExceptionLogic;
use App\Jobs\ListExportJob;
use App\Model\BaseModel;
use App\Model\Record;
use App\Model\StarRocks\ExceptionRecordPerf;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Model\Symbol;
use App\Model\TapdBug;
use App\Service\AnalysisService;
use App\Service\CrashService;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;
use App\Service\Exception\Analysis\AnalysisException;
use App\Service\Exception\Analysis\AnalysisLogcat;
use App\Service\Exception\Analysis\DayTopList;
use App\Service\Exception\Analysis\ExceptionList;
use App\Service\Exception\Analysis\HourTopList;
use App\Service\Exception\Analysis\InfoTrend;
use App\Service\Exception\Analysis\SearchList;
use App\Service\Exception\BaseService;
use App\Service\IpServer;
use App\Service\StatisticService;
use Arr;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CrashController extends BaseController
{
    /**
     * @var CrashScope
     */
    protected $scope;
    protected $statisticService;
    protected $crashService;
    protected $query;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->scope = new CrashScope($request->toArray());
        $this->statisticService = new StatisticService($request, $this->scope);
        $this->crashService = new CrashService($this->scope);
        $this->query = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/06 19:49
     * memo : 異常列表
     */
    public function list(Request $request): JsonResponse
    {
        try {
            $order = [
                'crash_user_count' => 'device_num',
                'crash_count' => 'block_num',
                'last_report_time' => 'stream_max_stream_time',
                'match_num' => 'match_num',
            ];
            $orderField = $order[$this->scope->sortField] ?? 'stream_max_stream_time';
            $orderSequence = $this->scope->sortType ?? 'DESC';
            $pageIndex = $request->page;
            $pageLimit = $request->per_page;
            [$total, $list] = (new ExceptionList($request))->getList($orderField, $orderSequence, $pageIndex, $pageLimit);
            return $this->response(0, compact('total', 'list'));
        } catch (\Throwable $e) {
            return $this->response(1000, [], $e->getMessage());
        }
    }

    /**
     * 异常崩溃详细信息列表
     * @param Request $request
     * @return JsonResponse
     */
    public function infoList(Request $request)
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'exception_block_id' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            $this->scope->page = $this->scope->page ?? '1'; // 默认显示第1页
            $this->scope->perPage = $this->scope->perPage ?? '10'; // 默认每页10条记录
            $total = $this->statisticService->getCount(clone ($this->query))['count'] ?? 0;
            $list = $this->query
                ->tap(function ($query) {
                    $this->scope->getBuilder($query);
                })->select(['app_version', 'event_time', 'dev_create_time', 'device_model', 'os_version', 'exception_merge_id'])
                ->forPage($this->scope->page, $this->scope->perPage)
                ->orderBy('event_time', 'desc')
                ->getFromCK();
            return $this->response(0, compact('total', 'list'));
        } catch (\Exception $e) {
            Log::error('异常崩溃详细信息列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/18 10:22
     * memo : [異常趨勢]統計數據
     */
    public function summaryStatistic(Request $request): JsonResponse
    {

        $extraAppId = $request->developer_app_id;
        $eventName = ExceptionLogic::EVENT_NAME[$request->type];
        $streamTime = [strtotime($request->start_date), strtotime($request->end_date)];
        $rawStreamDate = [$request->start_date, $request->end_date];
        $streamWhere = [];
        if ($request->os_type ?? 0) {
            $streamWhere['os_type'] = ($request->os_type ?? 0) ? (string)($request->os_type) : '';
        }
        //all/android/ios/pc
        if ($request->app_version ?? '') {
            $streamWhere['app_version'] = json_decode($request->app_version, true);
        }
        if ($request->inner_version ?? '') {
            $streamWhere['inner_version'] = json_decode($request->inner_version, true);
        }
        //APP版本
        if ((in_array($request->is_emulator ?? -1, [0, 1]))) {
            $streamWhere['is_emulator'] = intval($request->is_emulator);
        }
        //是否模擬器//追加的
        if ($request->sdk_package_name ?? '') {
            $streamWhere['sdk_package_name'] = json_decode($request->sdk_package_name, true);
        }
        $streamWhere['crash_type'] = $request->crash_type ?? null; //异常类型
        //SDK包名
        $useDuration = $request->use_duration ?? 0; //使用時長
        $whiteListFilter = (isset($request->is_filter) && !empty($request->is_filter)) ? intval($request->is_filter) : 0; //是否剔除白名單
        //$result1 = $this->statisticService->getCrashSummaryStatistic();
        $result2 = (new ExceptionLogic())->summary(
            $extraAppId,
            $eventName,
            $streamTime,
            $streamWhere,
            $useDuration,
            $whiteListFilter,
            $rawStreamDate
        );
        //        CommonHelper::xdebug([
        //            '$result1' => $result1,
        //            '$result2' => $result2,
        //        ],'$resultReview');
        return $this->response(0, $result2);
    }

    /**
     * 获取崩溃异常记录详情
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2635
     * @return JsonResponse
     */
    public function recordInfo(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required',
                'exception_block_id' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }

            $record = Record::where([
                'developer_app_id' => $this->request['developer_app_id'],
                'exception_block_id' => $this->request['exception_block_id'],
            ])->first();
            if (empty($record)) {
                Log::info('未查询到崩溃异常记录信息');
                //从数仓中获取数据
                $exceptionData = ExceptionStreamAll::query()
                    ->select(['os_type', 'subject_name', 'explain_desc'])
                    ->where('extra_app_id', $this->request['developer_app_id'])
                    ->where('event_name', BaseService::EVENT_NAME[$this->request['type']])
                    ->where('exception_block_id', $this->request['exception_block_id'])
                    ->limit(1)
                    ->firstFromSR();
                // 入库
                $record = new Record();
                $record->developer_app_id = $this->request['developer_app_id'];
                $record->type = $this->request['type'];
                $record->category = 0;
                $record->name = $exceptionData['subject_name'];
                $record->explain = $exceptionData['explain_desc'];
                $record->os_type = $exceptionData['os_type'];
                $record->exception_block_id = $this->request['exception_block_id'];
                $record->save();
            } else {
                $record->type = $this->request['type'];
                $record->save();
            }
            // 获取当前异常记录绑定的缺陷bug_id
            $tapBug = TapdBug::getTapdBugByExceptionBlockId($this->request['developer_app_id'], $this->request['type'], $this->request['exception_block_id']);
            //判断当前异常是否有关联tapd
            if (empty($tapBug)) {
                $record->bug_id = '';
            } else {
                $record->bug_id = $tapBug->tapd_bug_id ?? '';
            }
            //获取异常崩溃汇总统计
            //[異常趨勢]統計數據[START]
            $extraAppId = $request->developer_app_id;
            $eventName = ExceptionLogic::EVENT_NAME[$request->type];
            $record = collect($record)->merge((new ExceptionLogic())->summaryOneBlock($extraAppId, $eventName, $request->exception_block_id));
            //[異常趨勢]統計數據[END]
            return $this->response(0, $record);
        } catch (\Exception $e) {
            Log::error('获取崩溃异常记录详情接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * 获取崩溃异常上报详情
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2611
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'exception_block_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        //        $total = $this->statisticService->getCount(clone($this->query))['count'] ?? 0;
        $total = 0;
        $builder = $this->query->tap(function ($query) {
            $this->scope->getBuilder($query, false);
        })
            ->when(isset($this->request['exception_merge_id']), function ($query) { // 精确定位到某一条详细信息
                $query->where('exception_merge_id', $this->request['exception_merge_id']);
            })
            ->orderByDesc('stream_time')
            ->forPage($this->scope->page, 1);
        try {
            //先获取exception_merge_id和stream_date信息
            $mergeInfo = (clone $builder)->select(['stream_date', 'exception_merge_id'])->firstFromCK();
        } catch (\Exception $e) {
            $mergeInfo = [];
        }

        if ($mergeInfo) {
            try {
                //获取详细信息
                $info = (clone $builder)->select([
                    'stream_date',
                    'event_time',
                    'stream_time',
                    'server_dev_str',
                    'basic_info_json AS detail_info',
                    'memory_info_json AS memory_info',
                    'dev_create_time',
                    'origin_stacks_json AS origin_stacks',
                    'console_info_json AS console_info',
                    'content',
                    'page_info_json AS page_info',
                    'extra AS expand',
                    'app_version',
                    'os_type',
                    'game_info_json AS uuid_info',
                    'account_id',
                    'role_name',
                    'exception_merge_id',
                    'ip',
                    'exception_image',
                    'exception_file',
                    'exception_file2',
                    'event_name',
                    DB::raw('upper(exception_block_id) as exception_block_id'),
                ])
                    ->where('stream_date', $mergeInfo['stream_date'])
                    ->where('exception_merge_id', $mergeInfo['exception_merge_id'])
                    ->firstFromCK();
            } catch (\Exception $e) {
                $info = [];
            }
            if (!empty($info)) {
                // 默认值
                $info['record_id'] = 0;
                $info['perf_id'] = 0;
                // 判断设备号是否“协议未被同意”,不是才走逻辑
                if ($info['server_dev_str'] != '协议未被同意') {
                    try {
                        // 获取录屏和性能的ID
                        $relationInfo = ExceptionRecordPerf::query()
                            ->where('stream_date', $mergeInfo['stream_date'])
                            ->where('exception_merge_id', $mergeInfo['exception_merge_id'])
                            ->limit(1)
                            ->firstFromSR();
                    } catch (\Exception $e) {
                        $relationInfo = [];
                    }

                    // 判断是否有数据
                    if ($relationInfo) {
                        $info['record_id'] = $relationInfo['record_id'];
                        $info['perf_id'] = $relationInfo['perf_id'];
                    }
                }
            }
        } else {
            $info = [];
        }
        $exceptionFile = '';
        //補齊detail_info，兼容舊版數據結構[START]
        if ($info) {
            $info['detail_info']['os_type'] = intval($info['os_type'] ?? 1);
            $info['detail_info']['app_version'] = $info['app_version'] ?? '';
            $info['detail_info']['client_ts'] = $info['dev_create_time'] ?? 0;
            $info['detail_info']['server_dev_str'] = $info['server_dev_str'] ?? '';
            $info['detail_info']['ts'] = $info['stream_time'] ?? 0;
            // 如果 exception_file2 有值，则替换 exception_file
            $exceptionFile = $info['exception_file'] ?? '';
            if (!empty($info['exception_file2'])) {
                $info['exception_file'] = $info['exception_file2'];
                // 删除 exception_file2
                unset($info['exception_file2']);
            }
            // // 判断图片是否存在
            // if (!empty($info['exception_image'])) {
            //     $url = 'https://' . (config('app.env') != 'production' ? 'test-' : '') . 'hitbug-1256453865.cos.ap-shanghai.myqcloud.com/' . $info['exception_image'];
            //     try {
            //         $opts = [
            //             'http' => [
            //                 'method' => 'GET',
            //                 'header' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            //             ],
            //         ];
            //         $context = stream_context_create($opts);
            //         file_get_contents($url, false, $context);
            //     } catch (\Exception $e) {
            //         $info['exception_image'] = '';
            //     }
            // }
            // 判断IP是否ipv4
            if (filter_var($info['ip'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                // 获取IP地址
                $address = IpServer::getInstance()->getResultAll($info['ip']);
                if ($address) {
                    $info['detail_info']['system_region'] = "{$address['country']} - {$address['province']} - {$address['city']}";
                }
            }
        }
        //補齊detail_info，兼容舊版數據結構[END]
        // 将用户名称封装到detail_info中
        if (!empty($info) && is_array($info['expand'] ?? null)) {
            foreach ($info['expand'] as $value) {
                if (is_array($value) && isset($value['key'])) {
                    if ($value['key'] == 'roleName') {
                        $info['detail_info']['role_name'] = $value['value'] ?? '';
                    }
                }
            }
        }
        if (!empty($info)) {
            // 判断是否IOS
            if ((int)$info['os_type'] === BaseModel::IOS) {
                // 先对content进行json_decode
                $content = json_decode($info['content'], true);
                // 判断是否解析成json格式
                if (is_array($content)) {
                    $resUuids = $this->analyzeMainCrashStackRe($content['content'] ?? '');
                    // 处理数据
                    $uuidInfos = [];
                    foreach ($resUuids as $key => $val) {
                        $uuidInfos[$val] = [
                            'name' => $key,
                            'uuid' => $val,
                        ];
                    }
                    // 判断是否有数据
                    if ($uuidInfos) {
                        $info['uuid_info'] = array_values($uuidInfos);
                    }
                }
            }
            //获取最新的符号表解析结果和uuid_info
            // $symbolAnalysis = Analysis::query()
            //     ->where('extra_app_id', $this->request['developer_app_id'])
            //     ->where('server_dev_str', $info['server_dev_str'])
            //     ->where('event_time', $info['event_time'])
            //     ->where('dev_create_time', $info['dev_create_time'])
            //     ->where('exception_block_id', $this->request['exception_block_id'])
            //     ->select(['origin_stacks_json', 'game_info_json'])
            //     ->orderByDesc('stream_time')->firstFromCK();
            //获取符号表记录
            $symbol = Symbol::symbolList(
                $this->request['developer_app_id'],
                $info['app_version'],
                $info['os_type'],
                array_merge(Arr::pluck($info['uuid_info'], 'uuid'), Arr::pluck($info['uuid_info'], 'debug_id'))
            )->keyBy('uuid');
            // $uuids = $symbol->keys()->toJson();
            //组装最新原始堆栈、是否可字符表解析、符号表信息
            // 判断是否完整json格式，如果不是完整json格式，则截取到最后一个 '}]' 位置，并加上 ']'
            // if (isset($symbolAnalysis['origin_stacks_json']) && ($this->findLastClosingBracketsPosition($symbolAnalysis['origin_stacks_json'], '}]') !== (strlen($symbolAnalysis['origin_stacks_json']) - 2))) {
            //     $symbolAnalysis['origin_stacks_json'] = substr($symbolAnalysis['origin_stacks_json'], 0, $this->findLastClosingBracketsPosition($symbolAnalysis['origin_stacks_json'], '},{') + 1) . "]";
            // }
            // $info['last_origin_stacks'] = json_decode($symbolAnalysis['origin_stacks_json'] ?? null, true);
            $info['last_origin_stacks'] = null;
            // $info['is_can_analysis'] = !isset($symbolAnalysis['game_info_json']) || empty($uuids) || $symbolAnalysis['game_info_json'] != $uuids;
            $info['is_can_analysis'] = false;
            foreach ($info['uuid_info'] as $key => $value) {
                $uuidKey = $value['uuid'] ?? $value['debug_id'];
                if (!isset($info['uuid_info'][$key]['uuid'])) {
                    $info['uuid_info'][$key]['uuid'] = $info['uuid_info'][$key]['debug_id'] ?? '';
                }
                if (!isset($info['uuid_info'][$key]['name'])) {
                    $info['uuid_info'][$key]['name'] = $info['uuid_info'][$key]['filename'] ?? '';
                }
                $info['uuid_info'][$key]['symbol_id'] = $symbol[$uuidKey]['id'] ?? null;
                $info['uuid_info'][$key]['file_url'] = $symbol[$uuidKey]['file_url'] ?? null;
                $info['uuid_info'][$key]['file_size'] = $symbol[$uuidKey]['file_size'] ?? 0;
                $info['uuid_info'][$key]['created_at'] = isset($symbol[$uuidKey]['created_at']) ? $symbol[$uuidKey]['created_at']->format('Y-m-d H:i:s') : null;
                // 判断是否能解析符号表
                if ($info['uuid_info'][$key]['symbol_id']) {
                    $info['is_can_analysis'] = true;
                }
            }
        }
        // 判断是否完整json格式，如果不是完整json格式，则截取到最后一个 '}]' 位置，并加上 ']'
        // if (isset($info['origin_stacks']) && ($this->findLastClosingBracketsPosition($info['origin_stacks'], '}]') !== (strlen($info['origin_stacks']) - 2))) {
        //     $info['origin_stacks'] = substr($info['origin_stacks'], 0, $this->findLastClosingBracketsPosition($info['origin_stacks'], '},{') + 1) . "]";
        // }
        if (!empty($info['origin_stacks'])) {
            $info['origin_stacks'] = json_decode($info['origin_stacks'], true);
        } else {
            $info['origin_stacks'] = [];
        }
        // $info['origin_stacks'] = json_decode($info['origin_stacks'] ?? null, true);
        // 判断是否要转换堆栈
        if (!empty($info['os_type']) && $info['os_type'] == 3 && isset($info['origin_stacks'][0]['frame'])) {
            $info['origin_stacks'] = (new AnalysisService($this->request))->convertStacks($exceptionFile);
        }
        if (!empty($info['os_type']) && $info['os_type'] == 3 && !empty($info['console_info'])) {
            $info['console_info'] = $this->handleConsole($info['console_info']);
        }
        // 获取崩溃异常趋势数据
        $info = array_merge($info, (new InfoTrend())->getTrend($this->request['developer_app_id'], $info['event_name'] ?? '', $info['exception_block_id'] ?? 0));
        return $this->response(0, compact('total', 'info'));
    }

    /**
     * 处理跟踪日志
     *
     * @param $data
     * @return array
     */
    private function handleConsole($data)
    {
        //判断是否是数组
        if (!is_array($data)) {
            return [];
        }
        // 判断第一个元素是否是数组
        if (is_array($data[0])) {
            return $data;
        }
        $newData = [];
        $level = ['D' => 'Debug', 'I' => 'Info', 'W' => 'Warning', 'E' => 'Error', 'C' => 'Fatal'];
        foreach ($data as $key => $item) {
            // 使用正则表达式提取所需的部分
            preg_match('/\[(\d{2}:\d{2}:\d{2}\.\d{3})\] \[(\w)\] \[(\d+)\] (.*)/', $item, $matches);
            //判断数据是否正确
            if (count($matches) === 5) {
                $time = $matches[1];        // 获取时间
                $logLevel = $matches[2];    // 获取日志级别
                $processId = $matches[3];   // 获取进程ID
                $content = $matches[4];      // 获取后续内容
                $newData[] = [
                    'details' => $content,
                    'level' => $level[$logLevel] ?? '',
                    'shops' => '',
                    'thread_info' => $processId,
                    'time' => $time,
                ];
            }
        }
        return $newData;
    }

    /**
     * 解析堆栈信息，获取uuid
     *
     * @param string $reports 堆栈信息
     * @return array 包含uuid的数组
     */
    public function analyzeMainCrashStackRe($reports)
    {
        // 正则表达式匹配 "Crashed:" 到 "Thread" 之间的内容
        $pattern = "/Crashed:(.*?)Thread/s";
        $blackList = ['QuartzCore', 'UIKitCore', 'CoreFoundation', 'Foundation', 'GraphicsServices', 'CFNetwork'];
        // 使用 preg_match() 查找匹配项
        if (preg_match($pattern, $reports, $matches)) {
            $moduleDict = array();
            $content = $matches[1];
            foreach (explode("\n", $content) as $line) {
                $module = explode("  ", $line);
                if (count($module) > 1) {
                    $module = $module[1];
                    if (strpos($module, '.dylib') !== false) {
                        continue;
                    }
                    $isBlack = false;
                    // 判断$module是否存在黑名单中
                    foreach ($blackList as $black) {
                        if (strpos($module, $black) !== false) {
                            $isBlack = true;
                            break;
                        }
                    }
                    // 如果是黑名单则跳过
                    if ($isBlack) {
                        continue;
                    }
                    $moduleName = trim($module);
                    if (array_key_exists($moduleName, $moduleDict)) {
                        continue;
                    }
                    // 固定串为：UnityFramework、<、>
                    // .*? 匹配任意字符（. 表示任意字符，*? 表示非贪婪匹配，尽可能少匹配）
                    // ([a-z0-9]+) 是一个捕获组，匹配一个或多个小写字母或数字。
                    // \s* 匹配零个或多个空白字符。
                    // \s+ 匹配一个或多个空白字符
                    $pattern = "/$moduleName.*?<([a-z0-9]+)>/";
                    // 使用 preg_match_all() 查找匹配项
                    if (preg_match_all($pattern, $reports, $subMatches)) {
                        $moduleDict[$moduleName] = end($subMatches[1]);
                    }
                }
            }
            return $moduleDict;
        } else {
            return array();
        }
    }

    /**
     * 获取字符串最后一次出现的位置
     *
     * @param $jsonStr
     * @param $str
     * @return int
     */
    private function findLastClosingBracketsPosition($jsonStr, $str): int
    {
        // 查找 '}]' 最后一次出现的位置
        $position = strrpos($jsonStr, $str);

        if ($position !== false) {
            // 返回位置
            return $position;
        }
        // 如果没有找到 返回 -1 表示未找到
        return -1;
    }

    /**
     * 编辑异常记录状态、处理人
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2613
     * @return JsonResponse
     */
    public function store(): JsonResponse
    {
        try {
            $validator = \Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'record_ids' => 'required|array',
                'status' => 'required|integer',
                'handler_id' => 'integer',
                'handler' => 'string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            $records = Record::whereDeveloperAppId($this->request['developer_app_id'])->whereIn('record_id', $this->request['record_ids'])->get();
            //处理人暂定为当前操作人
            $this->request['handler_id'] = \Auth::id();
            $this->request['handler'] = \Auth::user()->alias;
            foreach ($records as $record) {
                $record->store($this->request, true);
            }
            return $this->response();
        } catch (\Exception $e) {
            \Log::error('编辑异常记录接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2576
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        //參數校驗[START]
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            //'search_type' => 'required|json',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $extraAppId = 0;
        $eventName = '';
        $streamTime = [];
        $statFilter = [];
        $streamFilter = [];
        $labelName = '';
        $handlerId = []; //處理人
        $handlerStatus = []; //處理狀態
        $isFilter = 0; //是否剔除白名單
        $ExceptionLogic = new ExceptionLogic();
        $ExceptionLogic->checkParameter(
            $request,
            $extraAppId,
            $eventName,
            $streamTime,
            $statFilter,
            $streamFilter,
            $labelName,
            $handlerId,
            $handlerStatus,
            $isFilter
        );
        //參數校驗[END]
        $result = $ExceptionLogic->pullExceptionSearch(
            $extraAppId,
            $eventName,
            $streamTime,
            $statFilter,
            $streamFilter,
            $labelName,
            $handlerId,
            $handlerStatus,
            $isFilter
        );
        return $this->response(0, $result);
    }

    //    /**
    //     * 筛选列表
    //     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2576
    //     * @param Request $request
    //     * @return JsonResponse
    //     */
    //    public function searchOfficial(Request $request): JsonResponse
    //    {
    //        @ini_set('memory_limit', '2G');
    //        @ini_set('max_execution_time', '0');
    //        $input = $request->all();
    //        $validator = \Validator::make($input, [
    //            'developer_app_id' => 'required|int',
    //            'search_type' => 'required|json',
    //        ]);
    //        try {
    //            if ($validator->fails()) {
    //                return $this->response(1000);
    //            }
    //            $types = json_decode($input['search_type'], true);
    //            $list = [];
    //            if (in_array('issue_status', $types, true)) {
    //                $types = array_diff($types, ['issue_status']);
    //                $list['issue_status'] = $this->crashService->getStatusValue();
    //            }
    //            if (in_array('crash_type', $types, true) && $this->scope->type == Record::TYPE_ERROR) {
    //                $types = array_diff($types, ['crash_type']);
    //            }
    //            if (in_array('handler', $types, true)) {
    //                $types = array_diff($types, ['handler']);
    //                $list['handler'] = $this->crashService->getHandlerValue();
    //            }
    //            $types = array_values($types);
    //            //過濾標籤[START]
    //            if(in_array('label_name', $types, true) && ($input['start_date'] ?? '') && ($input['end_date'] ?? '')){
    //                $eventName = [
    //                    1 => 'exception_crash',
    //                    2 => 'exception_error'
    //                ];
    //                $list['label_name'] = (new CrashLogic())->ableSearchLabelList(
    //                    $eventName[$input['type']],
    //                    $input['developer_app_id'],
    //                    $input['time_type'] ?? CrashScope::TIME_TYPE_CREATE,
    //                    $input['start_date'],
    //                    $input['end_date']
    //                );
    //            }
    //            //過濾標籤[END]
    //            if($labelNameIndex = array_search('label_name',$types)) unset($types[$labelNameIndex]);//過濾標籤A
    //            $list = array_merge($list, $this->crashService->getSearchValue($types));
    //            return $this->response(0, $list);
    //        } catch (Exception $e) {
    //            CommonHelper::xdebug($e);
    //            \Log::error('筛选列表' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
    //            return $this->response(1005);
    //        }
    //    }

    /**
     * 崩溃趋势--曲线图（总览、详情）
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2578
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatisticChart(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        $data = $this->statisticService->getSummaryDataByGroup();
        return $this->response(0, $data);
    }

    //    /**
    //     * 崩溃分布（总览、详情）
    //     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2579
    //     * @param Request $request
    //     * @return JsonResponse
    //     */
    //    public function getDistributionOfficial(Request $request): JsonResponse
    //    {
    //        $input = $request->all();
    //        $validator = \Validator::make($input, [
    //            'developer_app_id' => 'required|int',
    //        ]);
    //        if ($validator->fails()) {
    //            return $this->response(1000);
    //        }
    //        $list['app_version'] = $this->statisticService->getCrashDistribution('app_version');
    //        $list['device_model'] = $this->statisticService->getCrashDistribution('device_model');
    //        $list['os_version'] = $this->statisticService->getCrashDistribution('os_version');
    //        $list['channel'] = $this->statisticService->getCrashDistribution('release_store');
    //        $list['manufacturer'] = $this->statisticService->getCrashDistribution('manufacturer');
    //        return $this->response(0, $list);
    //    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/18 15:08
     * memo : 異常分佈（http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2579）
     */
    public function getDistribution(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $extraAppId = intval($request->developer_app_id);
        $requestParams = [
            'type' => $request->type, // 1崩溃，2错误
            'streamTime' => [strtotime($request->start_date), strtotime($request->end_date)],
            'number_mode' => intval($request->number_mode), // 统计维度
            'os_type' => strval($request->os_type) ?? '', // 平台类型
            'use_duration' => $request->use_duration ?? 0, // 崩溃时长/使用时长
            'white_list_filter' => $request->is_filter ?? 0, // 是否白名单剔除
            'is_emulator' => $request->is_emulator ?? null, // 是否白名单剔除
            'exception_block_id' => $request->exception_block_id ?? null, //异常ID
            'crash_type' => $request->crash_type ?? null, //异常类型
            'inner_version' => json_decode($request->inner_version ?? '', true), //游戏资源版本
        ];
        if ($request->app_version ?? '') {
            $requestParams['app_version'] = json_decode($request->app_version, true);
        }
        //APP版本
        if ($request->sdk_package_name ?? '') {
            $requestParams['sdk_package_name'] = json_decode($request->sdk_package_name, true);
        }
        //包名
        $ExceptionLogic = new ExceptionLogic();
        $list = $ExceptionLogic->pullExceptionDistribution($extraAppId, [
            'app_version',
            'device_model',
            'os_version',
            'release_store',
            'manufacturer',
            'inner_version',
        ], $requestParams);
        // 获取启动次数或者设备数
        $requestParams['type'] = -1;
        $requestParams['app_version_request'] = array_column($list['app_version'], 'name');
        $requestParams['device_model_request'] = array_column($list['device_model'], 'name');
        $requestParams['os_version_request'] = array_column($list['os_version'], 'name');
        $requestParams['release_store_request'] = array_column($list['release_store'], 'name');
        $requestParams['manufacturer_request'] = array_column($list['manufacturer'], 'name');
        $requestParams['inner_version_request'] = array_column($list['inner_version'], 'name');
        $startList = $ExceptionLogic->pullExceptionDistribution($extraAppId, [
            'app_version',
            'device_model',
            'os_version',
            'release_store',
            'manufacturer',
            'inner_version',
        ], $requestParams);
        // 计算异常次数占启动次数或者设备数的百分比
        foreach ($list as $key => $value) {
            $startCount = array_column($startList[$key], null, 'name');
            foreach ($value as $k => $v) {
                $startNum = $startCount[$v['name']]['num'] ?? 0;
                $list[$key][$k]['exception_percent'] = $startNum == 0 ? 0 : (float)bcadd(round(($v['num'] / $startNum) * 100, 4), 0, 2);
            }
        }
        $list['channel'] = $list['release_store'] ?? [];
        return $this->response(0, $list);
    }

    /**
     * 获取单个字段的崩溃数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getDistributionAll(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            'field' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $extraAppId = intval($request->developer_app_id);
        $requestParams = [
            'type' => $request->type, // 1崩溃，2错误
            'streamTime' => [strtotime($request->start_date), strtotime($request->end_date)],
            'number_mode' => 0, // 统计维度
            'os_type' => strval($request->os_type) ?? '', // 平台类型
            'use_duration' => $request->use_duration ?? 0, // 崩溃时长/使用时长
            'white_list_filter' => $request->is_filter ?? 0, // 是否白名单剔除
            'is_emulator' => $request->is_emulator ?? null, // 是否白名单剔除
            'exception_block_id' => $request->exception_block_id ?? null, //异常ID
            'crash_type' => $request->crash_type ?? null, //异常类型
            'limit' => 10000,
        ];
        if ($request->app_version ?? '') {
            $requestParams['app_version'] = json_decode($request->app_version, true);
        }
        //APP版本
        if ($request->sdk_package_name ?? '') {
            $requestParams['sdk_package_name'] = json_decode($request->sdk_package_name, true);
        }
        //包名
        $ExceptionLogic = new ExceptionLogic();
        $list = $ExceptionLogic->pullExceptionDistribution($extraAppId, [
            $request->field
        ], $requestParams);
        // 获取启动次数或者设备数
        $requestParams['type'] = -1;
        $requestParams[$request->field . '_request'] = array_column($list[$request->field], 'name');
        $startList = $ExceptionLogic->pullExceptionDistribution($extraAppId, [
            $request->field
        ], $requestParams);
        // 计算异常次数占启动次数或者设备数的百分比
        $startAllDevice = array_sum(array_column($startList[$request->field], 'num'));
        $startCount = array_column($startList[$request->field], null, 'name');
        foreach ($list as $key => $value) {
            foreach ($value as $k => $v) {
                $startNum = $startCount[$v['name']]['num'] ?? 0;
                $list[$key][$k]['exception_percent'] = $startNum == 0 ? 0 : bcadd(round(($v['num'] / $startNum) * 100, 4), 0, 2);
                $list[$key][$k]['network_percent'] = $startAllDevice == 0 ? 0 : bcadd(round(($v['num'] / $startAllDevice) * 100, 4), 0, 2);
                $list[$key][$k]['network_num'] = $startNum;
            }
        }
        return $this->response(0, [
            'all_dev' => $startAllDevice,
            'list' => $list[$request->field] ?? [],
        ]);
    }

    //    /**
    //     * http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3886
    //     * 异常崩溃版本信息列表
    //     * @param Request $request
    //     * @return JsonResponse
    //     */
    //    public function appVersionListOfficial(Request $request): JsonResponse
    //    {
    //        $sortField = $this->scope->sortField ?? 'app_version_count'; // 从请求中获取排序字段，默认为 app_version_count 字段排序
    //        $sortType = $this->scope->sortType ?? 'desc'; // 默认为降序
    //        $builder = $this->query->tap(function ($query) {
    //            $this->scope->getBuilder($query);
    //        });
    //        $total = \ClickHouse::getSqlData('select count(1) as `count` from (' .
    //                \ClickHouse::getSqlBindings((clone $builder)->groupBy(['app_version'])->selectRaw('app_version')) .
    //                ') limit 1')[0]['count'] ?? 0;
    //        $list = (clone $builder)->where('extra_app_id', $this->scope->developerAppId)
    //            ->groupBy(['app_version'])
    //            ->orderBy($sortField, $sortType)
    //            ->selectRaw('app_version AS app_version_name, COUNT(1) AS app_version_count, COUNT(distinct server_dev_str) AS app_version_user_count,
    //                min(`event_time`) AS first_report_time,
    //                max(`event_time`) AS last_report_time')
    //            ->forPage($this->scope->page, $this->scope->perPage)
    //            ->getFromCK();
    //        return $this->response(0, compact('total', 'list'));
    //    }

    /**
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/18 15:51
     * memo : 異常詳情--版本信息列表（ http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3886）
     */
    public function appVersionList(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $extraAppId = $request->developer_app_id;
        $eventName = ExceptionLogic::EVENT_NAME[$request->type ?? 0] ?? '';
        $exceptionBlockId = $request->exception_block_id ?? '';
        $streamTime = (($request->start_date ?? '') && ($request->end_date ?? '')) ? [strtotime($request->start_date), strtotime($request->end_date)] : [];
        $pageIndex = $request->page ?? 1;
        $pageLimit = $request->per_page ?? 10;
        $sortField = $request->sort_field ?? 'app_version';
        $sortType = $request->sort_type ?? 'desc';
        [$total, $list] = (new ExceptionLogic())->pullAppVersionList($extraAppId, $eventName, $exceptionBlockId, $streamTime, $pageIndex, $pageLimit, $sortField, $sortType);
        return $this->response(0, compact('total', 'list'));
    }

    /**
     * 标签管理
     * @param Request $request
     * @return JsonResponse
     */
    public function labelHandler(Request $request): JsonResponse
    {
        try {
            $input = $request->all();
            $validator = \Validator::make($input, [
                'developer_app_id' => 'required',
                'exception_block_id' => 'string',
                //add[START]
                'subject_name' => 'string', //list.name
                'explain' => 'string',
                'category' => 'int', //list.category//type已被構造方法佔用
                'os_type' => 'int',
                'event_name' => 'string',
                //edit[START]
                'label_id' => 'string',
                'label_name' => 'string',
                //delete[START]
                'deleted_at' => 'string',
            ]);
            //check parameter[START]
            if (!($input['label_id'] ?? '') && !($input['deleted_at'] ?? '')) {
                //新增
                $need = [
                    //-----
                    //                    'subject_name',
                    //                    'explain',
                    //                    'category',//type
                    //                    'os_type',
                    //                    'event_name',
                    //-----
                    'exception_block_id',
                    'label_name',
                ];
                foreach ($need as $field) {
                    if (!isset($input[$field])) {
                        return $this->response(1000, [], "a parameter is missing ( {$field} )");
                    }
                }
            } elseif (!($input['label_id'] ?? '') && ($input['deleted_at'] ?? '')) {
                //删除
                return $this->response(1000, [], 'a parameter is missing ( label_id )');
            } elseif (!($input['deleted_at'] ?? '')) {
                //編輯
                if (!isset($input['label_name'])) {
                    return $this->response(1000, [], 'a parameter is missing ( label_name )');
                }
            }
            if ($validator->fails()) {
                return $this->response(1000);
            }

            //check parameter[END]
            $return = (new CrashLogic())->labelHandler(
                $input['developer_app_id'],
                $input['label_id'] ?? '',
                $input['label_name'] ?? '',
                $input['deleted_at'] ?? '',
                $input['subject_name'] ?? '',
                $input['explain'] ?? '',
                intval($input['category'] ?? 0),
                $input['os_type'] ?? '',
                $input['event_name'] ?? '',
                $input['exception_block_id'] ?? ''
            );
            return $this->response(0, $return);
        } catch (\Throwable $e) {
            \Log::error(__FUNCTION__ . 'Throwable:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005, [], $e->getMessage());
        }
    }

    /**
     * 標籤列表
     * @param Request $request
     * @return JsonResponse
     */
    public function labelList(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            'exception_block_id' => 'string',
            'keyword' => 'string',
            'page' => 'int',
            'page_size' => 'int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $CrashLogic = new CrashLogic();
        $list = $CrashLogic->labelList(
            intval($input['developer_app_id']),
            $input['exception_block_id'] ?? '',
            $input['keyword'] ?? '',
            $input['page'] ?? 1,
            $input['page_size'] ?? 15
        );
        return $this->response(0, $list);
    }

    /**
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5373
     * @param Request $request
     * @return JsonResponse
     */
    public function storeRecord(Request $request)
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            'exception_block_id' => 'required|string',
            'event_name' => 'required|string',
            'category' => 'required|int',
            'name' => 'required|string',
            'os_type' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        $record_id = Record::createRecord($input);
        return $this->response(0, compact('record_id'));
    }

    /**
     * 获取异常记录的主堆栈信息
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5728
     * @param Request $request
     * @return JsonResponse
     */
    public function getExceptionOriginStacks(Request $request)
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            'exception_block_id' => 'required|string',
            'type' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        $result = (new ExceptionLogic())->getExceptionOriginStacks($input);
        return $this->response(0, $result);
    }

    /**
     * 导出数据
     *
     * @throws ValidationException
     */
    public function exportData()
    {
        $validator = \Validator::make($this->request, [
            'developer_app_id' => 'required|integer',
            'exception_block_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        $total = $this->statisticService->getCount(clone ($this->query))['count'] ?? 0;
        try {
            //查询数据
            $taskId = Str::uuid();
            // 创建导出队列
            dispatch(
                new ListExportJob($taskId, $total, $this->scope)
            )->onQueue('exception_excel_export');
            //返回数据
            return $this->response(0, [
                'task_id' => $taskId,
            ]);
        } catch (Exception $e) {
            Log::error('HitBug报告导出数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 导出状态
     *
     * @throws ValidationException
     */
    public function exportStatus()
    {
        $validator = \Validator::make($this->request, [
            'task_id' => 'required|string',
            'developer_app_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        try {
            // 查询数据
            $result = Redis::get(RedisKeyEnum::getExceptionExportDataKey($this->request['task_id']));
            // 进度值
            $progress = 0;
            // 判断数据是否为空
            if (!empty($result)) {
                $result = json_decode($result, true);
                // 判断code是否为1，1代表发生错误
                if ($result['code'] == 1) {
                    return $this->fail(1000, $result['msg']);
                }
                // 获取进度
                $progress = $result['progress'];
            }
            // 返回数据
            return $this->response(0, [
                'progress' => $progress,
                'download_url' => config('app.url') . "/crash/export/download?developer_app_id={$this->request['developer_app_id']}&task_id={$this->request['task_id']}",
            ]);
        } catch (Exception $e) {
            Log::error('HitBug报告导出状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 下载接口
     *
     * @throws ValidationException
     */
    public function exportDownload()
    {
        //校验参数
        $validator = \Validator::make($this->request, [
            'task_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        try {
            // 查询数据
            $result = Redis::get(RedisKeyEnum::getExceptionExportDataKey($this->request['task_id']));
            // 判断数据是否为空
            if (!empty($result)) {
                $result = json_decode($result, true);
                // 判断code是否为1，1代表发生错误
                if ($result['code'] == 1) {
                    return $this->fail(1000, $result['msg']);
                }
                // 判断文件路径是否为空
                if (!empty($result['path'])) {
                    // 获取文件路径，返回浏览器下载
                    return Storage::download(str_replace('app/', '', $result['path']));
                }
            }
            // 返回数据
            return $this->response(1000, [], '导出文件失败');
        } catch (Exception $e) {
            Log::error('HitBug报告下载接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 搜索列表
     *
     * @param Request $request
     * @return JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/06 19:49
     */
    public function searchList(Request $request): JsonResponse
    {
        try {
            $order = [
                'crash_user_count' => 'device_num',
                'crash_count' => 'block_num',
                'last_report_time' => 'stream_max_stream_time',
                'match_num' => 'match_num',
            ];
            $orderField = $order[$this->scope->sortField] ?? 'stream_max_stream_time';
            $orderSequence = $this->scope->sortType ?? 'DESC';
            $pageIndex = $request->page ?? 1;
            $pageLimit = $request->per_page ?? 5;
            [$total, $list] = (new SearchList($request))->getList($orderField, $orderSequence, $pageIndex, $pageLimit);
            return $this->response(0, compact('total', 'list'));
        } catch (\Throwable $e) {
            return $this->response(1000, [], CommonHelper::prettyJsonEncode([
                'file' => $e->getFile() . "(line:{$e->getLine()})",
                'message' => $e->getMessage(),
            ]));
        }
    }

    /**
     * 分析异常
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14376
     * @return JsonResponse
     */
    public function analysisException(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required',
                'exception_block_id' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            // 分析异常数据
            return $this->response(0, (new AnalysisException($request))->getData());
        } catch (\Exception $e) {
            Log::error('获取崩溃异常记录分析异常接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 分析Logcat
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14376
     * @return JsonResponse
     */
    public function analysisLogcat(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required',
                'exception_block_id' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            // 分析异常数据
            return $this->response(0, (new AnalysisLogcat($request))->getData());
        } catch (\Exception $e) {
            Log::error('获取崩溃异常记录分析异常接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 每小时top榜
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14376
     * @return JsonResponse
     */
    public function hourTop(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required',
                'limit' => 'required|integer',
                'filter_type' => 'required|integer|in:1,2',
                'app_version' => 'string',
                'start_date' => 'required|string',
                'end_date' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            // 每小时top榜
            return $this->response(0, (new HourTopList($request))->getList());
        } catch (\Exception $e) {
            Log::error('获取每小时top榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 每天top榜
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14376
     * @return JsonResponse
     */
    public function dayTop(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($this->request, [
                'developer_app_id' => 'required|integer',
                'type' => 'required',
                'limit' => 'required|integer',
                'filter_type' => 'required|integer|in:1,2',
                'app_version' => 'string',
                'start_date' => 'required|string',
                'end_date' => 'required|string',
            ]);
            if ($validator->fails()) {
                return $this->response(1000);
            }
            // 每小时top榜
            return $this->response(0, (new DayTopList($request))->getList());
        } catch (\Exception $e) {
            Log::error('获取每小时top榜接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
