<?php

namespace App\Jobs;

use App\Components\Helper\VoiceHelper;
use App\Components\OauthCenter\OauthCenterApi;
use App\Model\Warning;
use App\Model\WarningRecord;
use App\Model\WarningRule;
use App\Service\WarningIndexHandler\ExceptionIndexHandler;
use App\Service\WarningIndexHandler\LastHourExceptionIndexHandler;
use App\Service\WarningIndexHandler\LastHourStartIndexHandler;
use App\Service\WarningIndexHandler\SevenDayExceptionIndexHandler;
use App\Service\WarningIndexHandler\SevenDayStartIndexHandler;
use App\Service\WarningIndexHandler\StartIndexHandler;
use App\Service\WarningIndexHandler\YesterdayExceptionIndexHandler;
use App\Service\WarningIndexHandler\YesterdayStartIndexHandler;
use App\Service\WXGroupNoticeService;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProblemWarningJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 设置超时时间
     *
     * @var int
     */
    public $timeout = 3600;

    /**
     * Execute the job.
     *
     * @return void
     * @throws GuzzleException
     */
    public function handle()
    {
        // 设置内存限制， 2048MB
        ini_set('memory_limit', '2048M');
        try {
            //开始时间和结束时间
            $now = Carbon::now();
            $startTime = (clone $now)->subHours()->startOfHour()->timestamp;
            $endTime = (clone $now)->startOfHour()->timestamp;
            //获取APP
            $apps = DB::connection('developer')
                ->table("apps")
                ->pluck('app_name', 'id');
            //获取所有问题的预警
            $list = Warning::query()
                ->with('warningRule')
                ->where('status', Warning::START)
                ->where('monitor_type', Warning::ERROR_MONITOR)
                ->orderByDesc('created_at')
                ->get();
            //遍历预警
            foreach ($list as $warning) {
                //判断没有规则
                if (empty($warning->warningRule)) {
                    continue;
                }

                //判断生效时间
                if (!empty($warning->effective_time)) {
                    $hour = date('H:i:s');
                    //判断生效时间
                    if ($warning->effective_time['start_time'] > $hour || $warning->effective_time['end_time'] < $hour) {
                        continue;
                    }
                }

                //判断是否有异常类型
                if (empty($warning->exception_type)) {
                    continue;
                }

                //判断上次执行距离现在是否满足时间
                $scheduleTime = $warning->warningRule[0]->schedule_time;
                if (strtotime($warning->last_warning_time) > (time() - ($scheduleTime - 600))) {
                    continue;
                }

                try {
                    //循环异常类型
                    $isTarget = false;
                    //告警记录ID
                    $warningRecordId = null;
                    foreach ($warning->exception_type as $type) {
                        //校验规则
                        $result = $this->checkRules($type, $warning);
                        if ($result[0]) {
                            $isTarget = true;
                            // 将触发预警的记录存储数据库中
                            $statisticTime = [$startTime, $endTime];
                            $appName = $apps[$warning->developer_app_id] ?? '';
                            if (!$warningRecordId) {
                                $warningRecordId = $this->saveWarningRecord($warning, $statisticTime, $appName);
                            }
                            //发送通知 可能设置多个通知方式
                            $this->send($warning, $warningRecordId, $statisticTime, $appName, $result[1]);
                        }
                    }
                    if ($isTarget) {
                        //同步更新最近预警时间、触发次数自增+1
                        $warning->increment('trigger_count', 1, ['last_warning_time' => date('Y-m-d H:i:s')]);
                    }
                } catch (\Exception $err) {
                    Log::error('problem warning预警检查队列规则检测报错:' . $err->getMessage() . ' in: ' . $err->getFile() . ' line: ' . $err->getLine());
                }
            }
        } catch (\Exception $e) {
            Log::error('problem warning预警检查队列报错:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
        Log::info('problem warning预警检查队列结束');
    }

    /**
     * 获取统计数据
     *
     * @param int $type
     * @param Warning $warning
     * @return array
     */
    private function getStatData(int $type, Warning $warning): array
    {
        // 获取异常数据
        $exceptionData = (new ExceptionIndexHandler($type, $warning))->getData();
        // 获取昨天数据
        $yesterdayExceptionData = (new YesterdayExceptionIndexHandler($type, $warning))->getData();
        // 获取七天数据
        $sevenDayExceptionData = (new SevenDayExceptionIndexHandler($type, $warning))->getData();
        // 获取最近1个小时环比数据
        $lastHourExceptionData = (new LastHourExceptionIndexHandler($type, $warning))->getData();
        // 获取启动数据
        $startData = (new StartIndexHandler($warning))->getData();
        // 获取昨天启动数据
        $yesterdayStartData = (new YesterdayStartIndexHandler($warning))->getData();
        // 获取七天启动数据
        $sevenDayStartData = (new SevenDayStartIndexHandler($warning))->getData();
        // 获取最近1个小时环比启动数据
        $lastHourStartData = (new LastHourStartIndexHandler($warning))->getData();
        // 返回数据
        return [
            $exceptionData,
            $yesterdayExceptionData,
            $sevenDayExceptionData,
            $lastHourExceptionData,
            $startData,
            $yesterdayStartData,
            $sevenDayStartData,
            $lastHourStartData,
        ];
    }

    /**
     * 检查规则
     *
     * @param int $type
     * @param Warning $warning
     * @return array
     */
    private function checkRules(int $type, Warning $warning): array
    {
        // 获取统计数据
        [$exceptionData, $yesterdayExceptionData, $sevenDayExceptionData, $lastHourExceptionData, $startData, $yesterdayStartData, $sevenDayStartData, $lastHourStartData] = $this->getStatData($type, $warning);
        //保存校验结果
        $results = [];
        //循环校验规则
        foreach ($warning->warningRule as $rule) {
            //获取规则校验结果
            $value = $this->getCompareValue($rule->index, [
                'num' => $exceptionData[0]['num'],
                'dev_num' => $exceptionData[0]['dev_num'],
            ], [
                'num' => $startData[0]['num'] ?? 0,
                'dev_num' => $startData[0]['dev_num'] ?? 0,
            ]);
            $originValue = $value;
            //根据value_type执行不同操作
            if ($rule->value_type > 1) {
                if (in_array($rule->value_type, [2, 3])) {
                    $yesterdayValue = $this->getCompareValue($rule->index, [
                        'num' => $yesterdayExceptionData[0]['num'] ?? 0,
                        'dev_num' => $yesterdayExceptionData[0]['dev_num'] ?? 0,
                    ], [
                        'num' => $yesterdayStartData[0]['num'] ?? 0,
                        'dev_num' => $yesterdayStartData[0]['dev_num'] ?? 0,
                    ]);
                    if ($rule->value_type === 2) {
                        $value -= $yesterdayValue;
                    } else {
                        if ($value > 0) {
                            $value = (($value - $yesterdayValue) / $yesterdayValue) * 100;
                            // 保留两位小数
                            $value = round($value, 2);
                        }
                    }
                }
                if (in_array($rule->value_type, [4, 5])) {
                    $sevenDayValue = $this->getCompareValue($rule->index, [
                        'num' => $sevenDayExceptionData[0]['num'] ?? 0,
                        'dev_num' => $sevenDayExceptionData[0]['dev_num'] ?? 0,
                    ], [
                        'num' => $sevenDayStartData[0]['num'] ?? 0,
                        'dev_num' => $sevenDayStartData[0]['dev_num'] ?? 0,
                    ]);
                    if ($rule->value_type === 4) {
                        $value -= $sevenDayValue;
                    } else {
                        if ($value > 0) {
                            $value = (($value - $sevenDayValue) / $sevenDayValue) * 100;
                            // 保留两位小数
                            $value = round($value, 2);
                        }
                    }
                }
                if (in_array($rule->value_type, [6, 7])) {
                    $lastHourValue = $this->getCompareValue($rule->index, [
                        'num' => $lastHourExceptionData[0]['num'] ?? 0,
                        'dev_num' => $lastHourExceptionData[0]['dev_num'] ?? 0,
                    ], [
                        'num' => $lastHourStartData[0]['num'] ?? 0,
                        'dev_num' => $lastHourStartData[0]['dev_num'] ?? 0,
                    ]);
                    if ($rule->value_type === 6) {
                        $value -= $lastHourValue;
                    } else {
                        if ($value > 0) {
                            $value = (($value - $lastHourValue) / $lastHourValue) * 100;
                            // 保留两位小数
                            $value = round($value, 2);
                        }
                    }
                }
            }
            $result = $this->compareMinAndMax($rule->operator, $value, $rule->value);
            $results[] = [
                'result' => $result,
                'value' => bcadd($value, 0, 2),
                'rule' => $rule,
                'origin_value' => $originValue,
            ];
        }
        //TODO: 自动创建TAPD单，只插入最多的10条
        return $this->getCheckResult($results, $warning, $type);
    }

    /**
     * 获取校验结果
     *
     * @param array $results
     * @param Warning $warning
     * @param int $type
     * @return array
     */
    private function getCheckResult(array $results, Warning $warning, int $type): array
    {
        $passNum = 0;
        $texts = [];
        foreach ($results as $result) {
            if ($result['result']) {
                $passNum++;
                $text = Warning::EXCEPTION_TYPE_TEXT[$type] . WarningRule::INDEX[$result['rule']->index];
                $unit = '';
                $compareUnit = '';
                // 判断类型
                if (in_array($result['rule']->index, [WarningRule::INDEX_CRASH_RATE, WarningRule::INDEX_CRASH_USER_RATE], true)) {
                    // 加上百分比符号
                    $unit = '%';
                    $compareUnit = '%';
                }
                $compare = '';
                // 判断是否需要环比
                if ($result['rule']->value_type > 1) {
                    $compare = "，" . (WarningRule::VALUE_TYPE[$result['rule']->value_type] ?? '') . $result['value'];
                    // 百分比
                    if (in_array($result['rule']->value_type, [3, 5, 7])) {
                        $compareUnit = '%';
                        $compare .= "%";
                    }
                }
                $value = (int) $result['rule']->value;
                $texts[] = "在最近1小时时间内，{$text}{$result['origin_value']}{$unit}{$compare} (告警阈值：{$result['rule']->operator} {$value}{$compareUnit})";
            }
        }
        if ($warning['condition_mode'] === Warning::CONDITION_MODE_ANY) {
            $check = $passNum > 0;
        } else {
            $check = count($results) === $passNum;
        }
        return [$check, $texts];
    }

    /**
     * 获取比较的值
     *
     * @param $index
     * @param $exceptionData
     * @param $starData
     * @return int
     */
    private function getCompareValue($index, $exceptionData, $starData): int
    {
        $value = 0;
        switch ($index) {
            case WarningRule::INDEX_CRASH_COUNT:
                $value = $exceptionData['num'] ?? 0;
                break;
            case WarningRule::INDEX_CRASH_RATE:
                $startNum = $starData['num'] ?? 0;
                $value = $startNum ? bcmul(bcdiv($exceptionData['num'], $startNum, 4), 100, 2) : 0;
                break;
            case WarningRule::INDEX_CRASH_USER_COUNT:
                $value = $exceptionData['dev_num'] ?? 0;
                break;
            case WarningRule::INDEX_CRASH_USER_RATE:
                $startDevNum = $starData['dev_num'] ?? 0;
                $value = $startDevNum ? bcmul(bcdiv($exceptionData['dev_num'], $startDevNum, 4), 100, 2) : 0;
                break;
        }
        return $value;
    }

    /**
     * 比较大小
     *
     * @param $operator
     * @param $one
     * @param $second
     * @return bool
     */
    private function compareMinAndMax($operator, $one, $second): bool
    {
        $result = false;
        switch ($operator) {
            case '>':
                $result = $one > $second;
                break;
            case '<':
                $result = $one < $second;
                break;
        }
        return $result;
    }

    /**
     * 存储触发预警的记录
     *
     * @param Warning $warning
     * @param array $statisticTime
     * @param string $appName
     * @return mixed
     */
    public function saveWarningRecord(Warning $warning, array $statisticTime, string $appName)
    {
        $warningRecord = new WarningRecord();
        $fields = array(
            'developer_app_id' => $warning->developer_app_id,
            'warning_id' => $warning->warning_id,
            'rule_id' => 0,
            'app_name' => $appName,
            'os_type' => $warning->os_type,
            'app_version' => $warning->app_version,
            'start_date' => date('Y-m-d H:i:s', $statisticTime[0]),
            'end_date' => date('Y-m-d H:i:s', $statisticTime[1]),
        );
        $warningRecord->fill($fields);
        $warningRecord->save();
        return $warningRecord->id;
    }

    /**
     * 发送通知
     *
     * @param Warning $warning
     * @param $warningRecordId
     * @param $statisticTime
     * @param $appName
     * @param $rules
     * @throws GuzzleException
     */
    private function send(Warning $warning, $warningRecordId, $statisticTime, $appName, $rules)
    {
        //统计时间
        $countTime = date('Y-m-d H:i:s', $statisticTime[0]) . '~' . date('Y-m-d H:i:s', $statisticTime[1]);
        //告警规则
        $division = $warning->condition_mode == Warning::CONDITION_MODE_ANY ? '或者' : '并且';
        $rulesText = implode("\n                {$division} ", $rules);
        //个人推送
        if (!empty($warning->receiving_person)) {
            $message = sprintf(
                config('message.warning_wx_message'),
                $warning->name,
                date('Y-m-d H:i:s'),
                $appName,
                $warning->getOsType(),
                $warning->getAppVersion(),
                $warning->getMonitorRange(),
                $countTime,
                $rulesText,
                $this->getUrl($warning, $warningRecordId)
            );
            (new OauthCenterApi)->sendWXMsg($warning->receiving_person, $message);
        }
        //机器人推送
        if (!empty($warning->receiving_group)) {
            $message = sprintf(
                config('message.warning_wx_group_message'),
                $warning->name,
                date('Y-m-d H:i:s'),
                $appName,
                $warning->getOsType(),
                $warning->getAppVersion(),
                $warning->getMonitorRange(),
                $countTime,
                $rulesText,
                $this->getUrl($warning, $warningRecordId)
            );
            foreach ($warning->receiving_group as $receive) {
                $service = new WXGroupNoticeService($receive['url'], $receive['is_mentioned']);
                $service->wxGroupNotify($message, 'markdown');
            }
        }
        //电话预警
        if (!empty($warning->receiving_phone)) {
            $message = sprintf(
                config('message.warning_wx_message'),
                $warning->name,
                date('Y-m-d H:i:s'),
                $appName,
                $warning->getOsType(),
                $warning->getAppVersion(),
                $warning->getMonitorRange(),
                $countTime,
                $rulesText,
                $this->getUrl($warning, $warningRecordId)
            );
            VoiceHelper::callPhone([$message], $warning->receiving_phone, 1475525);
        }
    }

    /**
     * 获取推送跳转地址
     *
     * @param Warning $warning
     * @param $warningRecordId
     * @return string
     */
    private function getUrl(Warning $warning, $warningRecordId): string
    {
        // 拼接跳转的url
        $baseUrl = 'https://auth-pro-api.shiyue.com/oauth2/wechat-login?appId=1818223957164789760&gotoUri=https%3A%2F%2Fdeveloper-manager.shiyue.com%2Fauth-pro%3Fredirect_url%3D';

        return $baseUrl . base64_encode(config('app.front_url') . '/console/my-app/' . $warning->developer_app_id . '/app-monitor/global-config/warning-config?warning_id=' . $warning->warning_id . '&warning_record_id=' . $warningRecordId . '&showList=true&auto_login=1');
    }
}
