<?php

/**
 * 关键词过滤异步任务
 * @desc 关键词过滤异步任务
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs;

use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ExceptionKeywordJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 筛选字段
     *
     * @var array
     */
    public const FILTER_COLUMNS = ['explain_desc', 'subject_name'];
    /**
     * 限制每次查询数量
     *
     * @var int
     */
    public const LIMIT = 10000;
    /**
     * 执行时长，一个小时
     *
     * @var int
     */
    public $timeout = 3600;
    /**
     * 效能后台ID
     *
     * @var int
     */
    private $developerAppId;

    /**
     * 开始时间
     *
     * @var int
     */
    private $startTime;

    /**
     * 结束时间
     *
     * @var int
     */
    private $endTime;

    /**
     * 关键词
     *
     * @var array
     */
    private $keywords;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(int $developerAppId, int $startTime, int $endTime, array $keywords)
    {
        $this->developerAppId = $developerAppId;
        $this->startTime = $startTime;
        $this->endTime = $endTime;
        $this->keywords = $keywords;
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle(): void
    {
        //打印日志
        Log::info("执行关键词过滤脚本开始，应用ID：{$this->developerAppId}");
        //设置脚本内存
        ini_set('memory_limit', '2048M');
        //构造Builder
        $offset = 0;
        $builder = ExceptionStreamAll::query()
            ->selectRaw('exception_block_id, event_name, stream_date, any_value(subject_name) as subject_name, any_value(explain_desc) as explain_desc')
            ->where('extra_app_id', $this->developerAppId)
            ->where('stream_date', ">=", date('Y-m-d', $this->startTime))
            ->where('stream_date', "<=", date('Y-m-d', $this->endTime))
            ->where('event_name', '!=', 'exception_start')
            ->where('stream_time', '>=', $this->startTime)
            ->where('stream_time', '<=', $this->endTime)
            ->offset($offset)
            ->limit(self::LIMIT)
            ->orderBy('exception_block_id')
            ->groupBy(['exception_block_id', 'event_name', 'stream_date']);
        $values = $this->getKeywordList();
        $builder->where(function ($query) use ($values) {
            foreach (self::FILTER_COLUMNS as $column) {
                $keys1 = array_keys($values['1']);
                foreach ($keys1 as $v1) {
                    $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like? ", '%' . $v1 . '%');
                }
                $keys2 = array_keys($values['2']);
                foreach ($keys2 as $v2) {
                    $query->orWhereRaw("replace(lower({$column}), '%0a', '%20') like? ", '%' . $v2 . '%');
                }
            }
            return $query;
        });
        //错误次数
        $errorNum = 0;
        //查询多次
        while (true) {
            //打印日志
            Log::info("执行关键词过滤脚本，应用ID：{$this->developerAppId}、offset：{$offset}");
            try {
                //获取数据
                $list = $builder->offset($offset)->getFromSR();
            } catch (\Exception|\Throwable $e) {
                Log::error("执行关键词过滤脚本报错，应用ID：{$this->developerAppId}、offset：{$offset}，错误信息：" . $e->getMessage());
                //累计错误次数
                $errorNum++;
                //判断错误次数
                if ($errorNum > 3) {
                    Log::error("执行关键词过滤脚本报错，应用ID：{$this->developerAppId}、offset：{$offset}，错误次数超过3次，终止执行");
                    break;
                }
                continue;
            }
            //判断是否有数据
            if (empty($list)) {
                break;
            }
            //插入数据
            $this->saveData($list, $values);
            //判断是否结束
            if (count($list) < self::LIMIT) {
                break;
            }
            $offset += self::LIMIT;
        }
        //打印日志
        Log::info("执行关键词过滤脚本完成，应用ID：{$this->developerAppId}");
    }

    /**
     * 获取整理好的关键词列表
     *
     * @return array
     */
    private function getKeywordList(): array
    {
        //整理过滤的关键词
        $values = [];
        foreach ($this->keywords as $value) {
            $urlEncodeValue = mb_strtolower(urlencode($value));
            $rawUrlEncodeValue = mb_strtolower(rawurlencode($value));
            $values['1'][mb_strtolower($value)] = $value;
            $values['1'][$urlEncodeValue] = $value;
            $values['1'][$rawUrlEncodeValue] = $value;
            $values['2'][str_replace(["%27", "%24"], ["\'", "$"], $urlEncodeValue)] = $value;
            $values['2'][str_replace(["%27", "%24"], ["\'", "$"], $rawUrlEncodeValue)] = $value;
        }
        return $values;
    }

    /**
     * 保存数据
     *
     * @param array $data
     * @param array $keywords
     * @return void
     */
    private function saveData(array $data, array $keywords): void
    {
        $newData = [];
        foreach ($data as $item) {
            //检查命中那些关键词
            $keywordList = [];
            foreach ($keywords as $list) {
                foreach ($list as $key => $val) {
                    if (stripos($item['subject_name'], $key) !== false || stripos($item['explain_desc'], $key) !== false) {
                        $keywordList[] = $val;
                    }
                }
            }
            //赋值数据
            $newData[] = [
                'extra_app_id' => $this->developerAppId,
                'event_name' => $item['event_name'],
                'exception_block_id' => $item['exception_block_id'],
                'stream_date' => $item['stream_date'],
                'keywords' => json_encode(array_values(array_unique($keywordList)), JSON_UNESCAPED_UNICODE),
            ];
        }
        //字段
        $columns = [
            'extra_app_id',
            'event_name',
            'keywords',
            'exception_block_id',
            'stream_date',
        ];
        //写入csv
        $path = (new WriteCsvFileService("app/exception-stream-keyword/" . Str::random() . ".csv", $columns, $newData))->write();
        //同步到starRocks
        (new SyncFileToStarRocksService($path, $columns, 'exception_stream_keyword'))->sync();
    }
}
