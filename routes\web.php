<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

//公共接口
Route::group(['middleware' => 'auth', 'prefix' => 'common'], function () {
    //符号表管理
    Route::group(['prefix' => 'symbol'], function () {
        Route::get('list', 'CommonController@symbolList');
        Route::get('index', 'CommonController@symbolIndex');
        Route::post('store/{symbol?}', 'CommonController@symbolCommonStore');
        Route::post('common/store/{symbol?}', 'CommonController@symbolCommonStore');
        Route::post('analysis', 'CommonController@symbolAnalysis');
        Route::post('del', 'CommonController@symbolDel');
    });
    Route::get('search', 'CrashController@search');
    //分享
    Route::post('share', 'CommonController@share');
});

//崩溃平台
Route::group(['middleware' => 'auth', 'prefix' => 'crash'], function () {
    //统计汇总
    Route::group(['prefix' => 'statistic'], function () {
        Route::get('distribution', 'CrashController@getDistribution');
        Route::get('distribution_all', 'CrashController@getDistributionAll');
        Route::get('summary', 'CrashController@summaryStatistic');
        Route::get('chart', 'CrashController@getStatisticChart');
        Route::get('hourTop', 'CrashController@hourTop');
        Route::get('dayTop', 'CrashController@dayTop');
    });
    Route::get('record-info', 'CrashController@recordInfo');
    Route::get('info', 'CrashController@info');
    Route::get('list', 'CrashController@list');
    Route::post('store', 'CrashController@store');
    Route::get('info-list', 'CrashController@infoList');
    Route::get('appVersionList', 'CrashController@appVersionList');
    Route::post('store-record', 'CrashController@storeRecord');
    Route::post('labelHandler', 'CrashController@labelHandler');
    Route::get('labelList', 'CrashController@labelList');
    Route::get('getExceptionOriginStacks', 'CrashController@getExceptionOriginStacks');
    // 导出数据
    Route::get('export/data', 'CrashController@exportData');
    // 导出状态
    Route::get('export/status', 'CrashController@exportStatus');
    // 下载
    Route::get('export/download', 'CrashController@exportDownload');
    // 搜索列表
    Route::get('search_list', 'CrashController@searchList');
    // 分析异常
    Route::get('analysisException', 'CrashController@analysisException');
    // 分析Logcat
    Route::get('analysisLogcat', 'CrashController@analysisLogcat');
    //OOM
    Route::group(['prefix' => 'oom'], function () {
        Route::get('list', 'OOMController@list');
        Route::get('statistic', 'OOMController@statistic');
    });
});

//预警计划
Route::group(['middleware' => 'auth', 'prefix' => 'warning'], function () {
    Route::get('list', 'WarningController@list');
    Route::post('store/{warning_id?}', 'WarningController@store');
    Route::delete('delete/{warning_id}', 'WarningController@delete');
    Route::get('update-status/{warning_id}', 'WarningController@updateStatus');
    Route::get('search', 'WarningController@search');
    Route::get('version', 'WarningController@version');
    Route::get('exceptionList', 'WarningController@exceptionList');
    Route::get('warningTimeRange', 'WarningController@warningTimeRange');
    Route::get('{warning_id}', 'WarningController@getContent');
});

//开关配置
Route::group(['middleware' => 'auth', 'prefix' => 'switch'], function () {
    Route::get('list', 'SwitchController@list');
    Route::post('add', 'SwitchController@add');
    Route::post('edit', 'SwitchController@edit');
    Route::post('status', 'SwitchController@status');
    Route::post('del', 'SwitchController@del');
});

// 白名单管理
Route::group(['middleware' => 'auth', 'prefix' => 'whitelist'], function () {
    // 版本白名单
    Route::group(['prefix' => 'version'], function () {
        Route::get('list', 'VersionWhiteListController@list');
        Route::post('operate', 'VersionWhiteListController@operate');
    });
    // 异常记录白名单
    Route::group(['prefix' => 'record'], function () {
        Route::get('list', 'RecordWhiteListController@list');
        Route::post('operate', 'RecordWhiteListController@operate');
    });
});

// 异常记录处理
Route::group(['middleware' => 'auth', 'prefix' => 'handle'], function () {
    Route::post('operate', 'ExceptionHandleController@operate');
    Route::get('show', 'ExceptionHandleController@show');
    Route::get('list', 'ExceptionHandleController@list');
});

//首頁數據
Route::group(['middleware' => 'auth', 'prefix' => 'home'], function () {
    Route::get('networkNumList', 'HomeController@networkNumList'); // 首页数据——联网设备排行
    Route::get('crashRankList', 'HomeController@crashRankList'); // 首页数据——崩溃率排行
    Route::get('errorRankList', 'HomeController@errorRankList'); // 首页数据——错误率排行
    Route::get('rankCommonList', 'HomeController@rankCommonList');
    Route::get('rankSearchList', 'HomeController@rankSearchList');
    Route::get('hitbugChart', 'HomeController@hitbugChart');
});

//tapd
Route::group(['middleware' => 'auth', 'prefix' => 'tapd'], function () {
    Route::post('create', 'TapdController@create'); // 创建tapd缺陷
    Route::post('relation', 'TapdController@relation'); // 关联tapd缺陷
    Route::get('link', 'TapdController@link'); // 获取tap跳转链接
    Route::get('untie', 'TapdController@untie'); // 解绑tapd链接
    Route::get('handler', 'TapdController@handler'); // 获取tapd项目用户
    Route::get('iteration', 'TapdController@iteration'); // 获取tapd项目迭代
    Route::get('releases', 'TapdController@releases'); // 获取tapd项目发布计划
    Route::get('versions', 'TapdController@versions'); // 获取tapd项目发布计划
    Route::post('itemBindHandler', 'TapdController@itemBindHandler'); // 操作項目綁定
    Route::get('pullBugStatusList', 'TapdController@pullBugStatusList'); // 拉取缺陷狀態--列表
    // Route::get('pullBugStatus', 'TapdController@pullBugStatus'); // 拉取缺陷狀態--單個
    Route::get('getAutoConfig', 'TapdController@getTapdAutoConfig'); // 获取自动提单配置
    Route::post('saveAutoConfig', 'TapdController@saveTapdAutoConfig'); // 保存自动提单配置
});

//过滤配置
Route::group(['middleware' => 'auth', 'prefix' => 'filter'], function () {
    //关键词
    Route::group(['prefix' => 'keyword'], function () {
        Route::get('list', 'FilterKeywordController@list');
        Route::post('add', 'FilterKeywordController@add');
        Route::post('update', 'FilterKeywordController@update');
        Route::post('del', 'FilterKeywordController@del');
    });
});

//全局配置
Route::group(['middleware' => 'auth', 'prefix' => 'config'], function () {
    Route::get('get', 'GlobalConfigController@get'); //获取配置
    Route::post('add', 'GlobalConfigController@add'); //添加配置
});

//巡检配置
Route::group(['middleware' => 'auth', 'prefix' => 'inspection'], function () {
    Route::get('list', 'InspectionController@list');
    Route::post('add', 'InspectionController@add');
    Route::post('edit', 'InspectionController@edit');
    Route::post('status', 'InspectionController@status');
    Route::post('del', 'InspectionController@delete');
});

//异常处理配置
Route::group(['middleware' => 'auth', 'prefix' => 'handler'], function () {
    // 异常处理配置路由
    Route::get('/list', [App\Http\Controllers\ExceptionHandlerController::class, 'list']);
    Route::post('/add', [App\Http\Controllers\ExceptionHandlerController::class, 'add']);
    Route::post('/edit', [App\Http\Controllers\ExceptionHandlerController::class, 'edit']);
    Route::post('/del', [App\Http\Controllers\ExceptionHandlerController::class, 'delete']);
    Route::get('/stat', [App\Http\Controllers\ExceptionHandlerController::class, 'stat']);
    Route::get('/hourStat', [App\Http\Controllers\ExceptionHandlerController::class, 'hourStat']);

    // 异常处理人巡检配置路由
    Route::group(['prefix' => 'webhook'], function () {
        Route::get('/list', [App\Http\Controllers\ExceptionHandlerWebhookController::class, 'list']);
        Route::post('/add', [App\Http\Controllers\ExceptionHandlerWebhookController::class, 'add']);
        Route::post('/edit', [App\Http\Controllers\ExceptionHandlerWebhookController::class, 'edit']);
        Route::post('/del', [App\Http\Controllers\ExceptionHandlerWebhookController::class, 'delete']);
        Route::post('/status', [App\Http\Controllers\ExceptionHandlerWebhookController::class, 'status']);
    });
});

Route::post('/starRocks/query', 'StarRocksController@query'); // StarRocks查询

Route::get('home/getWarningWeekData', [App\Http\Controllers\CommonController::class, 'getWarningWeekData']);

Route::post('/api/tapd/unbind', [App\Http\Controllers\CommonController::class, 'unbind']); // Tapd解绑

Route::get('/api/summary/statistic', [App\Http\Controllers\ApiController::class, 'summaryStatistic']); // 异常统计

Route::get('/api/getMonitorData', [App\Http\Controllers\ApiController::class, 'monitor']);
