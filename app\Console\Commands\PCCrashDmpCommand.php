<?php

/**
 * PC崩溃dmp解析脚本
 * @desc PC崩溃dmp解析脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class PCCrashDmpCommand extends Command
{
    /**
     * 执行间隔时间
     *
     * @var int
     */
    const TIME = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pc:crash:dmp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PC崩溃dmp解析脚本';

    /**
     *  PC崩溃dmp解析脚本
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        Log::info('PC崩溃dmp解析脚本开始执行');
        // 获取redis连接
        $redis = Redis::connection('api');
        // 获取要执行的时长
        $time = time() + (self::TIME * 60);
        // 循环执行
        while (time() < $time) {
            try {
                $result = $redis->lIndex('hitbug_pc_crash_list', -1);
                // 判断是否为空
                if (empty($result)) {
                    sleep(10); // 休眠10秒
                    continue;
                }
                // 解析数据
                $data = json_decode($result, true);
                // 解析dmp文件
                $this->parseDmp($data);
                // 添加到解析队列
                $redis->lPush('hitbug_pc_crash_parse_list', $result);
                // 移除数据
                $redis->rPop('hitbug_pc_crash_list');
            } catch (\Exception $e) {
                Log::error('PC崩溃dmp解析脚本执行失败，错误信息：：' . $e->getMessage() . '，堆栈：' . $e->getTraceAsString());
                sleep(10); // 每次执行失败后等待10秒
            }
        }
    }

    /**
     * 解析dmp文件
     *
     * @return void
     * @throws \Exception
     * @var array $data
     */
    private function parseDmp($data)
    {
        // 打印日志
        Log::info('开始解析dmp文件，文件名：' . $data['file_name']);
        // 调用接口进行解析
        $response = HttpAgent::getInstance()->request('POST', 'https://crashpad.shiyue.com/dump/decode', [
            'json' => [
                'dump_file_name' => $data['file_name'],
            ],
        ]);
        if (!$response['success']) {
            throw new \Exception('请求接口失败：' . $response['message']);
        }
    }
}
