<?php

/**
 * 性能质量巡检配置
 * @desc 性能质量巡检配置
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/05
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model;

class InspectionConfig extends BaseModel
{
    protected $table = 'inspection_config';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'os_type',
        'category',
        'trigger_time',
        'day',
        'creator',
        'editor',
        'status',
        'webhook_url',
    ];

    /**
     * 强制转换的属性
     *
     * @var array
     */
    protected $casts = [
        'webhook_url' => 'array',
    ];
}
