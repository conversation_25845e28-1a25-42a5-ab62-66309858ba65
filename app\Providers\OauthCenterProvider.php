<?php

namespace App\Providers;

use App\Components\OauthCenter\OauthCenterApi;
use Illuminate\Support\ServiceProvider;

class OauthCenterProvider extends ServiceProvider
{
    /**
     * 指定提供者加载是否延缓。
     *
     * @var bool
     */
    protected $defer = true;

    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('oauthcenter', function () {
            return new OauthCenterApi();
        });
    }

    /**
     * @return array
     */
    public function provides()
    {
        return ['oauthcenter'];
    }
}
