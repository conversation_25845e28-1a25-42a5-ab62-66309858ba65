<?php

/**
 * PC崩溃解析脚本
 * @desc PC崩溃解析脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\HttpAgent;
use App\Service\AnalysisService;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class PCCrashAnalysisCommand extends Command
{
    /**
     * 执行间隔时间
     *
     * @var int
     */
    const TIME = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pc:crash:analysis';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PC崩溃解析脚本';

    /**
     *  PC崩溃解析脚本
     *
     * @return void
     */
    public function handle(): void
    {
        //设置脚本内存
        ini_set('memory_limit', '1024M');
        Log::info('PC崩溃解析脚本开始执行');

        $redis = Redis::connection('api');
        $time = time() + (self::TIME * 60);
        $result = '';
        while (time() < $time) {
            try {
                $result = $redis->lIndex('hitbug_pc_crash_parse_list', -1);
                // 判断是否为空
                if (empty($result)) {
                    sleep(10); // 休眠10秒
                    continue;
                }
                // 解析数据
                $data = json_decode($result, true);
                // 解析data的content的内容
                $data['content'] = json_decode($data['content'], true);
                // 获取解析的数据
                $data = $this->getReportDataJson($data);
                // 解析数据
                $this->parseData($data);
                // 移除数据
                $redis->rPop('hitbug_pc_crash_parse_list');
            } catch (\Exception $e) {
                if ($e->getCode() == 10010) {
                    // 移除数据
                    $redis->rPop('hitbug_pc_crash_parse_list');
                }
                Log::error('PC崩溃解析脚本执行失败，错误信息：' . $e->getMessage() . '，堆栈：' . $e->getTraceAsString() . "result：" . $result);
                sleep(10); // 每次执行失败后等待10秒
            }
        }
        Log::info('PC崩溃解析脚本执行结束');
    }

    /**
     * 获取报告数据
     *
     * @return array
     */
    private function getReportDataJson(array $data)
    {
        $response = HttpAgent::getInstance()->request('GET', env('COS_BASE_URL') . str_replace('.dmp', '_sym.json', $data['file_name']));

        $consoleRes = ['message' => ''];
        if ($data['console_log_file']) {
            $consoleRes = HttpAgent::getInstance()->request('GET', env('COS_BASE_URL') . $data['console_log_file']);
        }

        $message = json_decode($response['message'], true);

        if (empty($message)) {
            //判断当前数据的时间是否超过30分钟，如果是，则丢弃
            if ($data['stream_time'] < (time() - 1800)) {
                throw new Exception('还没生成解析数据', 10010);
            }
            throw new Exception('还没生成解析数据');
        }

        if (empty($message['crashing_thread'])) {
            throw new Exception('解析数据crashing_thread为空', 10010);
        }

        $data['content']['origin_stacks'] = $message['crashing_thread']['frames'];
        $data['content']['singular_explain'] = (new AnalysisService(null))->getThreadStackText($message['crashing_thread']['frames'][0]);
        $data['content']['content'] = $this->prettyJsonEncode($message['threads']);
        $data['content']['console_info'] = explode("\r\n", $consoleRes['message']);
        $data['content']['singular_name'] = $message['crash_info']['type'];
        $data['content']['exception_file'] = $data['file_name'];
        $files = [
            $data['file_name'],
            str_replace('dmp', 'json', $data['file_name']),
        ];
        if (!empty($data['console_log_file'])) {
            $files[] = $data['console_log_file'];
        }
        if (!empty($data['player_log_file'])) {
            $files[] = $data['player_log_file'];
        }
        $data['content']['exception_file2'] = implode(',', $files);
        // 处理uuid_info
        $data['content']['uuid_info'] = [];
        $uuidInfo = array_column($message['crashing_thread']['frames'], 'module');
        $modules = $message['modules'] ?? [];
        foreach ($modules as $value) {
            if (in_array($value['filename'], $uuidInfo)) {
                $data['content']['uuid_info'][] = $value;
            }
        }
        return $data;
    }

    /**
     * 解析数据
     *
     * @param array $data
     * @return void
     */
    private function parseData(array $data)
    {
        // $currentTime = $data['stream_time'];
        $currentTime = time();
        $input = $data['content'];
        // 设置IP
        $input['ip'] = $data['ip'] ?? '';
        //获取角色id以及角色名称
        $roleId = '';
        $roleName = '';
        $input['expand'] = empty($input['expand']) ? [] : $input['expand'];
        foreach ($input['expand'] as $value) {
            if (isset($value['key'])) {
                if ($value['key'] == 'roleID') {
                    $roleId = $value['value'] ?? '';
                    //判断roleId是否等于 角色ID，如果是，则需要取 name 的值
                    if ($roleId == '角色ID') {
                        $roleId = $value['name'] ?? '';
                    }
                }
                if ($value['key'] == 'roleName') {
                    $roleName = $value['value'] ?? '';
                    //判断roleName是否等于 角色名称，如果是，则需要取 name 的值
                    if ($roleName == '角色名称') {
                        $roleName = $value['name'] ?? '';
                    }
                }
            }
        }
        //iOS截取崩溃说明匹配字段
        $originalExplain = $explain = $input['singular_explain'] ?? '';
        //format time[START]
        $devCreateTime = (int)($input['detail_info']['client_ts'] ?? 0);
        $eventTime = (int)($input['detail_info']['ts'] ?? ($devCreateTime ?: $currentTime));
        // 获取上报数据
        $data = $this->getReportData($input, $currentTime, $devCreateTime, $roleId, $roleName, $originalExplain, $explain, $eventTime);
        // 保存数据
        $this->saveData($data);
    }

    /**
     * 获取上报数据
     *
     * @param array $input
     * @param string $scope
     * @param int $currentTime
     * @param int $devCreateTime
     * @param int $roleId
     * @param string $roleName
     * @param string $originalExplain
     * @param string $explain
     * @param int $eventTime
     * @return array
     */
    private function getReportData($input, $currentTime, $devCreateTime, $roleId, $roleName, $originalExplain, $explain, $eventTime)
    {
        //需要上报的数据
        $data = [
            'stream_date' => date('Y-m-d', $currentTime),
            'stream_time' => $currentTime,
            'event_name' => 'exception_crash',
            'timezone' => 0, //TODO:預留（默認：東八區）
            'event_time' => $eventTime,
            'dev_create_time' => $devCreateTime, //[產生異常的]發生時間，備註：存在{devCreateTime}相同，但{eventTime}不同的情況（即：重複上報）
            'extra_app_id' => (int)($input['extra_app_id'] ?? 0),
            'server_dev_str' => $input['detail_info']['server_dev_str'] ?? '',
            //組合篩選[START]
            'type' => (int)($input['singular_type'] ?? 0), //崩潰類型，值：1java，2native
            'os_type' => (string)($input['detail_info']['os_type'] ?? 1),
            'version' => $input['detail_info']['sdk_ver'] ?? '', //SDK版本
            'os_version' => $input['detail_info']['os_version'] ?? '',
            'app_version' => $input['detail_info']['app_version'] ?? '',
            'is_success' => (int)($input['detail_info']['is_prison_break'] ?? 0), //是否越狱，值：0否，1是
            'operate_status' => $input['detail_info']['around_status'] ?? 0, //是否后台，值： 0前台，1后台
            'release_store' => $input['detail_info']['channel_source'] ?? '', //發佈渠道
            'manufacturer' => $input['detail_info']['device_brand'] ?? '',
            'device_model' => $input['detail_info']['device_model'] ?? '',
            'current_page_title' => $input['detail_info']['crash_page'] ?? '',
            //組合篩選[END]
            'app_id' => (int)($input['detail_info']['appId'] ?? 0),
            'game_id' => (int)($input['game_id'] ?? 0),
            'account_id' => (string)($input['account_id'] ?? 0),
            'role_id' => $roleId,
            'role_name' => $roleName,
            'ip' => $input['ip'] ?? 0,
            'subject_name' => $input['singular_name'] ?? '',
            'sdk_package_name' => $input['detail_info']['sdk_package_name'] ?? '',
            'extra' => ($input['expand'] ?? []) ? $this->prettyJsonEncode($input['expand']) : '', //自定義信息
            'explain_desc' => $explain, //[歸納/匯總後的]异常詳情
            'content' => $input['content'] ?? '', //上報內容
            'search_content' => $originalExplain, //[原始的]異常詳情
            'page_info_json' => ($input['page_info'] ?? []) ? $this->prettyJsonEncode($input['page_info']) : '', //頁面追蹤
            'game_info_json' => ($input['uuid_info'] ?? []) ? $this->prettyJsonEncode($input['uuid_info']) : '', //[符號相關的]uuid列表
            'basic_info_json' => ($input['detail_info'] ?? []) ? $this->prettyJsonEncode($input['detail_info']) : '', //[設備]基礎信息
            'memory_info_json' => ($input['memory_info'] ?? []) ? $this->prettyJsonEncode($input['memory_info']) : '', //[設備]存儲數據
            'console_info_json' => ($input['console_info'] ?? []) ? $this->prettyJsonEncode($input['console_info']) : '', //跟蹤日誌
            'origin_stacks_json' => ($input['origin_stacks'] ?? []) ? $this->prettyJsonEncode($input['origin_stacks']) : '', //原始堆栈
            'duration' => (int)($input['duration_time'] ?? 0), //[持續時間內的]錯誤次數
            'is_emulator' => (int)($input['detail_info']['is_emulator'] ?? 0), //是否模擬器，值：0否，1是
            'inner_version' => '',
            'exception_image' => '', /* 增加图片截图名称 */
            'exception_file' => $input['exception_file'] ?? '', /* 增加上报文件名称 */
            'exception_file2' => $input['exception_file2'] ?? '', /* 增加上报文件名称 */
        ];
        // 获取extra字段
        $extra = $data['extra'];
        // 判断是否可以转为数组
        if (is_string($extra)) {
            $extra = json_decode($extra, true);
            // 判断是否转换成功
            if ($extra) {
                // 从数组中找到对应的值“游戏资源版本号”值
                foreach ($extra as $item) {
                    if (($item['name'] ?? '') == '游戏资源版本号') {
                        $data['inner_version'] = $item['value'] ?? '';
                    }
                }
            }
        }
        $data['exception_block_id'] = $this->createExceptionBlockId($data, 1);
        // 返回数据
        return $data;
    }

    /**
     * 创建问题ID
     *
     * @param array $data
     * @param $type
     * @return string
     */
    private function createExceptionBlockId(array $data, $type): string
    {
        $explain = $data['explain_desc'];
        $length = stripos($data['explain_desc'], '- stack1');
        if ($length) {
            $explain = substr($data['explain_desc'], 0, $length);
        }

        return strtoupper(md5($data['extra_app_id'] . $type . $data['os_type'] . $data['type'] . $data['subject_name'] . $explain));
    }

    /**
     * 创建异常合并ID
     *
     * @param array $data
     * @return string
     */
    private function createExceptionMergeId(array $data): string
    {
        ksort($data);
        return strtoupper(md5(json_encode($data)));
    }

    /**
     * 生成json格式
     *
     * @param $object
     * @param int|null $flag
     * @return string
     */
    private function prettyJsonEncode($object, ?int $flag = JSON_PRETTY_PRINT): string
    {
        //JSON_PRETTY_PRINT//易讀格式（即：自動換行）
        $flagCounter = JSON_UNESCAPED_SLASHES/*不轉義反斜杠*/ | JSON_UNESCAPED_UNICODE/*unicode轉至中文*/
        ;
        if (!$flag) {
            $flagCounter |= $flag;
        }
        return json_encode($object, $flagCounter);
    }

    /**
     * 保存数据
     *
     * @param array $data
     * @return void
     */
    private function saveData(array $data)
    {
        //公用参数
        $common = [
            'game_id' => intval($data['project_id'] ?? ''), //项目id
            'scene_id' => 0, //場景ID
            'referrer_scene_id' => 0, //[進入頁面前]場景標識
            'system_source' => 3, //系統來源，值：1瀏覽器，2公眾號，3客戶端，4服務端
            'project_id' => 9, //項目ID，值：1社區，2積分商城，3遊戲客戶端，4用戶體系，5活動通用，6WEB支付，9效能系統
            'account_id' => (string)($data['account_id'] ?? 0),
        ];
        $data = array_merge($common, $data);
        $data['exception_merge_id'] = $this->createExceptionMergeId($data);
        //字段
        $columns = [
            'extra_app_id',
            'event_name',
            'exception_block_id',
            'exception_merge_id',
            'stream_date',
            'stream_time',
            'timezone',
            'event_time',
            'dev_create_time',
            'server_dev_str',
            'type',
            'os_type',
            'version',
            'os_version',
            'app_version',
            'is_success',
            'operate_status',
            'release_store',
            'manufacturer',
            'device_model',
            'current_page_title',
            'app_id',
            'game_id',
            'scene_id',
            'referrer_scene_id',
            'project_id',
            'system_source',
            'account_id',
            'role_id',
            'role_name',
            'ip',
            'subject_name',
            'sdk_package_name',
            'extra',
            'explain_desc',
            'content',
            'search_content',
            'page_info_json',
            'game_info_json',
            'basic_info_json',
            'memory_info_json',
            'console_info_json',
            'origin_stacks_json',
            'duration',
            'is_emulator',
            'inner_version',
            'exception_image',
            'exception_file',
            'exception_file2',
        ];
        //写入csv
        $path = (new WriteCsvFileService("app/exception-stream-all/" . Str::random() . ".csv", $columns, [$data]))->write();
        //同步到starRocks
        (new SyncFileToStarRocksService($path, $columns, 'exception_stream_all'))->sync();
    }
}
