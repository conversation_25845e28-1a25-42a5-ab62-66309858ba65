<?php

/**
 * 预警记录服务类
 * @desc 预警记录服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/09/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Exception\Warning;

use App\Components\ClickHouse\ClickHouse;
use App\Http\Logic\ExceptionLogic;
use App\Model\ClickHouse\UserLogDataAll;
use App\Model\WarningRecord;
use App\Service\Exception\Analysis\ExceptionList;
use Illuminate\Http\Request;

class Records extends Base
{
    /**
     * 获取预警记录
     *
     * @return array
     */
    public function warningRecords(): array
    {
        //获取预警记录
        $warningRecord = WarningRecord::query()
            ->with('warning')
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->where('warning_id', $this->params['warning_id'])
            ->where('id', $this->params['warning_record_id'])->first();
        //没有预警记录返回空信息
        if (empty($warningRecord) || empty($warningRecord->warning)) {
            return [
                'total' => 0,
                "list" => [],
            ];
        }
        //获取分页信息
        [$page, $perPage] = $this->getPageInfo();
        //获取异常列表
        [$total, $list] = (new ExceptionList($this->getRequest($warningRecord)))->getList('block_num', 'desc', $page, $perPage);
        return compact('total', 'list');
    }

    /**
     * 获取分页信息
     *
     * @return array
     */
    private function getPageInfo(): array
    {
        return [$this->params['page'] ?? 1, $this->params['per_page'] ?? 10];
    }

    /**
     * 获取请求对象
     *
     * @param $warningRecord
     * @return Request
     */
    private function getRequest($warningRecord): Request
    {
        $request = request();
        $params = [
            'developer_app_id' => $this->params['developer_app_id'],
            'type' => $warningRecord->warning->exception_type[0] ?? 1,
            'exception_block_id' => $this->getBlocksIds($warningRecord),
            'start_date' => $warningRecord->start_date,
            'end_date' => $warningRecord->end_date,
            'start_time' => $warningRecord->start_date,
            'end_time' => $warningRecord->end_date,
        ];
        if (!empty($warningRecord->os_type)) {
            $params['os_type'] = $warningRecord->os_type;
        }
        if (!empty($warningRecord->app_version)) {
            $params['app_version'] = json_encode($warningRecord->app_version);
        }
        $request->replace($params);
        return $request;
    }

    /**
     * 获取异常ID集合
     *
     * @param $warningRecord
     * @return array
     */
    private function getBlocksIds($warningRecord): array
    {
        //获取事件名称
        $eventName = ExceptionLogic::EVENT_NAME[$warningRecord->warning->exception_type[0]] ?? null;
        //没有事件名称，返回空数组
        if (empty($eventName)) {
            return [];
        }

        //判断如果是没有监控范围，返回一个空数组
        if ($warningRecord->warning->monitor_range == 0) {
            return [];
        }

        $streamStartDate = date('Y-m-d', strtotime($warningRecord->start_date));
        $streamEndDate = date('Y-m-d', strtotime($warningRecord->end_date));

        //组装子SQL
        $builder = (new UserLogDataAll())
            ->selectRaw('exception_block_id, count(*) as num')
            ->where('event_name', $eventName)
            ->when($warningRecord->warning->app_version, function ($query, $appVersion) {
                return $query->whereIn('app_version', $appVersion);
            })
            ->when($warningRecord->warning->os_type, function ($query, $osType) {
                return $query->where('os_type', $osType);
            })
            ->where('extra_app_id', $warningRecord->warning->developer_app_id)
            ->where('server_dev_str', '!=', '')
            ->where(function ($query) use ($streamStartDate, $streamEndDate) {
                return $query->orWhere('stream_date', $streamStartDate)
                    ->orWhere('stream_date', $streamEndDate);
            })
            ->when($warningRecord->warning->monitor_range == 7, function ($query) use ($warningRecord) {
                $query->where(function ($query) use ($warningRecord) {
                    //循环关键词数据
                    foreach ($warningRecord->warning->exception_keywords as $keyword) {
                        $this->explainWhere($query, $keyword);
                    }
                });
                return $query;
            })
            ->whereRaw("(exception_block_id not in (select exception_block_id from (select exception_block_id, unnest as keyword from exception_stream_keyword, UNNEST(keywords) where extra_app_id = {$warningRecord->warning->developer_app_id} and event_name = 'exception_error' and stream_date >= '{$streamStartDate}' and stream_date <= '{$streamEndDate}' group by exception_block_id, keyword) t where keyword in (select keyword from exception_filter_keyword where developer_app_id = '{$warningRecord->warning->developer_app_id}') group by exception_block_id))")
            ->whereBetween('stream_time', [strtotime($warningRecord->start_date), strtotime($warningRecord->end_date)])
            ->groupBy('exception_block_id');

        if ($warningRecord->warning->stream_time_filter == 1 && $warningRecord->warning->stream_time_filter_hours > 0) {
            $builder = $builder->whereRaw(sprintf('exception_block_id in (select exception_block_id from exception_stat_all_v2 where min_stream_time >= %d)', strtotime("-{$warningRecord->warning->stream_time_filter_hours} hours")));
        }

        $subSql = (new ClickHouse())->getSqlBindings($builder);
        $result = (new ClickHouse())->getSqlData("SELECT exception_block_id FROM ({$subSql}) AS t ORDER BY num desc,exception_block_id desc limit {$this->getMonitorRange($warningRecord->warning->monitor_range)}");
        // 判断result是否为空, 如果为空,则返回一个不存在的数据
        if (empty($result)) {
            return ["00000000000000000000"];
        }
        return array_column($result, 'exception_block_id');
    }

    /**
     * 错误详情的where条件
     *
     * @param $query
     * @param $value
     * @return void
     */
    private function explainWhere($query, $value): void
    {
        $columns = ['explain_desc', 'subject_name'];
        $values = [$value, urlencode($value), rawurlencode($value)];
        foreach ($columns as $column) {
            foreach ($values as $val) {
                $query->orWhereRaw("replace(lower({$column}), '%0a', '+') like ? ", ['%' . mb_strtolower($val) . '%'])
                    ->orWhereRaw(
                        "replace(lower({$column}), '%0a', '%20') like ? ",
                        ['%' . mb_strtolower(str_replace(["%27", "%24"], ["\'", "$"], $val)) . '%']
                    );
            }
        }
    }

    /**
     * 获取监控范围
     *
     * @param $value
     * @return int
     */
    private function getMonitorRange($value): int
    {
        switch ($value) {
            case 1:
                return 5;
            case 2:
                return 10;
            case 3:
                return 20;
            case 4:
                return 30;
            case 5:
                return 50;
            case 6:
                return 100;
        }
        return 100;
    }
}
