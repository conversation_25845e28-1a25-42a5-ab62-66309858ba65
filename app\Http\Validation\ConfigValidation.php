<?php

/**
 * 全局配置验证类
 * @desc 全局配置验证类
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/27
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

/**
 * @method static ConfigValidation build()
 */
class ConfigValidation extends BaseValidation
{
    /**
     * 效能ID
     *
     * @return $this
     */
    public function developerAppId(): ConfigValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 键值数组
     *
     * @return $this
     */
    public function keys(): ConfigValidation
    {
        $this->rules['keys'] = 'required|string';
        return $this;
    }
}
