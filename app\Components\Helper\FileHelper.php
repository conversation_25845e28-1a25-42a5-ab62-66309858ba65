<?php
/**
 * FileHelper.php
 *
 * User: Dican
 * Date: 2022/9/26
 * Email: <<EMAIL>>
 */

namespace App\Components\Helper;


class FileHelper
{
    /**
     * 写入并保存文件
     * @param string $file 文件名，可带上目录
     * @param mixed $data 写入的数据
     */
    public static function write(string $file, $data)
    {
        $stream = fopen($file, 'w+') or die('Unable to open file!');
        fwrite($stream, $data);
        fclose($stream);
    }
}
