<?php

/**
 * 添加值类型字段到warning_rule表
 * @desc 添加值类型字段到warning_rule表
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/23
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddValueTypeToWarningRule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('exception')->table('warning_rule', function (Blueprint $table) {
            $table->tinyInteger('value_type')->default(1)
                ->comment('值类型，1：数值，2：和昨日同比数值增长，3：和昨日同比百分比增长，4：和7日前同比数值增长，5：和7日前同比百分比增长');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('exception')->table('warning_rule', function (Blueprint $table) {
            $table->dropColumn('value_type');
        });
    }
}
