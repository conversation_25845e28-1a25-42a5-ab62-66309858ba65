<?php

/**
 * Record.php
 *
 * User: Dican
 * Date: 2022/9/16
 * Email: <<EMAIL>>
 */

namespace App\Model;

use App\Model\StarRocks\StarRocksDB;
use App\Service\Exception\BaseService;
use Illuminate\Support\Facades\DB;

/**
 * App\Model\Record
 *
 * @property int $record_id
 * @property int $developer_app_id APP项目id
 * @property bool $type 异常类型 1为崩溃异常
 * @property bool $category 具体异常类型
 * @property string $name 异常说明
 * @property bool $os_type 平台类型;1为安卓,2为iOS
 * @property bool $status 状态 1未修复 2修复中 3已修复 4已忽略
 * @property int $handler_id 处理人id
 * @property string|null $handler 处理人
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereDeveloperAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereHandler($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereHandlerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereOsType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereRecordId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string|null $explain 异常描述
 * @property mixed exception_block_id 异常记录唯一id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Record whereExplain($value)
 */
class Record extends BaseModel
{
    use ModelTrait;

    protected $table = 'record_new';
    protected $primaryKey = 'record_id';
    protected $fillable = [
        'developer_app_id',
        'type',
        'category',
        'name',
        'os_type',
        'status',
        'handler_id',
        'handler',
        'handler_keyword_id',
        'exception_block_id',
        'explain',
    ];

    //异常类型
    const TYPE_CRASH = 1;
    const TYPE_ERROR = 2;
    const TYPE_ANR = 3;

    const CATEGORY_DEFAULT = 0;
    const CATEGORY_JAVA_EXCEPTION = 1;
    const CATEGORY_NATIVE_EXCEPTION = 2;
    const CATEGORY_EXTENSION_EXCEPTION = 3;
    const CATEGORY_CRASH_EXCEPTION = 4;
    const CATEGORY_OOM_EXCEPTION = 5;
    //    const CATEGORY_OTHER_EXCEPTION = 6;
    const CATEGORY_CSHARP_EXCEPTION = 7;
    const CATEGORY_LUA_EXCEPTION = 8;
    const CATEGORY_JS_EXCEPTION = 9;
    const CATEGORY_ANR_EXCEPTION = 10;

    //状态 1未处理 2修复中 3已修复 4已忽略
    const STATUS_WAIT = 1;
    const STATUS_RUNNING = 2;
    const STATUS_FINISH = 3;
    const STATUS_IGNORE = 4;

    // 异常记录处理状态，1未处理、2处理中、3已处理
    const EXCEPTION_NOT_HANDLE = 1;
    const EXCEPTION_PROCEED_HANDLE = 2;
    const EXCEPTION_FINISH_HANDLE = 3;
    const EXCEPTION_REPEATED_HANDLE = 4;

    public static $statusMessage = [
        self::STATUS_WAIT => '未处理',
        self::STATUS_RUNNING => '修复中',
        self::STATUS_FINISH => '已修复',
        self::STATUS_IGNORE => '已忽略',
    ];

    public static $categoryMessage = [
        self::CATEGORY_JAVA_EXCEPTION => 'Java层',
        self::CATEGORY_NATIVE_EXCEPTION => 'Native层',
        self::CATEGORY_EXTENSION_EXCEPTION => 'Extension层',
        self::CATEGORY_CRASH_EXCEPTION => 'OS层',
        self::CATEGORY_OOM_EXCEPTION => 'OOM层',
        //        self::CATEGORY_OTHER_EXCEPTION => '其它异常'
        self::CATEGORY_CSHARP_EXCEPTION => 'C#层',
        self::CATEGORY_LUA_EXCEPTION => 'Lua层',
        self::CATEGORY_JS_EXCEPTION => 'JS层',
        self::CATEGORY_ANR_EXCEPTION => 'ANR层',
    ];
    //可解析符号表异常类型
    public static $analysisCategory = [
        self::CATEGORY_NATIVE_EXCEPTION,
        self::CATEGORY_CRASH_EXCEPTION,
        self::CATEGORY_OOM_EXCEPTION,
    ];

    public static $categoryIndex = [
        self::CATEGORY_DEFAULT,
        self::CATEGORY_JAVA_EXCEPTION,
        self::CATEGORY_NATIVE_EXCEPTION,
        self::CATEGORY_EXTENSION_EXCEPTION,
        self::CATEGORY_CRASH_EXCEPTION,
        self::CATEGORY_OOM_EXCEPTION,
        self::CATEGORY_CSHARP_EXCEPTION,
        self::CATEGORY_LUA_EXCEPTION,
        self::CATEGORY_JS_EXCEPTION,
        self::CATEGORY_ANR_EXCEPTION,
    ];

    // 从mysql获取需要剔除的records集合
    public static function getRecords($developerAppId, $exceptionType)
    {
        return Record::query()
            ->where('developer_app_id', $developerAppId)
            ->where('type', $exceptionType)
            ->where('is_add_white_list', 1)
            ->get()
            ->toArray();
    }

    // 从mysql获取需要剔除的records集合
    public static function getRecordsSql($developerAppId, $exceptionType)
    {
        return StarRocksDB::toSql(DB::table('exception_record_new')
            ->selectRaw("UPPER(exception_block_id) as exception_block_id")
            ->where('developer_app_id', $developerAppId)
            ->where('type', $exceptionType)
            ->where('is_add_white_list', 1));
    }

    // 获取异常记录处理状态
    public static function getHandleStatusSql($developerAppId, $exceptionType, $handlerStatus)
    {
        $opt = 'not in';
        $all = [Record::EXCEPTION_PROCEED_HANDLE, Record::EXCEPTION_FINISH_HANDLE, Record::EXCEPTION_NOT_HANDLE, Record::EXCEPTION_REPEATED_HANDLE];
        // 如果 handlerStatus 等于 all 则，获取所有异常记录
        if (count($all) == count($handlerStatus)) {
            $handlerStatus = [-1];
        }
        // 判断 handlerStatus 里面是否包含 Record::EXCEPTION_NOT_HANDLE, 如果包含则获取 handlerStatus 中不包含 $all 的值
        if (in_array(Record::EXCEPTION_NOT_HANDLE, $handlerStatus)) {
            $handlerStatus = array_diff($all, $handlerStatus);
        } else {
            $opt = 'in';
        }
        if (empty($handlerStatus)) {
            $handlerStatus = [-1];
        }
        return [$opt, StarRocksDB::toSql(DB::table('exception_record_new')
            ->selectRaw("UPPER(exception_block_id) as exception_block_id")
            ->where('developer_app_id', $developerAppId)
            ->where('type', $exceptionType)
            ->whereIn('status', $handlerStatus))];
    }

    // 从mysql获取需要剔除的exception_block_id集合
    public static function getExceptionBlockIds($developerAppId, $exceptionType)
    {
        return Record::query()
            ->where('developer_app_id', $developerAppId)
            ->where('type', $exceptionType)
            ->where('is_add_white_list', 1)
            ->get()
            ->pluck('exception_block_id')
            ->toArray();
    }

    // 获取 异常记录 白名单筛选 sql
    public static function getExceptionBlockIdWhiteListSql(int $extraAppId, int $type)
    {
        $whereNotInExceptionBlockId = '';
        $exceptionBlockIdWhiteList = self::getExceptionBlockIds($extraAppId, $type);
        if (!empty($exceptionBlockIdWhiteList)) {
            $exceptionBlockIdWhiteList = array_map(function ($element) {
                return "'" . $element . "'";
            }, $exceptionBlockIdWhiteList);
            $exceptionBlockIdWhiteList = implode(',', $exceptionBlockIdWhiteList);
            $whereNotInExceptionBlockId = "AND `exception_block_id` NOT IN ({$exceptionBlockIdWhiteList})";
        }
        return $whereNotInExceptionBlockId;
    }

    public static function getArrayByField($field, $developerAppId, $exceptionType)
    {
        $records = Record::getRecords($developerAppId, $exceptionType);
        if ($field == 'os_type') { // ck中os_type为字符串存储
            $data = array_map('strval', array_unique(array_column($records, $field)));
        } else {
            $data = array_unique(array_column($records, $field));
        }

        // 对每个元素进行转义
        $data = array_map(function ($item) {
            return addslashes($item);
        }, $data);
        return $data;
    }

    public static function getSqlCondition($developerAppId, $type)
    {
        $categories = Record::getArrayByField('category', $developerAppId, $type);
        $osTypes = Record::getArrayByField('os_type', $developerAppId, $type);
        $names = Record::getArrayByField('name', $developerAppId, $type);
        $explains = Record::getArrayByField('explain', $developerAppId, $type);
        $placeholders = [];
        $bindings = [];
        if (!empty($categories)) {
            $placeholders[] = 'NOT(type IN (' . rtrim(str_repeat('?,', count($categories)), ',') . ')';
            $bindings = array_merge($bindings, $categories);
        }
        if (!empty($osTypes)) {
            $placeholders[] = 'os_type IN (' . rtrim(str_repeat('?,', count($osTypes)), ',') . ')';
            $bindings = array_merge($bindings, $osTypes);
        }
        if (!empty($names)) {
            $placeholders[] = 'subject_name IN (' . rtrim(str_repeat('?,', count($names)), ',') . ')';
            $bindings = array_merge($bindings, $names);
        }
        if (!empty($explains)) {
            $placeholders[] = 'explain IN (' . rtrim(str_repeat('?,', count($explains)), ',') . '))';
            $bindings = array_merge($bindings, $explains);
        }
        return array(
            'placeholders' => implode(' AND ', $placeholders),
            'bindings' => $bindings,
        );
    }

    /**
     * 创建异常记录
     *
     * @param array $input
     * @return int
     */
    public static function createRecord(array $input)
    {
        $record = Record::query()
            ->where('developer_app_id', $input['developer_app_id'])
            ->where('exception_block_id', $input['exception_block_id'])
            ->first();
        if ($record) {
            $record_id = $record->record_id;
            $record->type = BaseService::TYPE[$input['event_name']] ?? 0;
            $record->handler_keyword_id = $input['handler_keyword_id'] ?? $record->handler_keyword_id;
            $record->save();
        } else {
            // 入库
            $record = new Record();
            $record->developer_app_id = $input['developer_app_id'];
            $record->type = BaseService::TYPE[$input['event_name']] ?? 0;
            $record->category = $input['category'];
            $record->name = $input['name'];
            $record->explain = $input['explain'] ?? '';
            $record->os_type = $input['os_type'];
            $record->exception_block_id = $input['exception_block_id'];
            $record->handler_keyword_id = $input['handler_keyword_id'] ?? 0;
            $record->save();
            $record_id = $record->record_id;
        }
        return $record_id;
    }
}
