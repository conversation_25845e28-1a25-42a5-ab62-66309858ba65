<?php

/**
 * 全局配置控制器
 * @desc 全局配置控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/27
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Http\Validation\ConfigValidation;
use App\Model\GlobalConfig;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GlobalConfigController extends Controller
{

    /**
     * 获取配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12268
     * @return JsonResponse
     */
    public function get(): JsonResponse
    {
        //请求参数校验
        $params = ConfigValidation::build()
            ->developerAppId()->keys()->validate();
        try {
            $data = [];
            //获取配置信息
            $config = GlobalConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->value('config') ?? '{}';
            $config = json_decode($config, true);
            //遍历键值
            $keys = explode(',', $params['keys']);
            foreach ($keys as $key) {
                $data[$key] = $config[$key] ?? null;
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('获取全局配置报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加配置
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12267
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ConfigValidation::build()->developerAppId()->validate();
        try {
            //判断关键词是否存在
            $config = GlobalConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->first();
            //判断是否为空
            if (!empty($config)) {
                //格式化数据
                $config->config = json_decode($config->config, true) ?? [];
            } else {
                //创建模型
                $config = new GlobalConfig();
                $config->developer_app_id = $params['developer_app_id'];
                $config->config = [];
            }
            //保存数据
            $config->config = json_encode(array_merge($config->config, $request->all()));
            $config->save();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('添加全局配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
