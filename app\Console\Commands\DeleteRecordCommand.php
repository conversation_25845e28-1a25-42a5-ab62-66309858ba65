<?php

/**
 * 删除record表数据脚本
 * @desc 删除record表数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/03/03
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeleteRecordCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:record {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除record表数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        $table = 'record';
        $id = $this->argument('id');
        $count = DB::connection('exception')->table($table)->where('record_id', '>', $id)->count();
        $this->info('record_id: ' . $id . ' count: ' . $count . ' time: ' . date('Y-m-d H:i:s'));
        $this->info('start');
        $num = 0;
        while ($id > 0) {
            if ($count > 100000) {
                break;
            }
            if ($count > 0) {
                DB::connection('exception')->table($table)->where('record_id', '>', $id)->delete();
            }
            $id = $id - 50000;
            $count = DB::connection('exception')->table($table)->where('record_id', '>', $id)->count();
            $this->info('record_id: ' . $id . ' count: ' . $count . ' time: ' . date('Y-m-d H:i:s') . ' num: ' . $num);
            sleep(10);
            if ($num > 50) {
                break;
            }
            $num++;
        }
        $this->info('end time: ' . date('Y-m-d H:i:s'));
    }
}
