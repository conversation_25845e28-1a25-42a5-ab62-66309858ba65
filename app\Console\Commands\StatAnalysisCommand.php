<?php

/**
 * 统计分析数据脚本
 * @desc 统计分析数据脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\StarRocks\ExceptionAnalysis;
use App\Model\StarRocks\ExceptionStreamAll;
use App\Service\SyncFileToStarRocksService;
use App\Service\WriteCsvFileService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class StatAnalysisCommand extends Command
{
    /**
     * 统计时间间隔
     *
     * @var int
     */
    public const TIME = 1;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stat:analysis {startTime? : 开始时间} {endTime? : 结束时间}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '统计分析数据脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        // 设置脚本执行时间
        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        try {
            //1、获取开始和结束时间
            $startTime = $this->argument('startTime');
            // 判断 endTime 是否为空
            if (empty($startTime)) {
                // 从数据库中获取最新时间
                $max = ExceptionStreamAll::query()
                    ->selectRaw('max(stream_time) as max_stream_time')
                    ->whereIn('event_name', ['exception_crash', 'exception_anr'])
                    ->firstFromSR();
                $startTime = $max['max_stream_time'] ?? time();
                $startTime = $startTime - ((self::TIME * 60) + 600); //时间戳
            }
            $endTime = $this->argument('endTime') ?? time();
            //2、获取数据
            $newData = ExceptionStreamAll::query()
                ->selectRaw('extra_app_id, exception_block_id, event_name, exception_merge_id, stream_date, app_version, inner_version, manufacturer, device_model, os_version, basic_info_json, extra, is_emulator, exception_image')
                ->where('stream_time', '>=', $startTime)
                ->where('stream_time', '<=', $endTime)
                ->where('stream_date', '>=', date('Y-m-d', $startTime))
                ->where('stream_date', '<=', date('Y-m-d', $endTime))
                ->whereIn('event_name', ['exception_crash', 'exception_anr'])
                ->getFromSR();
            // 判断是否有数据
            if (empty($newData)) {
                return;
            }
            //3、处理数据
            foreach ($newData as &$item) {
                $item['basic_info_json'] = json_decode($item['basic_info_json'], true);
                $item['extra'] = json_decode($item['extra'], true);
                // 判断是否数组
                if (is_array($item['extra'])) {
                    $item['extra'] = array_column($item['extra'], null, 'key');
                } else {
                    $item['extra'] = [];
                }
                // 判断是否数组
                if (!is_array($item['basic_info_json'])) {
                    $item['basic_info_json'] = [];
                }
                $item['use_duration'] = $item['basic_info_json']['use_duration'] ?? 0;
                $item['role_level'] = $item['extra']['roleLevel']['value'] ?? 0;
                $item['daily_active'] = $item['extra']['dailyActive']['value'] ?? 0;
                $item['power'] = $item['extra']['power']['value'] ?? 0;
            }
            //字段
            $columns = [
                'extra_app_id',
                'exception_block_id',
                'event_name',
                'exception_merge_id',
                'stream_date',
                'app_version',
                'inner_version',
                'manufacturer',
                'device_model',
                'os_version',
                'use_duration',
                'role_level',
                'daily_active',
                'power',
                'is_emulator',
                'exception_image',
            ];
            //4、写入csv
            $path = (new WriteCsvFileService("app/exception-analysis/" . Str::random() . ".csv", $columns, $newData))->write();
            //同步到starRocks
            (new SyncFileToStarRocksService($path, $columns, ExceptionAnalysis::TABLE_NAME))->sync();
            //5、打印日志
            Log::info("执行统计分析数据脚本完成, startTime: " . date('Y-m-d H:i:s', $startTime) . ", endTime: " . date('Y-m-d H:i:s', $endTime));
        } catch (\Exception $e) {
            Log::error("执行统计分析数据脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }
}
