<?php declare(strict_types=1);

namespace App\Components\Redis;

use App\Components\Helper\CommonHelper;
use Exception;

/**
 * Class RedisHandler
 * @package App\Components\Redis
 * author : z<PERSON><PERSON><PERSON><PERSON>@gmail.com
 * datetime: 2023/06/27 10:48
 * memo : null
 */
class RedisHandler
{

    private static $redis;

    private static $instance;

    const INIT = [
        'ttl' => 1,//單位：秒
        'hotDataTimeout' => 1,//熱數據超時時間
        'coldDataTimeout' => 1//冷數據超時時間
    ];

    public static function redisInstance(array $option = [])
    {
        if (!self::$instance) {
            self::$instance = new self($option);
        }
        return self::$instance;
    }

    public function __construct(array $option = [])
    {
        if (!self::$redis) $this->connect($option);
    }

    public static function __callStatic($method, $args)
    {
        self::redisInstance();
        if (method_exists(self::redisInstance(), $method)) {
            return call_user_func_array(array(self::$instance, $method), $args);
        } elseif (method_exists(self::$redis, $method)) {
            return call_user_func_array(array(self::$redis, $method), $args);
        } else {
            throw new Exception("App\Components\Redis::{$method} not exist");
        }
    }

    public function __call($method, $args)
    {
        if (method_exists(self::redisInstance(), $method)) {
            return call_user_func_array(array(self::$instance, $method), $args);
        } elseif (method_exists(self::$redis, $method)) {
            return call_user_func_array(array(self::$redis, $method), $args);
        } else {
            throw new Exception("App\Components\Redis::{$method} not exist");
        }
    }

    private function connect(array $option)
    {
        if (!extension_loaded('redis')) {
            throw new \BadFunctionCallException('not support : redis');
        }
        $config = [
            'host' => env('REDIS_HOST'),
            'port' => env('REDIS_PORT'),
            'password' => env('REDIS_PASSWORD') ?? '',
            'timeout' => env('REDIS_TIMEOUT') ?? 15,
            'persistent' => env('REDIS_PERSISTENT') ?? '',
        ];
        if (!empty($option)) $config = array_merge($config, $option);
        $connectFunc = $config['persistent'] ? 'pconnect' : 'connect';
        self::$redis = new \Redis;
        if (!self::$redis->$connectFunc($config['host'], intval($config['port']), floatval($config['timeout']))) {
            throw new Exception('App\Components\Redis connect failed');
        }
        if (!empty($config['password']) && !self::$redis->auth($config['password'])) {
            throw new Exception('App\Components\Redis auth failed');
        }
    }

    /**
     * @param callable $func
     * @param string $redisKey
     * @param int $ttl -1/永不過期
     * @return mixed
     * author : <EMAIL>
     * memo : null
     */
    public static function autoGet(string $redisKey, callable $func, int $ttl = self::INIT['ttl'])
    {
        $Redis = self::redisInstance();
        $value = $Redis->get($redisKey);
        if ($value === false) {
            $value = $func();
            $Redis->set($redisKey, CommonHelper::prettyJsonEncode($value), ($ttl === -1 ? null : $ttl));//null表示永不過期，詳情參見set();
            return $value;
        }
        return json_decode($value, true);
    }

    /**
     * @param callable $func
     * @param string $redisKey
     * @param string $hashField
     * @param int $ttl -1/永不過期
     * @return mixed
     * author : <EMAIL>
     * memo : null
     */
    public static function autoHashGet(string $redisKey, string $hashField, callable $func, int $ttl = self::INIT['ttl'])
    {
        $Redis = self::redisInstance();
        $value = $Redis->hGet($redisKey, $hashField);
        if ($value === false) {
            $result = $func();
            $value = CommonHelper::prettyJsonEncode($result);
            $Redis->hSet($redisKey, $hashField, $value);
        }
        //refresh ttl[START]
        if ($ttl === -1) {
            $Redis->persist($redisKey);
        } else {
            $Redis->expire($redisKey, $ttl);
        }
        //refresh ttl[END]
        return json_decode($value, true);
    }

    public static function mutex(string $mutexName, callable $func, int $lockedTime = 3/* , int &$retry = 0 */, bool $returnCacheResult = true)
    {
        try {
            $owner = uniqid('', true);
            $Redis = self::redisInstance();
            $lockedRedisKey = RedisKeyEnum::STRING['STRING:MutexName:'] . $mutexName;
            $resultRedisKey = RedisKeyEnum::STRING['STRING:MutexResult:'] . $mutexName;
            if ($Redis->set($lockedRedisKey, $owner, ['EX' => $lockedTime, 'NX']) === true) {
                $result = $func();
                if ($returnCacheResult) $Redis->lPush($resultRedisKey, CommonHelper::prettyJsonEncode($result)); // 共享#並發邏輯#返回值
            } elseif ($returnCacheResult) {
                if ($result/* 返回:「含:1鍵名，2鍵值」的索引數組 */ = $Redis->brPop([$resultRedisKey], $lockedTime)) {// 阻塞，提取#並發邏輯#返回值
                    $result = json_decode($result[1], true);
                    $Redis->lPush($resultRedisKey, CommonHelper::prettyJsonEncode($result));
                }
            }
        } finally {
            if (isset($Redis, $owner, $lockedRedisKey, $resultRedisKey) && ($Redis->get($lockedRedisKey) === $owner)) {
                $Redis->expire($resultRedisKey, $lockedTime);
                $Redis->del($lockedRedisKey);
            }
        }
        return $result ?? null;
    }

    //釋放鏈接
    public static function close()
    {
        if (is_object(self::$redis)) self::$redis->close();//避免内存洩露
        self::$redis = null;
        self::$instance = null;
    }

}
