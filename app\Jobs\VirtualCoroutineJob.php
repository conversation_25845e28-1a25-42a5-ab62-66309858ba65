<?php

namespace App\Jobs;

use App\Components\Redis\RedisHandler;
use App\Components\Redis\RedisKeyEnum;
use App\Constant\RedisKey;
use App\Http\Logic\WarningLogic;
use Hyperf\Process\ProcessManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class VirtualCoroutineJob implements ShouldQueue
{

    //TODO:已發送時作冪等

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {
        $Redis = RedisHandler::redisInstance();
        $queue = RedisKeyEnum::LIST['LIST:VirtualCoroutineJob:'];
        while(true){
            try{//TODO:兼容分佈式
                $valueJson = $Redis->lPop($queue);//置頂隊列
                if($valueJson !== false) {
                    $valueArray = json_decode($valueJson, true);
                    $eachRedisName = $valueArray['mutex'];
                    RedisHandler::mutex($eachRedisName, function()use($valueArray){
                        (new $valueArray['class'])->$valueArray['function'](...$valueArray['parameter']);
                    },$valueArray['lockedTime'] ?? 10);
                };
                echo 1;
            }catch (\Throwable $e){
                \Log::error('VirtualCoroutineJob:' . $e->getMessage() . ' file: ' . $e->getFile() . ' line: ' . $e->getLine());
            }
        }
    }

}
