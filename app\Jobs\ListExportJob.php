<?php

/**
 * 列表导出队列任务
 * @desc 列表导出队列任务
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs;

use App\Components\Redis\RedisKeyEnum;
use App\Components\Scope\CrashScope;
use App\Model\BaseModel;
use App\Service\ExceptionQueryModel\ExceptionQueryModelFactory;
use Box\Spout\Common\Entity\Style\CellAlignment;
use Box\Spout\Common\Exception\InvalidArgumentException;
use Box\Spout\Common\Exception\IOException;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Writer\Exception\WriterNotOpenedException;
use Carbon\CarbonInterval;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;

class ListExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 设置最大尝试次数
     *
     * @var int
     */
    public $tries = 1;

    /**
     * 在超时之前任务可以运行的秒数
     *
     * @var int
     */
    public $timeout = 3600;

    /**
     * 获取条数
     *
     * @var int
     */
    const LIMIT = 1000;

    /**
     * 任务ID
     *
     * @var string
     */
    private $taskId;

    /**
     * 总条数
     *
     * @var int
     */
    private $total;

    /**
     * scope
     *
     * @var CrashScope
     */
    private $scope;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($taskId, $total, CrashScope $scope)
    {
        $this->taskId = $taskId;
        $this->total = $total;
        $this->scope = $scope;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws IOException
     * @throws InvalidArgumentException
     * @throws WriterNotOpenedException
     */
    public function handle()
    {
        //设置内存大小
        ini_set('memory_limit', '2048M');
        // 修改获取条数
        $perPage = self::LIMIT;
        // 设置页码
        $page = 1;
        // 获取redis的key
        $redisKey = RedisKeyEnum::getExceptionExportDataKey($this->taskId);
        // 获取记录总数
        $total = $this->total;
        // 判断是否有数据
        if (empty($total)) {
            // 设置错误信息
            Redis::setEx($redisKey, $this->timeout, json_encode(['code' => 1, 'msg' => '没有数据可以导出~']));
            // 中断
            return;
        }
        // 初始化writer
        $writer = WriterEntityFactory::createXLSXWriter();
        // 设置默认样式
        $writer->setDefaultRowStyle((new StyleBuilder())
            ->setCellAlignment(CellAlignment::CENTER)
            ->build());
        // 文件名
        $fileName = "app/export/hitbug数据-{$this->taskId}.xlsx";
        // 设置文件名
        $writer->openToFile(storage_path($fileName));
        // 设置表头
        $row = WriterEntityFactory::createRowFromArray([
            'URL',
            '设备码',
            '上报时间',
            '发生时间',
            '账号ID',
            '应用版本',
            '设备机型',
            '设备品牌',
            '系统版本',
            '网络类型',
            '应用使用时长',
            '是否ROOT',
            '是否模拟器',
            '是否后台',
            '操作系统',
            '设备ROM',
            '分辨率',
            'cpu架构',
            '设备地区',
            'IP地址',
            '网络类型',
            '应用名称',
            '应用包名',
            'Hitbug插件版本',
            '堆栈'
        ], (new StyleBuilder())
            ->setFontBold()
            ->setFontSize(14)
            ->setBackgroundColor('00B0F0')
            ->build());
        $writer->addRow($row);
        // 当前数量
        $currentCount = 0;
        // 获取query 对象
        $query = ExceptionQueryModelFactory::getExceptionQueryModel($this->scope->type)::query();
        // 循环获取数据
        while (true) {
            // 获取数据
            $list = $query
                ->tap(function ($query) {
                    $this->scope->getBuilder($query);
                })
                ->select(['extra_app_id', 'exception_block_id', 'exception_merge_id', 'event_name', 'server_dev_str', 'stream_time', 'event_time', 'account_id', 'basic_info_json', 'os_type', 'ip', 'origin_stacks_json'])
                ->orderBy('stream_time', 'desc')
                ->skip(($page - 1) * $perPage - 1)
                ->limit($perPage)
                ->getFromCK();
            // 判断是否还有数据
            if (empty($list)) {
                break;
            }
            // 写入数据
            foreach ($list as $item) {
                $type = ($item['event_name'] == 'exception_crash' ? 1 : 2);
                $url = config('app.front_url') . "/console/my-app/{$item['extra_app_id']}/app-monitor/exception-overview/crash-list/{$item['exception_block_id']}?type={$type}&exception_merge_id={$item['exception_merge_id']}";
                // 解析基础数据
                $basicInfo = json_decode($item['basic_info_json'], true) ?? [];
                // 处理数据
                $itemValue = [
                    $url,
                    $item['server_dev_str'],
                    date('Y-m-d H:i:s', $item['stream_time']),
                    date('Y-m-d H:i:s', $item['event_time']),
                    $item['account_id'],
                    $basicInfo['app_version'] ?? '',
                    $basicInfo['device_model'] ?? '',
                    $basicInfo['device_brand'] ?? '',
                    $basicInfo['os_version'] ?? '',
                    $basicInfo['network_type'] ?? '',
                    $this->convertSecondsToTime($basicInfo['use_duration'] ?? 0),
                    ($basicInfo['is_root'] ?? 0) == 1 ? '是' : '否',
                    ($basicInfo['is_emulator'] ?? 0) == 1 ? '是' : '否',
                    ($basicInfo['around_status'] ?? 0) == 1 ? '是' : '否',
                    BaseModel::OS_TYPE_TEXT[$item['os_type']] ?? '未知',
                    $basicInfo['rom_info'] ?? '',
                    $basicInfo['resolution'] ?? '',
                    $basicInfo['cpu_framework'] ?? '',
                    $basicInfo['system_region'] ?? '',
                    $item['ip'],
                    $basicInfo['network_type'] ?? '',
                    $basicInfo['app_name'] ?? '',
                    $basicInfo['sdk_package_name'] ?? '',
                    $basicInfo['crash_plugin_ver'] ?? '',
                    $this->truncateStackInfo($item['origin_stacks_json'] ?? '')
                ];

                $writer->addRow(WriterEntityFactory::createRowFromArray($itemValue));
            }
            // 设置当前数量
            $currentCount += count($list);
            // 设置导出进度
            $progress = intval(($currentCount / $total) * 100);
            // 判断进度值，进度值少于100时，才设置redis
            if ($progress < 100) {
                Redis::setEx($redisKey, $this->timeout, json_encode([
                    'code' => 0,
                    'progress' => $progress,
                    'msg' => '导出中~',
                ]));
            }
            // 判断是否还有数据
            if (count($list) < self::LIMIT) {
                break;
            }
            // 下一页
            $page++;
        }
        // 关闭writer
        $writer->close();
        // 设置完成导出
        Redis::setEx($redisKey, $this->timeout, json_encode(['code' => 0, 'progress' => 100, 'path' => $fileName, 'msg' => '导出成功~']));
    }

    /**
     * 将秒数转换为时间
     *
     * @param int $seconds
     * @return string
     */
    private function convertSecondsToTime($seconds)
    {
        $interval = CarbonInterval::seconds($seconds)->cascade();
        $hours = $interval->hours;
        $minutes = $interval->minutes;
        $seconds = $interval->seconds;
        $timeString = '';
        if ($hours > 0) {
            $timeString .= "{$hours}小时";
        }
        if ($minutes > 0) {
            $timeString .= "{$minutes}分";
        }
        if ($seconds > 0 || $timeString === '') {
            $timeString .= "{$seconds}秒";
        }
        return $timeString;
    }

    /**
     * 处理堆栈信息，确保不超过Excel单元格限制
     *
     * @param string $stackInfo
     * @return string
     */
    private function truncateStackInfo($stackInfo)
    {
        // Excel单元格最大字符限制
        $maxLength = 32767;
        if (strlen($stackInfo) <= $maxLength) {
            return $stackInfo;
        }
        // 截断并添加提示信息
        return substr($stackInfo, 0, $maxLength - 100) . "\n... (堆栈信息已截断，完整信息请查看详情页面)";
    }
}
