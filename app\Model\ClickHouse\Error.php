<?php

namespace App\Model\ClickHouse;

class Error extends UserLogDataAll
{
    protected $casts = [
        'detail_info' => 'array', 'memory_info' => 'array', 'expand' => 'array',
        'console_info' => 'array', 'page_info' => 'array', 'uuid_info' => 'array',
    ];

    static $projectId = self::PROJECT_EXCEPTION;

    public static function query()
    {
        return parent::query()->where('event_name', 'exception_error');
    }

    public const COUNT = "错误次数";
    public const RATE = "错误率";
    public const USER_COUNT = "错误人数";
    public const USER_RATE = "错误用户比例";
}
